BEGIN;

-- Create a function to update partner_owner_name in ams_assets
CREATE OR REPLACE FUNCTION update_partner_owner_name()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE ams_assets
    SET partner_owner_name = NEW.name
    WHERE partner_owner_id = NEW.id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger that calls the function on updates to uis_partners
CREATE TRIGGER update_partner_name_trigger
AFTER UPDATE OF name ON uis_partners
FOR EACH ROW
WHEN (OLD.name IS DISTINCT FROM NEW.name)
EXECUTE FUNCTION update_partner_owner_name();


-- Create a function to update ams_assets on insert into ams_linked_assets
CREATE OR REPLACE FUNCTION update_ams_assets_on_insert_linked_vehicle_tyre()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.linked_asset_type_code = 'VEHICLE_TYRE' THEN
        WITH
            cte AS (
                SELECT
                    partner_owner_id,
                    partner_owner_name,
                    partner_owner_no
                FROM
                    ams_assets aa
                WHERE
                    partner_owner_id IS NOT NULL
                    AND id = NEW.parent_asset_id
            )
        UPDATE ams_assets
        SET
            partner_owner_id = cte.partner_owner_id,
            partner_owner_name = cte.partner_owner_name,
            partner_owner_no = cte.partner_owner_no
        FROM
            cte
        WHERE
           ams_assets.id = NEW.child_asset_id ;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger that calls the function on insert into ams_linked_assets
CREATE TRIGGER insert_ams_linked_assets_vehicle_tyre_trigger
AFTER INSERT ON ams_linked_assets
FOR EACH ROW
EXECUTE FUNCTION update_ams_assets_on_insert_linked_vehicle_tyre();


-- Create a function to clear partner_owner_id and partner_owner_name on unlink
CREATE OR REPLACE FUNCTION clear_partner_info_on_unlink()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.linked_asset_type_code = 'VEHICLE_TYRE' AND OLD.unlinked_datetime IS NULL AND NEW.unlinked_datetime IS NOT NULL THEN
        UPDATE ams_assets
        SET partner_owner_id = NULL,
            partner_owner_name = NULL,
            partner_owner_no = NULL
        WHERE id = NEW.child_asset_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger that calls the function on update of unlinked_datetime in ams_linked_assets
CREATE TRIGGER clear_partner_info_trigger
AFTER UPDATE OF unlinked_datetime ON ams_linked_assets
FOR EACH ROW
WHEN (OLD.unlinked_datetime IS DISTINCT FROM NEW.unlinked_datetime)
EXECUTE FUNCTION clear_partner_info_on_unlink();

COMMIT;