BEGIN;

CREATE TABLE
    IF NOT EXISTS "uis_CLIENT_PACKAGES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "uis_CLIENT_PACKAGES" (code, "label", description)
VALUES
    ('FREE', 'Free', '-'),
    ('LITE', 'Lite', '-'),
    ('STANDARD', 'Standard', '-'),
    ('ADVANCED', 'Advanced', '-') ON CONFLICT (code) DO NOTHING;

CREATE TABLE
    IF NOT EXISTS "uis_OPTIMAX_PACKAGES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "uis_OPTIMAX_PACKAGES" (code, "label", description)
VALUES
    ('FLEET_OPTIMAX', 'Fleet Optimax', '-'),
    ('TYRE_OPTIMAX', 'Tyre Optimax', '-'),
    ('TRACK_OPTIMAX', 'Track Optimax', '-'),
    ('WORKSHOP_OPTIMAX', 'Workshop Optimax', '-') ON CONFLICT (code) DO NOTHING;

ALTER TABLE uis_clients
ADD COLUMN IF NOT EXISTS optimax_package_codes VARCHAR(20)[] DEFAULT ARRAY['FLEET_OPTIMAX', 'TYRE_OPTIMAX'];

ALTER TABLE uis_clients
ADD COLUMN IF NOT EXISTS package_code VARCHAR(20) DEFAULT 'FREE';

ALTER TABLE uis_clients
ADD CONSTRAINT fk_uis_clients_package foreign key (package_code) references "uis_CLIENT_PACKAGES" (code);

COMMIT;