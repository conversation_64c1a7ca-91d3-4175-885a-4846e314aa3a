-- Drop foreign key constraints
ALTER TABLE "ams_custom_asset_categories" 
DROP CONSTRAINT IF EXISTS fk_ams_custom_asset_categories_status;

ALTER TABLE "ams_custom_asset_sub_categories" 
DROP CONSTRAINT IF EXISTS fk_ams_custom_asset_sub_categories_status;

-- Drop the status_code columns from both tables
ALTER TABLE "ams_custom_asset_categories" 
DROP COLUMN IF EXISTS "status_code";

ALTER TABLE "ams_custom_asset_sub_categories" 
DROP COLUMN IF EXISTS "status_code";

-- Drop the status tables
DROP TABLE IF EXISTS "ams_CUSTOM_ASSET_CATEGORY_STATUSES";
DROP TABLE IF EXISTS "ams_CUSTOM_ASSET_SUB_CATEGORY_STATUSES";
