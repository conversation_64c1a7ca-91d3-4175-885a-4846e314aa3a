UPDATE "ins_ALERT_PARAMETERS" SET 
label = 'Counted Engine Hours', 
description = 'Engine Operating Hours since telematics monitoring was activated' 
WHERE label  = 'Engine Motorhours';

UPDATE "ins_ALERT_PARAMETERS" SET 
label = 'Total Engine Hours', 
description = 'Total Engine Operating Hours Since the Trip was reset' 
WHERE label  = 'Trip Engine Motorhours';

UPDATE "ins_ALERT_PARAMETERS" SET 
label = 'Counted Mileage (KM)', 
description = 'Total distance travelled in KM since telematics monitoring was activated' 
WHERE label  = 'Tracker Counted Mileage';

UPDATE "ins_ALERT_PARAMETERS" SET 
description = 'Vehicle Starter Battery Voltage level' 
WHERE label  = 'General Battery Voltage';