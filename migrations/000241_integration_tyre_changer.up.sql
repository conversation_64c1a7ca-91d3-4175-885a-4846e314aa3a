BEGIN;

INSERT INTO
    "ins_INTEGRATION_TARGET" (
        code,
        "label",
        description,
        auth_credential_json_template
    )
VALUES
    (
        'TASMOTA',
        'Tasmota',
        'Tasmota',
        '{}'
    ) ON CONFLICT (code) DO NOTHING;

INSERT INTO
    "ins_INTEGRATION_TARGET_TYPE" (
        code,
        label,
        description,
        integration_type_code,
        integration_target_code,
        identifier_json_template
    )
VALUES
    (
        'TASMOTA_GATEWAY',
        'Tasmota Gateway',
        'Tasmota Gateway',
        'IOT_GATEWAY',
        'TASMOTA',
        '{"ident":"string"}'
    ) ON CONFLICT (code) DO NOTHING;

INSERT INTO
    ins_integration_account (
        id,
        target_code,
        auth_credential_json,
        status_code,
        client_id,
        created_at,
        updated_at,
        deleted_at,
        updated_by,
        created_by,
        "name"
    )
VALUES
    (
        'ina_ea1ab712-df08-4c56-aa88-d75ce5cc32df',
        'TASMO<PERSON>',
        '',
        'ACTIVE',
        'GENERAL',
        NOW(),
        NOW(),
        NULL,
        'admin',
        'admin',
        'Tasmota Gateway'
    ) ON CONFLICT (id) DO NOTHING;

COMMIT;