BEGIN;

ALTER TABLE ams_asset_tyres
ADD COLUMN IF NOT EXISTS last_stock_date TIMESTAMPTZ;

UPDATE ams_asset_tyres aat
SET last_stock_date = aa.updated_at
FROM ams_assets aa 
WHERE aa.id = aat.asset_id
AND aat.last_stock_date is null
AND aa.asset_status_code = 'IN_STOCK';
    
UPDATE ams_asset_tyres aat
SET last_stock_date = aa.created_at
FROM ams_assets aa 
WHERE aa.id = aat.asset_id
AND aat.last_stock_date is null
AND aa.asset_status_code = 'NEW_STOCK';

COMMIT;