WITH cte AS (
  SELECT
    id,
    CASE
    WHEN av.client_id = 'GENERAL' THEN
    'VHS-' || 
    'GEN-' ||
    row_number() over (partition by client_id order by created_at)
    ELSE
    'VHS-' || 
    row_number() over (partition by client_id order by created_at)
    END
    AS new_vehicle_number,
    vehicle_number
  FROM
    ams_vehicles av
  WHERE
   av.deleted_at IS NULL
)
UPDATE ams_vehicles AS av SET vehicle_number = cte.new_vehicle_number FROM cte WHERE av.id = cte.id;

CREATE OR REPLACE FUNCTION trigger_set_new_vehicle_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    CASE
    WHEN av.client_id = 'GENERAL' THEN
    'VHS-' || 
    'GEN-' ||
    row_number() over (partition by client_id order by created_at)
    ELSE
    'VHS-' || 
    row_number() over (partition by client_id order by created_at)
    END
    AS new_vehicle_number,
    vehicle_number
  FROM
    ams_vehicles av
  WHERE
   av.deleted_at IS NULL 
   AND client_id = NEW.client_id
)
UPDATE ams_vehicles AS av SET vehicle_number = cte.new_vehicle_number FROM cte WHERE av.id = cte.id AND (cte.vehicle_number IS NULL OR cte.vehicle_number = '');
RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS set_number_after_insert_vehicle ON ams_vehicles;

CREATE TRIGGER set_number_after_insert_vehicle
  AFTER INSERT
  ON ams_vehicles
  FOR EACH ROW
  EXECUTE PROCEDURE trigger_set_new_vehicle_number();
 
--
  
 WITH cte AS (
  SELECT
    id,
    CASE
    WHEN at.client_id = 'GENERAL' THEN
    'TYR-' || 
    'GEN-' ||
    row_number() over (partition by client_id order by created_at)
    ELSE
    'TYR-' || 
    row_number() over (partition by client_id order by created_at)
    END
    AS new_tyre_number,
    tyre_number
  FROM
    ams_tyres at
  WHERE
   at.deleted_at IS NULL
)
UPDATE ams_tyres AS at SET tyre_number = cte.new_tyre_number FROM cte WHERE at.id = cte.id;

CREATE OR REPLACE FUNCTION trigger_set_new_tyre_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    CASE
    WHEN at.client_id = 'GENERAL' THEN
    'TYR-' || 
    'GEN-' ||
    row_number() over (partition by client_id order by created_at)
    ELSE
    'TYR-' || 
    row_number() over (partition by client_id order by created_at)
    END
    AS new_tyre_number,
    tyre_number
  FROM
    ams_tyres at
  WHERE
   at.deleted_at IS NULL 
   AND client_id = NEW.client_id
)
UPDATE ams_tyres AS at SET tyre_number = cte.new_tyre_number FROM cte WHERE at.id = cte.id AND (cte.tyre_number IS NULL OR cte.tyre_number = '');
RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS set_number_after_insert_tyre ON ams_tyres;

CREATE TRIGGER set_number_after_insert_tyre
  AFTER INSERT
  ON ams_tyres
  FOR EACH ROW
  EXECUTE PROCEDURE trigger_set_new_tyre_number();