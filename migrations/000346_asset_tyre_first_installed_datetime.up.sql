BEGIN;

ALTER TABLE ams_asset_tyres
ADD COLUMN IF NOT EXISTS first_installed_datetime TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_inspected_at TIMESTAMPTZ;

WITH cte AS (
    SELECT 
        child_asset_id,
        MIN(linked_datetime) AS first_installed_datetime
    FROM
        ams_linked_assets
    WHERE
        linked_asset_type_code = 'VEHICLE_TYRE'
    GROUP BY
        child_asset_id
) UPDATE ams_asset_tyres aat
SET
    first_installed_datetime = cte.first_installed_datetime
FROM
    cte
WHERE
    cte.child_asset_id = aat.asset_id;

UPDATE ams_asset_tyres aat
SET last_inspected_at = greatest(aat.average_rtd_last_updated_at, aat.pressure_last_updated_at, aat.temperature_last_updated_at)
WHERE
    aat.last_inspected_at IS NULL;

COMMIT;