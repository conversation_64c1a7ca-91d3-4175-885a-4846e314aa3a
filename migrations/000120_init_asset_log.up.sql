BEGIN;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE OR REPLACE FUNCTION ams_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
  _new jsonb;
  _old jsonb;
  _k text;
  _id varchar(44);
BEGIN
  _id := concat('ams_log_', uuid_generate_v4());
  _new := to_jsonb(NEW);
  _old := to_jsonb(OLD);

  FOR _k IN SELECT * FROM jsonb_object_keys(_new)
  LOOP
    IF _new[_k] = _old[_k]
      THEN _new = _new - _k; _old = _old - _k;
    END IF;
  END LOOP;

  INSERT INTO ams_logs(id, table_name, client_id, created_by, created_at, previous_value,new_value)
    VALUES(_id, TG_TABLE_NAME, NEW.client_id , NEW.updated_by, NEW.updated_at, _old,_new);
  
  IF TG_TABLE_NAME = 'ams_assets' 
    THEN  INSERT INTO ams_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.id);
  END IF;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;


CREATE TABLE
    IF NOT EXISTS ams_logs (
        id VARCHAR(44) PRIMARY KEY,
        table_name VARCHAR(255) NOT NULL,
        previous_value JSONB,
        new_value JSONB,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        created_by VARCHAR(40)
    );
  
CREATE TABLE
    IF NOT EXISTS ams_log_references (
        id VARCHAR(48) PRIMARY KEY DEFAULT concat('ams_log_ref_', uuid_generate_v4()),
        log_id VARCHAR(44),
        "reference_code" VARCHAR(20) NOT NULL,
        "reference_id" VARCHAR(40) NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        created_by VARCHAR(40)
    );
  
CREATE TRIGGER update_trigger_ams_assets
AFTER INSERT OR UPDATE ON ams_assets
FOR EACH ROW
EXECUTE FUNCTION ams_trigger_function();

COMMIT;