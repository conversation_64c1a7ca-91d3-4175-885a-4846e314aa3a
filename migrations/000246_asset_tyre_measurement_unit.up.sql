BEGIN;


CREATE TABLE IF NOT EXISTS
    "ams_ASSET_TYRE_METER_CALCULATIONS" (
        "code" varchar(20) NOT NULL PRIMARY KEY ,
        "label" VARCHAR(20) NOT NULL,
        "description" varchar(255)
    );

INSERT INTO "ams_ASSET_TYRE_METER_CALCULATIONS" (code,label) 
VALUES 
    ('KM','KM'),
    ('HM','HM') ON CONFLICT (code) DO NOTHING;


ALTER TABLE ams_asset_tyres
ADD COLUMN IF NOT EXISTS "meter_calculation_code" VARCHAR(20) 
REFERENCES "ams_ASSET_TYRE_METER_CALCULATIONS" (code);

COMMIT;