BEGIN;

CREATE TABLE IF NOT EXISTS
    "tks_TASKS_STATUSES" (
        "code" varchar(20) NOT NULL PRIMARY KEY ,
        "label" VARCHAR(20) NOT NULL,
        "description" varchar(255)
    );

INSERT INTO "tks_TASKS_STATUSES" (code,label) 
VALUES 
    ('OPEN','Open'),
    ('COMPLETED','Completed') ON CONFLICT (code) DO NOTHING;


CREATE TABLE
    IF NOT EXISTS "tks_tasks" (
        id VARCHAR(40) PRIMARY KEY,
        task_number VARCHAR(31),
        "subject" VARCHAR(255),
        "description" VARCHAR(255),
        status_code VARCHAR(40) REFERENCES "tks_TASKS_STATUSES" (code),
        schedule_datetime TIMESTAMPTZ,
        asset_id VARCHAR(40),
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ,
        updated_at TIMESTAMPTZ,
        deleted_at TIMESTAMPTZ,
        updated_by VA<PERSON>HAR(40),
        created_by <PERSON><PERSON>HA<PERSON>(40) NOT NULL
    );


CREATE OR REPLACE FUNCTION trigger_set_new_task_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    'TD-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    lpad((row_number() over (partition BY client_id, created_by,  date_trunc('day',timezone('-7', created_at)) order by created_at))::text,4,'0') AS new_number,
    task_number
  FROM
    tks_tasks t
  WHERE
   t.deleted_at IS NULL 
   AND client_id = NEW.client_id
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE tks_tasks AS t SET task_number = cte.new_number FROM cte WHERE t.id = cte.id AND (cte.task_number IS NULL OR cte.task_number = '');
RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS trigger_set_new_task_number ON tks_tasks;

CREATE TRIGGER set_number_after_insert_task
  AFTER INSERT
  ON tks_tasks	
  FOR EACH ROW
  EXECUTE PROCEDURE trigger_set_new_task_number();

CREATE TABLE IF NOT EXISTS
    "ins_ALERT_TICKET_TYPES" (
        "code" varchar(20) NOT NULL PRIMARY KEY ,
        "label" VARCHAR(20) NOT NULL,
        "description" varchar(255)
    );

INSERT INTO "ins_ALERT_TICKET_TYPES" (code,label) 
VALUES 
    ('TASK','Task'),
    ('TICKET','Ticket') ON CONFLICT (code) DO NOTHING;

ALTER TABLE ins_alert_configs_v2 
ADD COLUMN IF NOT EXISTS ticket_type VARCHAR(20) REFERENCES "ins_ALERT_TICKET_TYPES"(code) DEFAULT 'TICKET';

ALTER TABLE ins_alerts_v2 
ADD COLUMN IF NOT EXISTS ticket_type VARCHAR(20) REFERENCES "ins_ALERT_TICKET_TYPES"(code) DEFAULT 'TICKET';

INSERT INTO
    "nts_NOTIFICATION_SOURCE" (code, label, description, created_at, updated_at)
VALUES
    (
        'TASK',
        'Task',
        'Task',
        NOW (),
        NOW ()
    );

INSERT INTO
    "nts_NOTIFICATION_REFERENCES" (code, "label", description)
VALUES
    ('TASK', 'Task', '-')
    ON CONFLICT (code) DO NOTHING;

COMMIT;