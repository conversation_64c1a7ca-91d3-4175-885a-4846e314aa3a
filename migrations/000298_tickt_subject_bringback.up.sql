BEGIN;

ALTER TABLE tks_tickets
ALTER COLUMN subject TYPE VARCHAR(255);

ALTER TABLE ins_alert_configs_v2 ADD COLUMN IF NOT EXISTS ticket_subject VARCHAR(255);

WITH
    cte AS (
        SELECT
            code,
            "label"
        FROM
            "tks_TICKET_CATEGORIES" ttc
        WHERE
            code != 'Others'
    )
UPDATE tks_tickets
SET
    subject = cte.LABEL
FROM
    cte
WHERE
    tks_tickets.ticket_category_code = cte.code
    AND tks_tickets.subject = '';

WITH
    cte AS (
        SELECT
            code,
            "label"
        FROM
            "tks_TICKET_CATEGORIES" ttc
        WHERE
            code != 'Others'
    )
UPDATE ins_alert_configs_v2 
SET
    ticket_subject = cte.LABEL
FROM
    cte
WHERE
    ins_alert_configs_v2.ticket_category_code = cte.code
    AND ins_alert_configs_v2.ticket_subject IS NULL;

COMMIT;