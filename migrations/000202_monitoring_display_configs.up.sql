BEGIN;

CREATE TABLE IF NOT EXISTS ins_monitoring_display_configs (
    id VARCHAR(40) PRIMARY KEY,
    asset_id VARCHAR(40) NOT NULL,
    hidden_fields VARCHAR(510)[],
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMP,
    updated_by VA<PERSON>HAR(40) NOT NULL,
    created_by VA<PERSON>HAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS idx_ins_monitoring_display_configs_asset_id ON ins_monitoring_display_configs (asset_id);

COMMIT;