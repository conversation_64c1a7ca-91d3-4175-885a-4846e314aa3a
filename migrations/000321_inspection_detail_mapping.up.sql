CREATE TABLE IF NOT EXISTS "ams_ASSET_INPECTION_DISPLAY_FORMATION_CODES" (
    code VARCHAR(40) PRIMARY KEY,
    label VARCHAR(40) NOT NULL,
    description VARCHAR(50) NOT NULL
);

INSERT INTO "ams_ASSET_INPECTION_DISPLAY_FORMATION_CODES" VALUES
('DIGISPECT_INSPECTION_FLOW_SCENARIO_1','Digispect Inspection Flow Scenario 1','-'),
('DIGISPECT_INSPECTION_FLOW_SCENARIO_2','Digispect Inspection Flow Scenario 2','-'),
('DIGISPECT_INSPECTION_FLOW_SCENARIO_3','Digispect Inspection Flow Scenario 3','-'),
('DIGISPECT_INSPECTION_FLOW_SCENARIO_4','Digispect Inspection Flow Scenario 4','-'),
('DIGISPECT_INSPECTION_FLOW_SCENARIO_5','Digispect Inspection Flow Scenario 5','-'),
('DIGISPECT_INSPECTION_FLOW_SCENARIO_6','Digispect Inspection Flow Scenario 6','-'),
('DIGISPECT_INSPECTION_FLOW_SCENARIO_7','Digispect Inspection Flow Scenario 7','-'),
('WORKSHOP_OPTIMAX','Workshop Optimax','-'),
('TYRE_OPTIMAX','Tyre Optimax','-') ON CONFLICT (code) DO NOTHING;


ALTER TABLE "ams_asset_inspections"
    ADD COLUMN IF NOT EXISTS "display_formation_code" VARCHAR(40) 
    REFERENCES "ams_ASSET_INPECTION_DISPLAY_FORMATION_CODES"(code) DEFAULT NULL;

