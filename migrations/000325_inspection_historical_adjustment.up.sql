ALTER TABLE ams_asset_inspection_vehicle 
ADD COLUMN IF NOT EXISTS custom_serial_number VARCHAR(255);

UPDATE ams_assets SET partner_owner_name = NULL WHERE partner_owner_id IS NULL AND deleted_at IS NULL;

WITH cte AS (
    SELECT
	aaiv.id,
	CASE
		WHEN aa.reference_number = '' THEN NULL
		ELSE aa.reference_number
	END,
	CASE
		WHEN aa.serial_number = '' THEN NULL
		ELSE aa.serial_number
	END,
	aa.partner_owner_name,
	aam.asset_model_name ,
	ab.brand_name
FROM
	ams_asset_inspection_vehicle aaiv
JOIN ams_assets aa ON
	aa.id = aaiv.asset_vehicle_id
LEFT JOIN ams_asset_models aam ON
	aam.id = aa.model_id
LEFT JOIN ams_brands ab ON
	ab.id = aa.brand_id 
)
UPDATE ams_asset_inspection_vehicle
SET custom_serial_number = cte.serial_number,
    partner_owner_name = cte.partner_owner_name,
    custom_reference_number = cte.reference_number,
    custom_brand_name = cte.brand_name,
    custom_model_name = cte.asset_model_name
FROM cte
WHERE ams_asset_inspection_vehicle.id = cte.id;



WITH cte AS (
SELECT
	aait.id,
	CASE
		WHEN aa.serial_number = '' THEN NULL
		ELSE aa.serial_number
	END,
	CASE
		WHEN aa.rfid = '' THEN NULL
		ELSE aa.rfid 
	END,
	ab.brand_name ,
	CONCAT(at2.section_width,
	' ',
	at2.construction,
	' ',
	at2.rim_diameter) tyre_size
FROM
	ams_asset_inspection_tyre aait
JOIN ams_assets aa ON
	aa.id = aait.asset_tyre_id
JOIN ams_asset_tyres aat ON
	aat.asset_id = aa.id
JOIN ams_tyres at2 ON
	at2.id = aat.tyre_id
LEFT JOIN ams_brands ab ON
	ab.id = aa.brand_id
)
UPDATE ams_asset_inspection_tyre
SET custom_serial_number = cte.serial_number,
    custom_rfid = cte.rfid,
    custom_brand_name = cte.brand_name,
    custom_tyre_size = cte.tyre_size
FROM cte
WHERE ams_asset_inspection_tyre.id = cte.id;




-- CREATE OR REPLACE FUNCTION update_custom_on_insert_inspection_vehicle()
-- RETURNS TRIGGER AS $$
-- BEGIN
--     IF NEW.asset_vehicle_id IS NOT NULL THEN
--         WITH cte AS (
--             SELECT
--                 id,
--                 serial_number,
--                 reference_number,
--                 partner_owner_name,
--                 brand_name,
--                 asset_model_name
--             FROM
--                 ams_assets aa
--             LEFT JOIN ams_brands ab ON
--                 ab.id = aa.brand_id
--             LEFT JOIN ams_asset_models aam ON
--                 aam.id = aa.model_id
--             WHERE
--                 aa.id = NEW.asset_vehicle_id
--         ) UPDATE ams_asset_inspection_vehicle
--         SET custom_serial_number = cte.serial_number,
--             custom_reference_number = cte.reference_number,
--             custom_partner_owner_name = cte.partner_owner_name,
--             custom_brand_name = cte.brand_name,
--             custom_asset_model_name = cte.asset_model_name
--         FROM cte
--         WHERE ams_asset_inspection_vehicle.id = NEW.id;
--     END IF;
--     RETURN NEW;
-- END;
-- $$ LANGUAGE plpgsql;

-- DROP TRIGGER IF EXISTS update_custom_on_insert_inspection_vehicle ON ams_asset_inspection_vehicle;

-- CREATE TRIGGER update_custom_on_insert_inspection_vehicle_trigger
-- AFTER INSERT ON ams_asset_inspection_vehicle
-- FOR EACH ROW
-- EXECUTE FUNCTION update_custom_on_insert_inspection_vehicle();