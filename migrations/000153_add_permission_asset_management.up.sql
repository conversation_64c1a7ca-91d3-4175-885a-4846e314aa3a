INSERT INTO
    "uis_PERMISSION_CATEGORIES" (code,title,subtitle ,icon , "label" , description,display_sequence)
VALUES
    ('ASSET_MANAGEMENT', 'Asset Management','','asset-management.svg','Asset Management','',10);

INSERT INTO
	"uis_PERMISSIONS" (code, user_permission_category_code, label, description, display_sequence)
VALUES
('ADD_ASSETS', 'ASSET_MANAGEMENT', 'Add Asset', 'Users can add asset.', 1),
('EDIT_ASSETS', 'ASSET_MANAGEMENT', 'Update Asset', 'Users can update asset.', 2),
('VIEW_ASSET_PURCHASES', 'ASSET_MANAGEMENT', 'View Asset Purchase', 'Users can view asset purchase.', 3),
('ADD_ASSET_PURCHASES', 'ASSET_MANAGEMENT', 'Add Asset Purchase', 'Users can add asset purchase.', 4),
('UPDATE_ASSET_PURCHASES', 'ASSET_MANAGEMENT', 'Update Asset Purchase', 'Users can update asset purchase.', 5),
('VIEW_ASSET_RENT', 'ASSET_MANAGEMENT', 'View Asset Rent', 'Users can view asset rent.', 6),
('ADD_ASSET_RENT', 'ASSET_MANAGEMENT', 'Add Asset Rent', 'Users can add asset rent.', 7),
('UPDATE_ASSET_RENT', 'ASSET_MANAGEMENT', 'Update Asset Rent', 'Users can update asset rent.', 8),
('VIEW_ASSET_WARRANTIES', 'ASSET_MANAGEMENT', 'View Asset Warranty', 'Users can view asset warranty.', 9),
('ADD_ASSET_WARRANTIES', 'ASSET_MANAGEMENT', 'Add Asset Warranty', 'Users can add asset warranty.', 10),
('UPDATE_ASSET_WARRANTIES', 'ASSET_MANAGEMENT', 'Update Asset Warranty', 'Users can update asset warranty.', 11),
('VIEW_ASSET_INSURANCE', 'ASSET_MANAGEMENT', 'View Asset Insurance', 'Users can view asset insurance.', 12),
('ADD_ASSET_INSURANCE', 'ASSET_MANAGEMENT', 'Add Asset Insurance', 'Users can add asset insurance.', 13),
('UPDATE_ASSET_INSURANCE', 'ASSET_MANAGEMENT', 'Update Asset Insurance', 'Users can update asset insurance.', 14),
('VIEW_LINKED_ASSET', 'ASSET_MANAGEMENT', 'View Linked Asset', 'Users can view linked asset.', 15),
('ADD_LINKED__ASSET', 'ASSET_MANAGEMENT', 'Add Linked Asset', 'Users can add linked asset.', 16),
('UPDATE_LINKED_ASSET', 'ASSET_MANAGEMENT', 'Update Linked Asset', 'Users can update linked asset.', 17);

WITH cte AS (
SELECT
  upgr.id
FROM
  uis_permission_groups upg
JOIN uis_permission_group_rights upgr ON
  upg.id = upgr.permission_group_id
  AND permission_group_type_code = 'ADMIN'
  AND upgr.permission_category_code = 'ASSET_MANAGEMENT')
  UPDATE
  uis_permission_group_rights AS upgr SET
  permission_codes = '{ADD_ASSETS,EDIT_ASSETS,VIEW_ASSET_PURCHASES,ADD_ASSET_PURCHASES,UPDATE_ASSET_PURCHASES,VIEW_ASSET_RENT,ADD_ASSET_RENT,UPDATE_ASSET_RENT,VIEW_ASSET_WARRANTIES,ADD_ASSET_WARRANTIES,UPDATE_ASSET_WARRANTIES,VIEW_ASSET_INSURANCE,ADD_ASSET_INSURANCE,UPDATE_ASSET_INSURANCE,VIEW_LINKED_ASSET,ADD_LINKED__ASSET,UPDATE_LINKED_ASSET}'
FROM
  cte
WHERE
  upgr.id = cte.id;
