BEGIN;

-- Create vehicle meter states table to track historical meter readings
CREATE TABLE IF NOT EXISTS ams_asset_vehicle_meter_states 
(
    id VARCHAR(40) PRIMARY KEY,
    asset_id VARCHAR(40) NOT NULL,
    axle_configuration JSONB,
    vehicle_km BIGINT,
    vehicle_hm BIGINT,
    date_time TIMESTAMPTZ NOT NULL DEFAULT now(),
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    CONSTRAINT fk_asset_vehicle_meter_states_asset FOREIGN KEY (asset_id) REFERENCES ams_assets (id)
);

-- Create index for efficient querying by asset
CREATE INDEX IF NOT EXISTS idx_asset_vehicle_meter_states_asset
ON ams_asset_vehicle_meter_states (asset_id);

-- <PERSON>reate linked vehicle tyres table to track tyre-vehicle relationships
CREATE TABLE IF NOT EXISTS ams_linked_vehicle_tyres_v2
(
    id VARCHAR(40) PRIMARY KEY,
    parent_asset_id VARCHAR(40) NOT NULL, -- vehicle asset id
    child_asset_id VARCHAR(40) NOT NULL,  -- tyre asset id
    tyre_position INTEGER NOT NULL,
    is_spare BOOLEAN NOT NULL DEFAULT FALSE,
    linked_meter_state_id VARCHAR(40),
    unlinked_meter_state_id VARCHAR(40),
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    CONSTRAINT fk_linked_vehicle_tyres_parent_asset FOREIGN KEY (parent_asset_id) REFERENCES ams_assets (id),
    CONSTRAINT fk_linked_vehicle_tyres_child_asset FOREIGN KEY (child_asset_id) REFERENCES ams_assets (id),
    CONSTRAINT fk_linked_vehicle_tyres_linked_meter_state FOREIGN KEY (linked_meter_state_id) REFERENCES ams_asset_vehicle_meter_states (id),
    CONSTRAINT fk_linked_vehicle_tyres_unlinked_meter_state FOREIGN KEY (unlinked_meter_state_id) REFERENCES ams_asset_vehicle_meter_states (id)
);

-- Create index for efficient querying by parent asset
CREATE INDEX IF NOT EXISTS idx_linked_vehicle_tyres_parent_asset
ON ams_linked_vehicle_tyres_v2 (parent_asset_id);

-- Create index for efficient querying by child asset
CREATE INDEX IF NOT EXISTS idx_linked_vehicle_tyres_child_asset
ON ams_linked_vehicle_tyres_v2 (child_asset_id);

COMMIT;