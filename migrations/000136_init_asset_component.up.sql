BEGIN;

CREATE TABLE
    IF NOT EXISTS "ams_ASSET_COMPONENT_STATUTES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "ams_ASSET_COMPONENT_STATUTES" (code, "label", description)
VALUES
    ('DELETED', 'Deleted', '-'),
    ('ACTIVE', 'Active', '-'),
    ('INACTIVE', 'Inactive', '-') ON CONFLICT (code) DO NOTHING;
      
CREATE TABLE
    IF NOT EXISTS ams_asset_components (
        id VARCHAR(40) PRIMARY KEY,
        serial_number VARCHAR(50),
        component_name VARCHAR(100) NOT NULL,
        assigned_to VARCHAR(40),
        expiry_date TIMESTAMPTZ,
        status_code VARCHAR(20) REFERENCES "ams_ASSET_COMPONENT_STATUTES" (code) NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );

CREATE INDEX "idx_ams_asset_components_deleted_at" ON "ams_asset_components" ("deleted_at"); 

INSERT INTO
    "sts_ATTACHMENT_REFERENCE" (code, "label", description)
VALUES
    ('ASSET_COMPONENT', 'Asset Component', 'Asset Component') ON CONFLICT (code) DO NOTHING;
   
COMMIT;