ALTER TABLE "ams_custom_asset_categories"
ALTER COLUMN "status_code" SET DEFAULT 'ACTIVE';

ALTER TABLE "ams_custom_asset_sub_categories"
ALTER COLUMN "status_code" SET DEFAULT 'ACTIVE';

ALTER TABLE "ams_asset_models"
ALTER COLUMN "status_code" SET DEFAULT 'ACTIVE';

UPDATE "ams_custom_asset_categories" 
SET "status_code" = 'ACTIVE' 
WHERE "status_code" IS NULL;

UPDATE "ams_custom_asset_sub_categories" 
SET "status_code" = 'ACTIVE' 
WHERE "status_code" IS NULL;

UPDATE "ams_asset_models" 
SET "status_code" = 'ACTIVE' 
WHERE "status_code" IS NULL;