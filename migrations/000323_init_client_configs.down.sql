BEGIN;

DELETE FROM "uis_CLIENT_PACKAGES"
WHERE
    code IN (
    'CORE_MODULE',
    'GENERAL_CUSTOMER',
    'GENERAL_VEHICLE',
    'WORKSHOP_OPTIMAX_CUSTOMER',
    'WOR<PERSON><PERSON>OP_OPTIMAX_VEHICLE',
    'GENERAL_TODO',
    'GE<PERSON>RAL_ASSETFINDR',
    'WOR<PERSON>HOP_OPTIMAX',
    'TYRE_OPTIMAX',
    'FLEET_OPTIMAX',
    'MONITOR_OPTIMAX',
    'TRACK_OPTIMAX_AF',
    'WIALON_OPTIMAX',
    'DIGISPECT_PRO',
    'DIGISPECT_PRO_PLUS',
    'DIGISPECT_BASIC',
    'DIGISPECT_INSPECTION_FLOW_SCENARIO_1',
    'DIGISPECT_INSPECTION_FLOW_SCENARIO_2',
    'DIGISPECT_INSPECTION_FLOW_SCENARIO_3',
    'DIGISPECT_INSPECTION_FLOW_SCENARIO_4',
    'DIGISPECT_INSPECTION_FLOW_SCENARIO_5',
    'DIGISPECT_INSPECTION_FLOW_SCENARIO_6',
    'DIGISPECT_INSPECTION_FLOW_SCENARIO_7'
);

ALTER TABLE "uis_CLIENT_PACKAGES" 
    ALTER COLUMN code TYPE VARCHAR(20),
    ALTER COLUMN label TYPE VARCHAR(20);

DELETE TABLE IF EXISTS "uis_CLIENT_CONFIGS";

COMMIT;