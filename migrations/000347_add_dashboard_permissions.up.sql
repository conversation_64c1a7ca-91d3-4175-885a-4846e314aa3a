begin;

INSERT INTO public."uis_PERMISSION_CATEGORIES" (
	code, title, subtitle, icon, "label", description, display_sequence, client_package_code
) VALUES
	('ASSET_MANAGEMENT_DASHBOARD', 'Asset Management Dashboard', '', 'assets.svg', 'Asset Management Dashboard', '-', -1, 'GENERAL_ASSETFINDR'),
	('WORKSHOP_DASHBOARD', 'Workshop Dashboard', '', 'assets.svg', 'Workshop Dashboard', '-', -2, 'WORKSHOP_OPTIMAX'),
    ('TYRE_DEALER_DASHBOARD', 'Tyre Dealer Dashboard', '', 'assets.svg', 'Tyre Dealer Dashboard', '-', -3, 'GENERAL_CUSTOMER')
ON CONFLICT (code) DO NOTHING;

ALTER TABLE public."uis_PERMISSIONS" ALTER COLUMN user_permission_category_code TYPE varchar(255) USING user_permission_category_code::varchar(255);

INSERT INTO "uis_PERMISSIONS" (
	code,
	user_permission_category_code,
	"label",
	description,
	display_sequence
) VALUES
	('VIEW_ASSET_MANAGEMENT_DASHBOARD', 'ASSET_MANAGEMENT_DASHBOARD', 'View Asset Management Dashboard', 'Permission to view asset management dashboard', 0),
	('VIEW_WORKSHOP_DASHBOARD', 'WORKSHOP_DASHBOARD', 'View Workshop Dashboard', 'Permission to view workshop dashboard', 0),
	('VIEW_TYRE_DEALER_DASHBOARD', 'TYRE_DEALER_DASHBOARD', 'View Tyre Dealer Dashboard', 'Permission to view tyre dealer dashboard', 0)
ON CONFLICT (code) DO NOTHING;

-- create administrator user group right for each package group 
INSERT INTO public.uis_permission_group_rights (
    id,
    permission_group_id,
    permission_codes,
    is_enable,
    client_id,
    created_at,
    updated_at,
    permission_category_code,
    created_by,
    updated_by
)
SELECT
    'pgr_' || gen_random_uuid() AS id,  -- or use uuid_generate_v4() depending on your DB
    pg.id AS permission_group_id,
    rights.permission_codes,
    true AS is_enable,
    pg.client_id,
    now() AS created_at,
    now() AS updated_at,
    rights.permission_category_code,
    pg.created_by AS created_by,
    pg.updated_by AS updated_by
FROM public.uis_permission_groups pg
CROSS JOIN (
    -- Define default permission rights to insert for each group
    SELECT 
        ARRAY['VIEW_ASSET_MANAGEMENT_DASHBOARD']::varchar[] AS permission_codes,
        'ASSET_MANAGEMENT_DASHBOARD'::varchar AS permission_category_code
    UNION ALL
    SELECT 
        ARRAY['VIEW_WORKSHOP_DASHBOARD']::varchar[] AS permission_codes,
        'WORKSHOP_DASHBOARD'::varchar AS permission_category_code
    UNION ALL
    SELECT 
        ARRAY['VIEW_TYRE_DEALER_DASHBOARD']::varchar[] AS permission_codes,
        'TYRE_DEALER_DASHBOARD'::varchar AS permission_category_code
) AS rights;

commit;