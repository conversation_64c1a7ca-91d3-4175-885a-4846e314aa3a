BEGIN;

INSERT INTO
    "ins_INTEGRATION_TYPE" (code, "label", description)
VALUES
    ('IOT_GATEWAY', 'IoT Gateway', 'IoT Gateway') ON CONFLICT (code) DO NOTHING;

INSERT INTO
    "ins_INTEGRATION_TARGET" (
        code,
        "label",
        description,
        auth_credential_json_template
    )
VALUES
    (
        'SENDQUIP',
        'Sendquip',
        'Sendquip',
        '{}'
    ) ON CONFLICT (code) DO NOTHING;

INSERT INTO
    "ins_INTEGRATION_TARGET_TYPE" (
        code,
        label,
        description,
        integration_type_code,
        integration_target_code,
        identifier_json_template
    )
VALUES
    (
        'SENDQUIP_GATEWAY',
        'Sendquip Gateway',
        'Sendquip Gateway',
        'IOT_GATEWAY',
        'SENDQUIP',
        '{"device_id":"string"}'
    ) ON CONFLICT (code) DO NOTHING;

INSERT INTO
    "ins_INTEGRATION_TARGET_TYPE" (
        code,
        label,
        description,
        integration_type_code,
        integration_target_code,
        identifier_json_template
    )
VALUES
    (
        'FLESPI_GATEWAY',
        'Flespi Gateway',
        '-',
        'IOT_GATEWAY',
        'FLESPI',
        '{"imei":"string"}'
    ) ON CONFLICT (code) DO NOTHING;

INSERT INTO
    ins_integration_account (
        id,
        target_code,
        auth_credential_json,
        status_code,
        client_id,
        created_at,
        updated_at,
        deleted_at,
        updated_by,
        created_by,
        "name"
    )
VALUES
    (
        'ina_dd16116d-6e03-4649-a536-12db0129630d',
        'SENDQUIP',
        '',
        'ACTIVE',
        'GENERAL',
        NOW(),
        NOW(),
        NULL,
        'admin',
        'admin',
        'Sendquip Gateway'
    ) ON CONFLICT (id) DO NOTHING;

COMMIT;