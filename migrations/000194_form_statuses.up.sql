-- Create "ctn_FORM_STATUSES" table
CREATE TABLE
    IF NOT EXISTS "ctn_FORM_STATUSES" (
        "code" VARCHAR(20) NOT NULL,
        "label" VARCHAR(20) NOT NULL,
        "description" VARCHAR(50) NOT NULL,
        PRIMARY KEY ("code")
    );

-- Populate "ctn_FORM_STATUSES" table
INSERT INTO
    "ctn_FORM_STATUSES" (code, "label", description)
VALUES
    ('OPEN', 'Open', 'Open'),
    ('DONE', 'Done', 'Done')
    ON CONFLICT (code) DO NOTHING;

-- Add status_code column to "ctn_forms" table
ALTER TABLE "ctn_forms"
    ADD COLUMN IF NOT EXISTS "status_code" VARCHAR(40) REFERENCES "ctn_FORM_STATUSES"(code) DEFAULT 'OPEN';
