BEGIN;

ALTER TABLE "ams_asset_vehicles"
ADD COLUMN IF NOT EXISTS vehicle_hm BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS use_kilometer BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS use_hourmeter BOOLEAN NOT NULL DEFAULT FALSE;

UPDATE "ams_asset_vehicles" SET use_kilometer = TRUE;
UPDATE "ams_asset_vehicles" SET use_hourmeter = FALSE;

ALTER TABLE "ams_asset_tyres"
ADD COLUMN IF NOT EXISTS total_hm BIGINT DEFAULT 0;

ALTER TABLE "ams_asset_tyres"
DROP COLUMN IF EXISTS "lifetime_km",
DROP COLUMN IF EXISTS "lifetime_cpk",
DROP COLUMN IF EXISTS "lifetime_hm",
DROP COLUMN IF EXISTS "lifetime_cpm";

ALTER TABLE "ams_asset_tyres_treads"
ADD COLUMN IF NOT EXISTS total_hm BIGINT DEFAULT 0;

ALTER TABLE "ams_linked_asset_vehicle_tyres"
ADD COLUMN IF NOT EXISTS on_linked_vehicle_hm BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS on_linked_tyre_hm BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS on_unlinked_vehicle_hm BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS on_unlinked_tyre_hm BIGINT DEFAULT 0;

ALTER TABLE "ams_asset_inspection_vehicle"
ADD COLUMN IF NOT EXISTS vehicle_hm BIGINT DEFAULT 0;

ALTER TABLE "ams_asset_inspection_tyre"
ADD COLUMN IF NOT EXISTS tyre_hm BIGINT DEFAULT 0;

CREATE OR REPLACE FUNCTION update_tread_after_asset_tyre_update()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
    WITH
        cte AS (
            SELECT
                aatt.id, aa.updated_by
            FROM
                ams_asset_tyres_treads aatt JOIN ams_assets aa ON aa.id = aatt.asset_id
            WHERE
                asset_id = NEW.asset_id
            ORDER BY
                thread_sequence DESC
            LIMIT
                1
        )
    UPDATE ams_asset_tyres_treads
    SET
        average_rtd = NEW.average_rtd,
        total_km = NEW.total_km,
        total_hm = NEW.total_hm,
        total_lifetime = NEW.total_lifetime,
        start_thread_depth= NEW.start_thread_depth,
        updated_by = cte.updated_by,
        updated_at = NEW.updated_at
        FROM cte
    WHERE
        ams_asset_tyres_treads.id = cte.id;

  RETURN NEW;
END;
$$;

COMMIT;