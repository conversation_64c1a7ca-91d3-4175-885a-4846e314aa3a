BEGIN;

ALTER TABLE "ins_translogic_calibration"
ADD COLUMN IF NOT EXISTS "number" VARCHAR(20),
ADD COLUMN IF NOT EXISTS "standard_tread_depth_value" DOUBLE PRECISION DEFAULT 0;

CREATE INDEX IF NOT EXISTS idx_ins_translogic_calibration_number ON ins_translogic_calibration (number) WHERE deleted_at IS NULL;

-- Populate Past Data
WITH cte AS (
  SELECT
    id,
    'KLB-' || 
    to_char(created_at, 'YYMMDD') || '-' ||
    row_number() over (partition by client_id order by created_at) AS new_number,
    "number"
  FROM
    ins_translogic_calibration tt
  WHERE
   tt.deleted_at IS NULL
)
UPDATE ins_translogic_calibration AS tt SET "number" = cte.new_number FROM cte WHERE tt.id = cte.id;

-- Create Trigger To Set New Number
CREATE OR REPLACE FUNCTION trigger_set_new_translogic_calibration_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    'KLB-' || 
    to_char(created_at, 'YYMMDD') || '-' ||
    row_number() over (partition by client_id order by created_at) AS new_number,
    number
  FROM
    ins_translogic_calibration tt
  WHERE
   tt.deleted_at IS NULL 
   AND client_id = NEW.client_id
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE ins_translogic_calibration AS tt SET number = cte.new_number FROM cte WHERE tt.id = cte.id AND (cte.number IS NULL OR cte.number = '');
RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS set_number_after_insert_translogic_calibration ON ins_translogic_calibration;

-- Define Trigger
CREATE TRIGGER set_number_after_insert_translogic_calibration
  AFTER INSERT
  ON ins_translogic_calibration
  FOR EACH ROW
  EXECUTE PROCEDURE trigger_set_new_translogic_calibration_number();

COMMIT;



