BEGIN;

-- Create table for integration session types
CREATE TABLE IF NOT EXISTS "ins_INTEGRATION_SESSION_TYPE" (
    code VARCHAR(255) PRIMARY KEY,
    label VARCHAR(255) NOT NULL,
    description TEXT NOT NULL
);

INSERT INTO
    "ins_INTEGRATION_SESSION_TYPE" (code, "label", description)
VALUES
    ('ACCURATE', 'Accurate', '-') ON CONFLICT (code) DO NOTHING;


-- Create table for integration sessions
CREATE TABLE IF NOT EXISTS ins_integration_sessions (
    id VARCHAR(40) PRIMARY KEY,
    integration_account_id VARCHAR(40) NOT NULL UNIQUE,
    expired_time TIMESTAMPTZ,
    session_type_code VARCHAR(255) NOT NULL REFERENCES "ins_INTEGRATION_SESSION_TYPE"(code),
    data JSONB,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VA<PERSON>HA<PERSON>(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_ins_integration_sessions_on_client_id_and_session_type_code 
ON ins_integration_sessions (client_id, session_type_code);

COMMIT;