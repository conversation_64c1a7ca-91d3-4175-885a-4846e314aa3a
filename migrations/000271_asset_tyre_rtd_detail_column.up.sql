BEGIN;

ALTER TABLE ams_asset_tyres
ADD COLUMN IF NOT EXISTS rtd1 NUMERIC(14, 2),
ADD COLUMN IF NOT EXISTS rtd2 NUMERIC(14, 2),
ADD COLUMN IF NOT EXISTS rtd3 NUMERIC(14, 2),
ADD COLUMN IF NOT EXISTS rtd4 NUMERIC(14, 2);


UPDATE ams_asset_tyres aat
SET
    rtd1 =  average_rtd,
    rtd2 =  average_rtd,
    rtd3 =  average_rtd,
    rtd4 =  average_rtd WHERE average_rtd IS NOT NULL AND average_rtd != 0;

WITH
    cte AS (
        SELECT
            *
        FROM
            (
                SELECT
                    ROW_NUMBER() OVER (
                        PARTITION BY
                            asset_tyre_id
                        ORDER BY
                            created_at
                    ) AS rn,
                    *
                FROM
                    ams_asset_inspection_tyre aait
                WHERE
                    average_rtd > 0 AND average_rtd IS NOT NULL
            ) T
        WHERE
            rn = 1
    )
UPDATE ams_asset_tyres aat
SET
    rtd1 =  rdt1,
    rtd2 =  rdt2,
    rtd3 =  rdt3,
    rtd4 =  rdt4
FROM
    cte
WHERE
    cte.asset_tyre_id = aat.asset_id;

COMMIT;