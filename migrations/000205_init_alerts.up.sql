BEGIN;

-- Table: "ins_ALERT_CONFIG_STATUSES"
CREATE TABLE IF NOT EXISTS "ins_ALERT_CONFIG_STATUSES" (
    code VARCHAR(255) PRIMARY KEY,
    description TEXT,
    label TEXT
);

INSERT INTO
    "ins_ALERT_CONFIG_STATUSES" (code, "label", description)
VALUES
    ('ACTIVE', 'Active', '-'),
    ('INACTIVE', 'Inactive', '-') ON CONFLICT (code) DO NOTHING;


-- Table: "ins_ALERT_CONFIG_CATEGORIES"
CREATE TABLE IF NOT EXISTS "ins_ALERT_CONFIG_CATEGORIES" (
    code VARCHAR(255) PRIMARY KEY,
    description TEXT,
    label TEXT,
    asset_category_code VARCHAR(255)
);

INSERT INTO
    "ins_ALERT_CONFIG_CATEGORIES" (code, "label", description, asset_category_code)
VALUES
    ('VEHICLE_GPS', 'GPS', '-', 'VEHICLE'),
    ('VEHICLE_SENSOR', 'Vehicle Sensor', '-', 'VEHICLE') 
    ON CONFLICT (code) DO NOTHING;


-- Table: "ins_ALERT_CONFIG_SUB_CATEGORIES"
CREATE TABLE IF NOT EXISTS "ins_ALERT_CONFIG_SUB_CATEGORIES" (
    code VARCHAR(510) PRIMARY KEY,
    category_code VARCHAR(255) REFERENCES "ins_ALERT_CONFIG_CATEGORIES"(code),
    uom_codes VARCHAR(255)[],
    description TEXT,
    label TEXT
);

INSERT INTO
    "ins_ALERT_CONFIG_SUB_CATEGORIES" (code, "label", description, category_code, uom_codes)
VALUES
    ('GPS_VEHICLE_SPEED', 'Vehicle Speed', '-', 'VEHICLE_GPS', '{"KM"}'),
    ('VEC_SSR_BATTERY_VOLTAGE', 'Battery Voltage', '-', 'VEHICLE_SENSOR', '{"V"}')

    ON CONFLICT (code) DO NOTHING;

-- Table: "ins_ALERT_TRIGGERS"
CREATE TABLE IF NOT EXISTS "ins_ALERT_TRIGGERS" (
    code VARCHAR(510) PRIMARY KEY,
    sub_category_code VARCHAR(510) REFERENCES "ins_ALERT_CONFIG_SUB_CATEGORIES"(code),
    description TEXT,
    label TEXT
);

INSERT INTO
    "ins_ALERT_TRIGGERS" (sub_category_code, code, "label", description)
VALUES
    ('GPS_VEHICLE_SPEED', 'VEHICLE_EXCEED_SPEED_LIMIT', 'Exceeding Speed Limit', '-'),
    ('VEC_SSR_BATTERY_VOLTAGE', 'VEHICLE_SENSOR_LOW_BATTERY', 'Battery voltage drops below a certain threshold', '-')

    ON CONFLICT (code) DO NOTHING;

-- Table: "ins_ALERT_STATE_CATEGORIES"
CREATE TABLE IF NOT EXISTS "ins_ALERT_STATE_CATEGORIES" (
    code VARCHAR(255) PRIMARY KEY,
    description TEXT,
    label TEXT
);

INSERT INTO
    "ins_ALERT_STATE_CATEGORIES" (code, "label", description)
VALUES
    ('WARNING', 'Warning', '-'),
    ('ALERT', 'Alert', '-') ON CONFLICT (code) DO NOTHING;

-- Table: "ins_ALERT_ACTION_TYPES"
CREATE TABLE IF NOT EXISTS "ins_ALERT_ACTION_TYPES" (
    code VARCHAR(255) PRIMARY KEY,
    description TEXT,
    label TEXT
);

INSERT INTO
    "ins_ALERT_ACTION_TYPES" (code, "label", description)
VALUES
    ('WEB_NOTIFICATION', 'Web Notification', '-'),
    ('MOBILE_APP_NOTIFICATION', 'Mobile App Notification', '-'),
    ('EMAIL_NOTIFICATION', 'Email Notification', '-') ON CONFLICT (code) DO NOTHING;

-- Table: "ins_alert_configs"
CREATE TABLE IF NOT EXISTS "ins_alert_configs" (
    id VARCHAR(40) PRIMARY KEY,
    asset_category_code VARCHAR(255) NOT NULL,
    asset_sub_category_code VARCHAR(255) NOT NULL,
    alert_category_code VARCHAR(255) NOT NULL REFERENCES "ins_ALERT_CONFIG_CATEGORIES"(code),
    alert_sub_category_code VARCHAR(510) NOT NULL REFERENCES "ins_ALERT_CONFIG_SUB_CATEGORIES"(code),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status_code VARCHAR(255) NOT NULL REFERENCES "ins_ALERT_CONFIG_STATUSES"(code),
    asset_id VARCHAR(40),
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);

-- Table: "ins_alert_config_triggers"
CREATE TABLE IF NOT EXISTS "ins_alert_config_triggers" (
    id VARCHAR(40) PRIMARY KEY,
    alert_config_id VARCHAR(40) NOT NULL REFERENCES "ins_alert_configs"(id),
    trigger_code VARCHAR(510) REFERENCES "ins_ALERT_TRIGGERS"(code),
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);

-- Table: "ins_alert_config_trigger_states"
CREATE TABLE IF NOT EXISTS "ins_alert_config_trigger_states" (
    id VARCHAR(40) PRIMARY KEY,
    alert_config_trigger_id VARCHAR(40) NOT NULL REFERENCES "ins_alert_config_triggers"(id),
    state_category_code VARCHAR(255) REFERENCES "ins_ALERT_STATE_CATEGORIES"(code),
    value DOUBLE PRECISION,
    action_type_codes VARCHAR(255)[],
    auto_create_ticket BOOLEAN,
    ticket_priority_code VARCHAR(255),
    assigned_user_id VARCHAR(40),
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);

CREATE TABLE IF NOT EXISTS "ins_alerts" (
    id VARCHAR(40) PRIMARY KEY,
    alert_config_trigger_state_id VARCHAR(40) REFERENCES "ins_alert_config_trigger_states"(id),
    alert_config_trigger_id VARCHAR(40) REFERENCES "ins_alert_config_triggers"(id),
    alert_config_id VARCHAR(40) REFERENCES "ins_alert_configs"(id),
    state_category_code VARCHAR(255),
    trigger_code VARCHAR(510),
    asset_id VARCHAR(40),
    ticket_id VARCHAR(40),
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);

COMMIT;