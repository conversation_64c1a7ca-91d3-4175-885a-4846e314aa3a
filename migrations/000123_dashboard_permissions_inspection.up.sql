BEGIN;

INSERT INTO "uis_PERMISSIONS" (user_permission_category_code, code, label, description, display_sequence) VALUES
('DASHBOARD', 'INSPECTION_TOTAL_INSPECTION', 'Inspection Total Inspection', 'User can view Inspection Total Inspection in Dashboard', 21),
('DASHBOARD', 'INSPECTION_TOTAL_ASSET_INSPECTED', 'Inspection Total Asset Inspected', 'User can view Inspection Total Asset Inspected in Dashboard', 22);


WITH cte AS (
SELECT
  upgr.id
FROM
  uis_permission_groups upg
JOIN uis_permission_group_rights upgr ON
  upg.id = upgr.permission_group_id
  AND permission_group_type_code = 'ADMIN'
  AND upgr.permission_category_code = 'DASHBOARD')
  UPDATE
  uis_permission_group_rights AS upgr SET
  permission_codes = '{VEHICLE_NO_OF_VEHICLE, VEHICLE_STATUS, VEHICLE_TYPE, VEHICLE_TOTAL_KM, TYRE_NO_OF_TYRES, TYRE_NEW_STOCK, TYRE_SIZE, TYRE_BRAND, TYRE_RETREAD, TYRE_STATUS, TYRE_HIGHEST_KM_PER_TYRE_SIZE, TYRE_HIGHEST_TREAD_PER_TYRE_SIZE, TYRE_AVERAGE_KM_PER_MM_ORIGINAL, TYRE_AVERAGE_KM_PER_MM_RETREAD, TYRE_SCRAPED_TYRE, TYRE_DISPOSED_TYRE, TYRE_SCRAP_REASON, WORK_ORDER_SUBJECT, WORK_ORDER_STATUS, WORK_ORDER_TOP_10_SUBJECT_STATUS, INSPECTION_TOTAL_INSPECTION, INSPECTION_TOTAL_ASSET_INSPECTED}'
FROM
  cte
WHERE
  upgr.id = cte.id;

COMMIT;