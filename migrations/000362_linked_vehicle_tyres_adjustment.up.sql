BEGIN;

-- Create vehicle meter states table to track historical meter readings
CREATE TABLE IF NOT EXISTS ams_asset_vehicle_meter_states 
(
    id VARCHAR(40) PRIMARY KEY,
    asset_id VARCHAR(40) NOT NULL,
    axle_configuration JSONB,
    vehicle_km BIGINT,
    vehicle_hm BIGINT,
    date_time TIMESTAMPTZ NOT NULL DEFAULT now(),
    is_last BOOLEAN,
    delete_reason VARCHAR(255),

    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    CONSTRAINT fk_asset_vehicle_meter_states_asset FOREIGN KEY (asset_id) REFERENCES ams_assets (id)
);

-- Create index for efficient querying by asset
CREATE INDEX IF NOT EXISTS idx_asset_vehicle_meter_states_asset
ON ams_asset_vehicle_meter_states (asset_id);

CREATE OR REPLACE FUNCTION insert_asset_vehicle_meter_states()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    IF NEW.is_last IS TRUE THEN
        UPDATE ams_asset_vehicle_meter_states
        SET is_last = FALSE
        WHERE is_last = TRUE
        AND asset_id = NEW.asset_id 
        AND id != NEW.id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS insert_asset_vehicle_meter_states ON ams_asset_vehicle_meter_states;

CREATE TRIGGER insert_asset_vehicle_meter_states
AFTER INSERT ON ams_asset_vehicle_meter_states
FOR EACH ROW
EXECUTE FUNCTION insert_asset_vehicle_meter_states();

CREATE OR REPLACE FUNCTION update_asset_vehicle_meter_states()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' AND NEW.deleted_at IS NOT NULL THEN
        -- CASE Delete
        WITH cte AS (
            SELECT 
                ams_linked_assets.id,
                ams_linked_assets.child_asset_id,
                aavmsb.date_time AS before_date_time,
                aavmsb.vehicle_km AS before_vehicle_km,
                aavmsb.vehicle_hm AS before_vehicle_hm,
                aav.vehicle_km AS asset_vehicle_km,
                aav.vehicle_hm AS asset_vehicle_hm 
            FROM ams_linked_assets 
            JOIN ams_asset_vehicle_meter_states aavmsb 
            ON aavmsb.id = ams_linked_assets.linked_state_id
            JOIN ams_asset_vehicles aav ON aav.asset_id = NEW.asset_id
            WHERE unlinked_state_id = NEW.id AND linked_state_id != NEW.id
            AND ams_linked_assets.deleted_at IS NULL
        )
        UPDATE ams_asset_tyres aat
        SET 
            total_km = total_km - NEW.vehicle_km + cte.asset_vehicle_km,
            total_hm = total_hm - NEW.vehicle_hm + cte.asset_vehicle_hm,
            total_lifetime = total_lifetime - EXTRACT(EPOCH FROM (NEW.date_time - cte.before_date_time)),
            updated_at = NEW.deleted_at,
            updated_by = NEW.updated_by
        FROM cte
        WHERE aat.asset_id = cte.child_asset_id;

        WITH cte AS (
            SELECT 
                ams_linked_assets.id
            FROM ams_linked_assets 
            WHERE unlinked_state_id = NEW.id AND ams_linked_assets.deleted_at IS NULL
        )
        UPDATE ams_linked_assets ala
        SET unlinked_state_id = NULL,
            unlinked_datetime = NULL
        FROM cte
        WHERE ala.id = cte.id;

        WITH cte AS (
            SELECT 
                ams_linked_assets.id
            FROM ams_linked_assets 
            WHERE unlinked_state_id = NEW.id AND ams_linked_assets.deleted_at IS NULL
        )
        UPDATE ams_linked_asset_vehicle_tyres alavt
        SET on_unlinked_vehicle_km = NULL,
            on_unlinked_tyre_hm = NULL,
            on_unlinked_tyre_km = NULL
        FROM cte
        WHERE alavt.asset_linked_id = cte.id;

        WITH cte AS (
            SELECT id FROM ams_linked_assets
            WHERE linked_state_id = NEW.id AND deleted_at IS NULL
        )
        UPDATE ams_linked_asset_vehicle_tyres alavt
        SET deleted_at = NEW.deleted_at,
            updated_at = NEW.deleted_at
        FROM cte
        WHERE alavt.asset_linked_id = cte.id;

        WITH cte AS (
            SELECT id FROM ams_linked_assets
            WHERE linked_state_id = NEW.id AND deleted_at IS NULL
        )
        UPDATE ams_linked_assets ala
        SET deleted_at = NEW.deleted_at,
            updated_at = NEW.deleted_at,
            updated_by = NEW.updated_by
        FROM cte
        WHERE ala.id = cte.id;

        -- END CASE Delete    
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_asset_vehicle_meter_states ON ams_asset_vehicle_meter_states;

CREATE TRIGGER update_asset_vehicle_meter_states
AFTER UPDATE ON ams_asset_vehicle_meter_states
FOR EACH ROW
EXECUTE FUNCTION update_asset_vehicle_meter_states();


ALTER TABLE ams_linked_assets
ADD COLUMN IF NOT EXISTS linked_state_id VARCHAR(40) REFERENCES ams_asset_vehicle_meter_states (id) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS unlinked_state_id VARCHAR(40) REFERENCES ams_asset_vehicle_meter_states (id) DEFAULT NULL; 


COMMIT;