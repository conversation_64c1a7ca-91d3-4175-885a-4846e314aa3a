BEGIN;

ALTER TABLE "ams_asset_handover_requests"
ADD COLUMN IF NOT EXISTS "number" VARCHAR(20);

CREATE INDEX IF NOT EXISTS idx_ams_asset_handover_requests_number ON ams_asset_handover_requests (number) WHERE deleted_at IS NULL;

-- Populate Past Data
WITH cte AS (
  SELECT
    id,
    'AHO-' || 
    to_char(created_at, 'YYMMDD') || '-' ||
    row_number() over (partition by client_id order by created_at) AS new_number,
    "number"
  FROM 
    ams_asset_handover_requests tt
  WHERE
   tt.deleted_at IS NULL
)
UPDATE ams_asset_handover_requests AS tt SET "number" = cte.new_number FROM cte WHERE tt.id = cte.id;

-- Create Trigger To Set New Number
CREATE OR REPLACE FUNCTION trigger_set_new_asset_handover_requests_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    'AHO-' || 
    to_char(created_at, 'YYMMDD') || '-' ||
    row_number() over (partition by client_id order by created_at) AS new_number,
    number
  FROM
    ams_asset_handover_requests tt
  WHERE
   tt.deleted_at IS NULL 
   AND client_id = NEW.client_id
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE ams_asset_handover_requests AS tt SET number = cte.new_number FROM cte WHERE tt.id = cte.id AND (cte.number IS NULL OR cte.number = '');
RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS set_number_after_insert_asset_handover_requests ON ams_asset_handover_requests;

-- Define Trigger
CREATE TRIGGER set_number_after_insert_asset_handover_requests
  AFTER INSERT
  ON ams_asset_handover_requests
  FOR EACH ROW
  EXECUTE PROCEDURE trigger_set_new_asset_handover_requests_number();

COMMIT;



