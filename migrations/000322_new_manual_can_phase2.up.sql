BEGIN;

ALTER TABLE "ins_ALERT_PARAMETERS"
ALTER COLUMN icon_path SET DEFAULT 'https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg';

INSERT INTO
    "ins_ALERT_PARAMETERS" (code,label,description,unit,data_type,source_codes,type_code)
VALUES
('can_bus.adblue_level','Level AdBlue', '<PERSON><PERSON><PERSON>', '%',  'NUMERIC', '{FLESPI_TELTONIKA}', 'ORIGINAL');


UPDATE ins_integration_data_mapping_templates SET data_mapping = jsonb_set(data_mapping, '{can_bus.adblue_level}', '"can.adblue.level"');

UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Coolant Temperature', "description" = 'Suhu cairan pendingin mesin', unit = '°C' WHERE code = 'can_bus.engine_coolant_temperature';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Fuel 1 Temperature 1', "description" = '<PERSON><PERSON> bahan bakar mesin 1', unit = '°C' WHERE code = 'can_bus.engine_fuel_1_temperature_1';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Oil Temperature 1', "description" = 'Suhu oli mesin', unit = '°C' WHERE code = 'can_bus.engine_oil_temperature_1';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Turbocharger 1 Oil Temperature', "description" = 'Suhu oli turbocharger mesin', unit = '°C' WHERE code = 'can_bus.engine_turbocharger_1_oil_temperature';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Intercooler Temperature', "description" = 'Suhu intercooler mesin', unit = '°C' WHERE code = 'can_bus.engine_intercooler_temperature';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Charge Air Cooler Thermostat Opening', "description" = 'Posisi pembukaan termostat pendingin udara mesin', unit = '%' WHERE code = 'can_bus.engine_charge_air_cooler_thermostat_opening';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Fuel Delivery Pressure', "description" = 'Tekanan suplai bahan bakar ke mesin', unit = 'kPa' WHERE code = 'can_bus.engine_fuel_delivery_pressure';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Extended Crankcase Blow By Pressure', "description" = 'Tekanan blow-by crankcase mesin', unit = 'kPa' WHERE code = 'can_bus.engine_extended_crankcase_blow_by_pressure';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Oil Level', "description" = 'Level oli mesin', unit = '%' WHERE code = 'can_bus.engine_oil_level';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Oil Pressure 1', "description" = 'Tekanan oli mesin', unit = 'kPa' WHERE code = 'can_bus.engine_oil_pressure_1';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Crankcase Pressure 1', "description" = 'Tekanan crankcase mesin', unit = 'kPa' WHERE code = 'can_bus.engine_crankcase_pressure_1';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Coolant Pressure 1', "description" = 'Tekanan cairan pendingin mesin', unit = 'kPa' WHERE code = 'can_bus.engine_coolant_pressure_1';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Coolant Level 1', "description" = 'Level cairan pendingin mesin', unit = '%' WHERE code = 'can_bus.engine_coolant_level_1';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Total Hours Of Operation', "description" = 'Total jam operasi mesin', unit = 'hours' WHERE code = 'can_bus.engine_total_hours_of_operation';
UPDATE "ins_ALERT_PARAMETERS" SET label = 'Engine Total Revolutions', "description" = 'Total putaran mesin', unit = 'revolutions' WHERE code = 'can_bus.engine_total_revolutions';

INSERT INTO
    "ins_ALERT_PARAMETERS" (code,label,description,unit,data_type,source_codes,type_code)
VALUES
('can_bus.front_axle_speed','Front Axle Speed','Kecepatan poros roda depan','km/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.relative_speed_front_axle_left_wheel','Relative Speed; Front Axle, Left Wheel','Kecepatan relatif roda kiri poros depan','km/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.relative_speed_front_axle_right_wheel','Relative Speed; Front Axle, Right Wheel','Kecepatan relatif roda kanan poros depan','km/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.relative_speed_rear_axle_1_left_wheel','Relative Speed; Rear Axle #1, Left Wheel','Kecepatan relatif roda kiri poros belakang #1','km/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.relative_speed_rear_axle_1_right_wheel','Relative Speed; Rear Axle #1, Right Wheel','Kecepatan relatif roda kanan poros belakang #1','km/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.relative_speed_rear_axle_2_left_wheel','Relative Speed; Rear Axle #2, Left Wheel','Kecepatan relatif roda kiri poros belakang #2','km/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.relative_speed_rear_axle_2_right_wheel','Relative Speed; Rear Axle #2, Right Wheel','Kecepatan relatif roda kanan poros belakang #2','km/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_fuel_rate','Engine Fuel Rate','Laju konsumsi bahan bakar mesin','l/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_instantaneous_fuel_economy','Engine Instantaneous Fuel Economy','Efisiensi bahan bakar mesin secara real-time','km/L', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_average_fuel_economy','Engine Average Fuel Economy','Rata-rata efisiensi bahan bakar mesin','km/L', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_throttle_valve_1_position_1','Engine Throttle Valve 1 Position 1','Posisi katup throttle mesin 1','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_throttle_valve_2_position','Engine Throttle Valve 2 Position','Posisi katup throttle mesin 2','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_driveline_engaged','Transmission Driveline Engaged','Status penggerak transmisi aktif atau tidak','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_torque_converter_lockup_engaged','Transmission Torque Converter Lockup Engaged','Status penguncian torque converter transmisi','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_shift_in_process','Transmission Shift In Process','Status perpindahan gigi transmisi sedang berlangsung','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_torque_converter_lockup_transition_in_process','Transmission Torque Converter Lockup Transition in Process','Status transisi penguncian torque converter transmisi','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_output_shaft_speed','Transmission Output Shaft Speed','Kecepatan poros keluaran transmisi','rpm', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.percent_clutch_slip','Percent Clutch Slip','Persentase slip kopling','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_momentary_overspeed_enable','Engine Momentary Overspeed Enable','Aktivasi batas kecepatan mesin sesaat','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.progressive_shift_disable','Progressive Shift Disable','Status penonaktifan perpindahan gigi progresif','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.momentary_engine_maximum_power_enable','Momentary Engine Maximum Power Enable','Aktivasi daya maksimum mesin sementara','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_input_shaft_speed','Transmission Input Shaft Speed','Kecepatan poros masukan transmisi','rpm', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.source_address_of_controlling_device_for_transmission_control','Source Address of Controlling Device for Transmission Control','Alamat sumber perangkat pengendali transmisi','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_torque_mode','Engine Torque Mode','Mode torsi mesin','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.actual_engine_percent_torque_fractional','Actual Engine Percent Torque (Fractional)','Persentase torsi mesin aktual','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.driver_s_demand_engine_percent_torque','Driver’s Demand Engine Percent Torque','Persentase torsi mesin yang diminta pengemudi','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.actual_engine_percent_torque','Actual Engine Percent Torque','Persentase torsi mesin aktual','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_speed','Engine Speed','Kecepatan putaran mesin (RPM)','rpm', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.source_address_of_controlling_device_for_engine_control','Source Address of Controlling Device for Engine Control','Alamat sumber perangkat pengendali mesin','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_starter_mode','Engine Starter Mode','Mode starter mesin','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_demand_percent_torque','Engine Demand – Percent Torque','Permintaan torsi mesin dalam persen','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.accelerator_pedal_1_low_idle_switch','Accelerator Pedal 1 Low Idle Switch','Sakelar idle rendah pedal akselerator 1','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.accelerator_pedal_kickdown_switch','Accelerator Pedal Kickdown Switch','Sakelar kickdown pedal akselerator','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.road_speed_limit_status','Road Speed Limit Status','Status batas kecepatan kendaraan','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.accelerator_pedal_2_low_idle_switch','Accelerator Pedal 2 Low Idle Switch','Sakelar idle rendah pedal akselerator 2','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.accelerator_pedal_1_position','Accelerator Pedal 1 Position','Posisi pedal akselerator 1','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_percent_load_at_current_speed','Engine Percent Load At Current Speed','Persentase beban mesin pada kecepatan saat ini','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.remote_accelerator_pedal_position','Remote Accelerator Pedal Position','Posisi pedal akselerator jarak jauh','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.accelerator_pedal_2_position','Accelerator Pedal 2 Position','Posisi pedal akselerator 2','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.vehicle_acceleration_rate_limit_status','Vehicle Acceleration Rate Limit Status','Status batas laju percepatan kendaraan','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.momentary_engine_maximum_power_enable_feedback','Momentary Engine Maximum Power Enable Feedback','Umpan balik aktivasi daya maksimum mesin sementara','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.dpf_thermal_management_active','DPF Thermal Management Active','Status manajemen termal DPF aktif','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.scr_thermal_management_active','SCR Thermal Management Active','Status manajemen termal SCR aktif','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.actual_maximum_available_engine_percent_torque','Actual Maximum Available Engine Percent Torque','Persentase torsi mesin maksimum yang tersedia','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.estimated_pumping_percent_torque','Estimated Pumping Percent Torque','Estimasi persentase torsi pompa mesin','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.two_speed_axle_switch','Two Speed Axle Switch','Sakelar poros dua kecepatan','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.parking_brake_switch','Parking Brake Switch','Sakelar rem parkir','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.cruise_control_pause_switch','Cruise Control Pause Switch','Sakelar jeda kontrol jelajah','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.park_brake_release_inhibit_request','Park Brake Release Inhibit Request','Permintaan penghambatan pelepasan rem parkir','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.wheel_based_vehicle_speed','Wheel-Based Vehicle Speed','Kecepatan kendaraan berbasis roda','km/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.cruise_control_active','Cruise Control Active','Status kontrol jelajah aktif','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.cruise_control_enable_switch','Cruise Control Enable Switch','Sakelar aktivasi kontrol jelajah','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.brake_switch','Brake Switch','Sakelar rem','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.clutch_switch','Clutch Switch','Sakelar kopling','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.cruise_control_set_switch','Cruise Control Set Switch','Sakelar pengaturan kontrol jelajah','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.cruise_control_coast_decelerate_switch','Cruise Control Coast (Decelerate) Switch','Sakelar perlambatan kontrol jelajah','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.cruise_control_resume_switch','Cruise Control Resume Switch','Sakelar lanjutkan kontrol jelajah','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.cruise_control_accelerate_switch','Cruise Control Accelerate Switch','Sakelar percepatan kontrol jelajah','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.cruise_control_set_speed','Cruise Control Set Speed','Kecepatan yang disetel dalam kontrol jelajah','km/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.pto_governor_state','PTO Governor State','Status pengatur PTO','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.cruise_control_states','Cruise Control States','Status kontrol jelajah','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_idle_increment_switch','Engine Idle Increment Switch','Sakelar peningkatan idle mesin','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_idle_decrement_switch','Engine Idle Decrement Switch','Sakelar penurunan idle mesin','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_diagnostic_test_mode_switch','Engine Diagnostic Test Mode Switch','Sakelar mode uji diagnostik mesin','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_shutdown_override_switch','Engine Shutdown Override Switch','Sakelar pengabaian pemadaman mesin','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_gas_recirculation_1_mass_flow_rate','Engine Exhaust Gas Recirculation 1 Mass Flow Rate','Laju aliran gas buang EGR 1','kg/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_intake_air_mass_flow_rate','Engine Intake Air Mass Flow Rate','Laju aliran udara masuk mesin','kg/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_gas_recirculation_2_mass_flow_rate','Engine Exhaust Gas Recirculation 2 Mass Flow Rate','Laju aliran gas buang EGR 2','kg/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.target_fresh_air_mass_flow','Target Fresh Air Mass Flow','Target aliran udara segar','kg/h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.asr_engine_control_active','ASR Engine Control Active','Status kontrol mesin ASR aktif','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.asr_brake_control_active','ASR Brake Control Active','Status kontrol rem ASR aktif','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.anti_lock_braking_abs_active','Anti-Lock Braking (ABS) Active','Status sistem rem ABS aktif','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.ebs_brake_switch','EBS Brake Switch','Sakelar rem EBS','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.brake_pedal_position','Brake Pedal Position','Posisi pedal rem','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.abs_off_road_switch','ABS Off-road Switch','Sakelar ABS untuk kondisi off-road','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.asr_off_road_switch','ASR Off-road Switch','Sakelar ASR untuk kondisi off-road','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.asr_hill_holder_switch','ASR "Hill Holder" Switch','Sakelar ASR untuk fungsi hill-holder','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.traction_control_override_switch','Traction Control Override Switch','Sakelar override kontrol traksi','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.accelerator_interlock_switch','Accelerator Interlock Switch','Sakelar pengunci pedal akselerator','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_derate_switch','Engine Derate Switch','Sakelar pembatas daya mesin','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_auxiliary_shutdown_switch','Engine Auxiliary Shutdown Switch','Sakelar pemadaman mesin tambahan','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.remote_accelerator_enable_switch','Remote Accelerator Enable Switch','Sakelar aktivasi akselerator jarak jauh','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_retarder_selection','Engine Retarder Selection','Pemilihan mode pengereman mesin','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.abs_fully_operational','ABS Fully Operational','Status operasional penuh sistem ABS','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.ebs_red_warning_signal','EBS Red Warning Signal','Sinyal peringatan merah sistem EBS','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.abs_ebs_amber_warning_signal_powered_vehicle','ABS/EBS Amber Warning Signal (Powered Vehicle)','Sinyal peringatan kuning ABS/EBS untuk kendaraan bermotor','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.atc_asr_information_signal','ATC/ASR Information Signal','Sinyal informasi kontrol traksi ASR','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.source_address_of_controlling_device_for_brake_control','Source Address of Controlling Device for Brake Control','Alamat sumber perangkat pengendali rem','source address', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.railroad_mode_switch','Railroad Mode Switch','Sakelar mode rel kereta api','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.halt_brake_switch','Halt Brake Switch','Sakelar rem berhenti','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.trailer_abs_status','Trailer ABS Status','Status ABS pada trailer','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.tractor_mounted_trailer_abs_warning_signal','Tractor-Mounted Trailer ABS Warning Signal','Sinyal peringatan ABS untuk trailer yang dipasang di traktor','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_clutch_1_pressure','Transmission Clutch 1 Pressure','Tekanan kopling transmisi 1','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_oil_level_1','Transmission Oil Level 1','Level oli transmisi 1','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_filter_differential_pressure','Transmission Filter Differential Pressure','Perbedaan tekanan pada filter transmisi','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_1_oil_pressure','Transmission 1 Oil Pressure','Tekanan oli pada transmisi 1','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_1_oil_temperature_1','Transmission 1 Oil Temperature 1','Suhu oli transmisi 1','°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_oil_level_1_high_low','Transmission Oil Level 1 High / Low','Status level oli transmisi 1 (tinggi/rendah)','l', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_oil_level_1_countdown_timer','Transmission Oil Level 1 Countdown Timer','Penghitung waktu mundur untuk level oli transmisi 1','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.transmission_oil_level_1_measurement_status','Transmission Oil Level 1 Measurement Status','Status pengukuran level oli transmisi 1','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.pneumatic_supply_pressure','Pneumatic Supply Pressure','Tekanan suplai udara pneumatik','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.parking_and_or_trailer_air_pressure','Parking and/or Trailer Air Pressure','Tekanan udara untuk rem parkir dan/atau trailer','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.service_brake_circuit_1_air_pressure','Service Brake Circuit 1 Air Pressure','Tekanan udara pada sirkuit rem layanan 1','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.service_brake_circuit_2_air_pressure','Service Brake Circuit 2 Air Pressure','Tekanan udara pada sirkuit rem layanan 2','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.auxiliary_equipment_supply_pressure','Auxiliary Equipment Supply Pressure','Tekanan suplai udara untuk peralatan tambahan','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.air_suspension_supply_pressure_1','Air Suspension Supply Pressure 1','Tekanan suplai udara untuk suspensi udara 1','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.air_compressor_status','Air Compressor Status','Status operasional kompresor udara','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.powertrain_circuit_air_supply_pressure','Powertrain Circuit Air Supply Pressure','Tekanan suplai udara untuk sistem powertrain','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_1_n_ox_1','Engine Exhaust 1 NOx 1','Konsentrasi NOx pada gas buang mesin 1','ppm', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_1_percent_oxygen_1','Engine Exhaust 1 Percent Oxygen 1','Persentase oksigen dalam gas buang mesin 1','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_1_gas_sensor_1_power_in_range','Engine Exhaust 1 Gas Sensor 1 Power In Range','Status daya sensor gas buang 1 dalam rentang normal','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_1_gas_sensor_1_at_temperature','Engine Exhaust 1 Gas Sensor 1 at Temperature','Status sensor gas buang 1 telah mencapai suhu operasi','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_1_n_ox_1_reading_stable','Engine Exhaust 1 NOx 1 Reading Stable','Status pembacaan NOx gas buang 1 stabil','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_1_wide_range_percent_oxygen_1_reading_stable','Engine Exhaust 1 Wide-Range Percent Oxygen 1 Reading Stable','Status pembacaan oksigen gas buang 1 stabil dalam rentang luas','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_1_gas_sensor_1_heater_preliminary_fmi','Engine Exhaust 1 Gas Sensor 1 Heater Preliminary FMI','Indikasi awal kerusakan pemanas sensor gas buang 1','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_1_gas_sensor_1_heater_control','Engine Exhaust 1 Gas Sensor 1 Heater Control','Kontrol pemanas sensor gas buang 1','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_1_n_ox_sensor_1_preliminary_fmi','Engine Exhaust 1 NOx Sensor 1 Preliminary FMI','Indikasi awal kerusakan sensor NOx gas buang 1','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_1_n_ox_sensor_1_self_diagnosis_status','Engine Exhaust 1 NOx Sensor 1 Self-diagnosis Status','Status diagnostik mandiri sensor NOx gas buang 1','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_1_oxygen_sensor_1_preliminary_fmi','Engine Exhaust 1 Oxygen Sensor 1 Preliminary FMI','Indikasi awal kerusakan sensor oksigen gas buang 1','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_trip_fuel_high_resolution','Engine Trip Fuel (High Resolution)','Konsumsi bahan bakar mesin selama perjalanan dengan resolusi tinggi','l', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_total_fuel_used_high_resolution','Engine Total Fuel Used (High Resolution)','Total bahan bakar yang digunakan mesin dengan resolusi tinggi','l', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.aftertreatment_1_scr_intake_temperature','Aftertreatment 1 SCR Intake Temperature','Suhu gas buang yang masuk ke sistem SCR','°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.aftertreatment_1_scr_intake_temperature_preliminary_fmi','Aftertreatment 1 SCR Intake Temperature Preliminary FMI','Indikasi awal kerusakan suhu intake SCR','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.aftertreatment_1_scr_outlet_temperature','Aftertreatment 1 SCR Outlet Temperature','Suhu gas buang yang keluar dari sistem SCR','°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.aftertreatment_1_scr_outlet_temperature_preliminary_fmi','Aftertreatment 1 SCR Outlet Temperature Preliminary FMI','Indikasi awal kerusakan suhu outlet SCR','', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.powered_vehicle_weight','Powered Vehicle Weight','Berat kendaraan bermotor','kg', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.gross_combination_vehicle_weight','Gross Combination Vehicle Weight','Berat total kombinasi kendaraan (truk + trailer)','kg', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.gross_combination_vehicle_weight_confidence','Gross Combination Vehicle Weight Confidence','Keakuratan pengukuran berat kombinasi kendaraan','%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.total_vehicle_distance_high_resolution','Total Vehicle Distance (High Resolution)','Jarak total kendaraan dengan resolusi tinggi','m', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.trip_distance_high_resolution','Trip Distance (High Resolution)','Jarak perjalanan kendaraan dengan resolusi tinggi','m', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.seconds','Seconds','Waktu dalam satuan detik','s', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.minutes','Minutes','Waktu dalam satuan menit','min', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.hours','Hours','Waktu dalam satuan jam','h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.month','Month','Waktu dalam satuan bulan','month', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.day','Day','Waktu dalam satuan hari','days', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.year','Year','Waktu dalam satuan tahun','year', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.local_minute_offset','Local Minute Offset','Selisih menit dari waktu lokal terhadap UTC','min', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.local_hour_offset','Local Hour Offset','Selisih jam dari waktu lokal terhadap UTC','h', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.barometric_pressure','Barometric Pressure','Tekanan udara atmosfer sekitar','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.cab_interior_temperature','Cab Interior Temperature','Suhu dalam kabin kendaraan','°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.ambient_air_temperature','Ambient Air Temperature','Suhu udara sekitar kendaraan','°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_intake_1_air_temperature','Engine Intake 1 Air Temperature','Suhu udara masuk ke mesin','°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.road_surface_temperature','Road Surface Temperature','Suhu permukaan jalan','°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.aftertreatment_1_diesel_particulate_filter_intake_pressure','Aftertreatment 1 Diesel Particulate Filter Intake Pressure','Tekanan gas buang masuk ke filter partikulat diesel (DPF)','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_intake_manifold_1_pressure','Engine Intake Manifold #1 Pressure','Tekanan udara pada manifold intake mesin 1','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_intake_manifold_1_temperature','Engine Intake Manifold 1 Temperature','Suhu udara pada manifold intake mesin 1','°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_intake_air_pressure','Engine Intake Air Pressure','Tekanan udara masuk ke mesin','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_air_filter_1_differential_pressure','Engine Air Filter 1 Differential Pressure','Perbedaan tekanan pada filter udara mesin 1','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_exhaust_temperature','Engine Exhaust Temperature','Suhu gas buang mesin','°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_coolant_filter_differential_pressure','Engine Coolant Filter Differential Pressure','Perbedaan tekanan pada filter pendingin mesin','kPa', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.manual.18FEBF0B', 'Manual Can 18FEBF0B', 'Wheel Speed Information', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.18FEF200', 'Manual Can 18FEF200', 'Fuel Economy (Liquid)', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.0CF00203', 'Manual Can 0CF00203', 'Electronic Transmission Controller 1', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.0CF00400', 'Manual Can 0CF00400', 'Electronic Engine Controller 1', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.0CF00300', 'Manual Can 0CF00300', 'Electronic Engine Controller 2', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.18FEF111', 'Manual Can 18FEF111', 'Cruise Control/ Vehicle Speed 1', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.0CF00A00', 'Manual Can 0CF00A00', 'Engine Gas Flow Rate', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.18F0010B', 'Manual Can 18F0010B', 'Electronic Brake Controller 1', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.18FEF803', 'Manual Can 18FEF803', 'Transmission Fluids 1', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.18FEAE11', 'Manual Can 18FEAE11', 'Air Supply Pressure', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.18F00E00', 'Manual Can 18F00E00', 'Aftertreatment 1 Intake Gas 1', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.18FD0900', 'Manual Can 18FD0900', 'High Resolution Fuel Consumption (Liquid)', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.14FD3E00', 'Manual Can 14FD3E00', 'Aftertreatment 1 SCR Exhaust Gas Temperature 1', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.18FE700B', 'Manual Can 18FE700B', 'Combination Vehicle Weight', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.18FEC111', 'Manual Can 18FEC111', 'High Resolution Vehicle Distance', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.18FEE617', 'Manual Can 18FEE617', 'Time/ Date', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.18FEF511', 'Manual Can 18FEF511', 'Ambient Conditions', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.manual.18FEF600', 'Manual Can 18FEF600', 'Intake/ Exhaust Conditions 1', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN')
ON CONFLICT (code) DO UPDATE SET label = excluded.label, description = excluded.description, unit = excluded.unit;

ALTER TABLE "ins_MANUAL_CAN_BUS_MAPPINGS" ADD COLUMN IF NOT EXISTS is_all_f_null BOOLEAN DEFAULT true;

INSERT INTO "ins_MANUAL_CAN_BUS_MAPPINGS" 
(can_id_code, pgn_spn_code, parameter_code, label, start_byte, len_byte, scale, formula, is_all_f_null)
VALUES
('18FEBF0B', '65215.904', 'can_bus.front_axle_speed', 'Front Axle Speed', 1, 2, 0.00390625, '(value*scale) - 0', TRUE),
('18FEBF0B', '65215.905', 'can_bus.relative_speed_front_axle_left_wheel', 'Relative Speed; Front Axle, Left Wheel', 3, 1, 0.0625, '(value*scale) -7.8125', TRUE),
('18FEBF0B', '65215.906', 'can_bus.relative_speed_front_axle_right_wheel', 'Relative Speed; Front Axle, Right Wheel', 4, 1, 0.0625, '(value*scale) -7.8125', TRUE),
('18FEBF0B', '65215.907', 'can_bus.relative_speed_rear_axle_1_left_wheel', 'Relative Speed; Rear Axle #1, Left Wheel', 5, 1, 0.0625, '(value*scale) -7.8125', TRUE),
('18FEBF0B', '65215.908', 'can_bus.relative_speed_rear_axle_1_right_wheel', 'Relative Speed; Rear Axle #1, Right Wheel', 6, 1, 0.0625, '(value*scale) -7.8125', TRUE),
('18FEBF0B', '65215.909', 'can_bus.relative_speed_rear_axle_2_left_wheel', 'Relative Speed; Rear Axle #2, Left Wheel', 7, 1, 0.0625, '(value*scale) -7.8125', TRUE),
('18FEBF0B', '65215.910', 'can_bus.relative_speed_rear_axle_2_right_wheel', 'Relative Speed; Rear Axle #2, Right Wheel', 8, 1, 0.0625, '(value*scale) -7.8125', TRUE),
('18FEF200', '65266.183', 'can_bus.engine_fuel_rate', 'Engine Fuel Rate', 1, 2, 0.05, '(value*scale) - 0', TRUE),
('18FEF200', '65266.184', 'can_bus.engine_instantaneous_fuel_economy', 'Engine Instantaneous Fuel Economy', 3, 2, 0.001953125, '(value*scale) - 0', TRUE),
('18FEF200', '65266.185', 'can_bus.engine_average_fuel_economy', 'Engine Average Fuel Economy', 5, 2, 0.001953125, '(value*scale) - 0', TRUE),
('18FEF200', '65266.51', 'can_bus.engine_throttle_valve_1_position_1', 'Engine Throttle Valve 1 Position 1', 7, 1, 0.4, '(value*scale) - 0', TRUE),
('18FEF200', '65266.3673', 'can_bus.engine_throttle_valve_2_position', 'Engine Throttle Valve 2 Position', 8, 1, 0.4, '(value*scale) - 0', TRUE),
('0CF00203', '61442.560', 'can_bus.transmission_driveline_engaged', 'Transmission Driveline Engaged', 1, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('0CF00203', '61442.573', 'can_bus.transmission_torque_converter_lockup_engaged', 'Transmission Torque Converter Lockup Engaged', 1, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('0CF00203', '61442.574', 'can_bus.transmission_shift_in_process', 'Transmission Shift In Process', 1, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('0CF00203', '61442.4816', 'can_bus.transmission_torque_converter_lockup_transition_in_process', 'Transmission Torque Converter Lockup Transition in Process', 1, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('0CF00203', '61442.191', 'can_bus.transmission_output_shaft_speed', 'Transmission Output Shaft Speed', 2, 2, 0.125, '(value*scale) - 0', TRUE),
('0CF00203', '61442.522', 'can_bus.percent_clutch_slip', 'Percent Clutch Slip', 4, 1, 0.4, '(value*scale) - 0', TRUE),
('0CF00203', '61442.606', 'can_bus.engine_momentary_overspeed_enable', 'Engine Momentary Overspeed Enable', 5, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('0CF00203', '61442.607', 'can_bus.progressive_shift_disable', 'Progressive Shift Disable', 5, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('0CF00203', '61442.5015', 'can_bus.momentary_engine_maximum_power_enable', 'Momentary Engine Maximum Power Enable', 5, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('0CF00203', '61442.161', 'can_bus.transmission_input_shaft_speed', 'Transmission Input Shaft Speed', 6, 2, 0.125, '(value*scale) - 0', TRUE),
('0CF00203', '61442.1482', 'can_bus.source_address_of_controlling_device_for_transmission_control', 'Source Address of Controlling Device for Transmission Control', 8, 1, 1, '(value*scale) - 0', TRUE),
('0CF00400', '61444.899', 'can_bus.engine_torque_mode', 'Engine Torque Mode', 1, 1, 1, '(value >> (1 - 1)) & 15', FALSE),
('0CF00400', '61444.4154', 'can_bus.actual_engine_percent_torque_fractional', 'Actual Engine Percent Torque (Fractional)', 1, 1, 0.125, '(value >> (5 - 1)) & 15', FALSE),
('0CF00400', '61444.512', 'can_bus.driver_s_demand_engine_percent_torque', 'Driver’s Demand Engine Percent Torque''s Demand Engine - Percent Torque', 2, 1, 1, '(value*scale) -125', TRUE),
('0CF00400', '61444.513', 'can_bus.actual_engine_percent_torque', 'Actual Engine Percent Torque', 3, 1, 1, '(value*scale) -125', TRUE),
('0CF00400', '61444.190', 'can_bus.engine_speed', 'Engine Speed', 4, 2, 0.125, '(value*scale) - 0', TRUE),
('0CF00400', '61444.1483', 'can_bus.source_address_of_controlling_device_for_engine_control', 'Source Address of Controlling Device for Engine Control', 6, 1, 1, '(value*scale) - 0', TRUE),
('0CF00400', '61444.1675', 'can_bus.engine_starter_mode', 'Engine Starter Mode', 7, 1, 1, '(value >> (1 - 1)) & 15', FALSE),
('0CF00400', '61444.2432', 'can_bus.engine_demand_percent_torque', 'Engine Demand – Percent Torque', 8, 1, 1, '(value*scale) -125', TRUE),
('0CF00300', '61443.558', 'can_bus.accelerator_pedal_1_low_idle_switch', 'Accelerator Pedal 1 Low Idle Switch', 1, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('0CF00300', '61443.559', 'can_bus.accelerator_pedal_kickdown_switch', 'Accelerator Pedal Kickdown Switch', 1, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('0CF00300', '61443.1437', 'can_bus.road_speed_limit_status', 'Road Speed Limit Status', 1, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('0CF00300', '61443.2970', 'can_bus.accelerator_pedal_2_low_idle_switch', 'Accelerator Pedal 2 Low Idle Switch', 1, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('0CF00300', '61443.91', 'can_bus.accelerator_pedal_1_position', 'Accelerator Pedal 1 Position', 2, 1, 0.4, '(value*scale) - 0', TRUE),
('0CF00300', '61443.92', 'can_bus.engine_percent_load_at_current_speed', 'Engine Percent Load At Current Speed', 3, 1, 1, '(value*scale) - 0', TRUE),
('0CF00300', '61443.974', 'can_bus.remote_accelerator_pedal_position', 'Remote Accelerator Pedal Position', 4, 1, 0.4, '(value*scale) - 0', TRUE),
('0CF00300', '61443.29', 'can_bus.accelerator_pedal_2_position', 'Accelerator Pedal 2 Position', 5, 1, 0.4, '(value*scale) - 0', TRUE),
('0CF00300', '61443.2979', 'can_bus.vehicle_acceleration_rate_limit_status', 'Vehicle Acceleration Rate Limit Status', 6, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('0CF00300', '61443.5021', 'can_bus.momentary_engine_maximum_power_enable_feedback', 'Momentary Engine Maximum Power Enable Feedback', 6, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('0CF00300', '61443.5399', 'can_bus.dpf_thermal_management_active', 'DPF Thermal Management Active', 6, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('0CF00300', '61443.5400', 'can_bus.scr_thermal_management_active', 'SCR Thermal Management Active', 6, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('0CF00300', '61443.3357', 'can_bus.actual_maximum_available_engine_percent_torque', 'Actual Maximum Available Engine Percent Torque', 7, 1, 0.4, '(value*scale) - 0', TRUE),
('0CF00300', '61443.5398', 'can_bus.estimated_pumping_percent_torque', 'Estimated Pumping Percent Torque', 8, 1, 1, '(value*scale) -125', TRUE),
('18FEF111', '65265.69', 'can_bus.two_speed_axle_switch', 'Two Speed Axle Switch', 1, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('18FEF111', '65265.70', 'can_bus.parking_brake_switch', 'Parking Brake Switch', 1, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('18FEF111', '65265.1633', 'can_bus.cruise_control_pause_switch', 'Cruise Control Pause Switch', 1, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('18FEF111', '65265.3807', 'can_bus.park_brake_release_inhibit_request', 'Park Brake Release Inhibit Request', 1, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('18FEF111', '65265.84', 'can_bus.wheel_based_vehicle_speed', 'Wheel-Based Vehicle Speed', 2, 2, 0.00390625, '(value*scale) - 0', TRUE),
('18FEF111', '65265.595', 'can_bus.cruise_control_active', 'Cruise Control Active', 4, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('18FEF111', '65265.596', 'can_bus.cruise_control_enable_switch', 'Cruise Control Enable Switch', 4, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('18FEF111', '65265.597', 'can_bus.brake_switch', 'Brake Switch', 4, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('18FEF111', '65265.598', 'can_bus.clutch_switch', 'Clutch Switch', 4, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('18FEF111', '65265.599', 'can_bus.cruise_control_set_switch', 'Cruise Control Set Switch', 5, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('18FEF111', '65265.600', 'can_bus.cruise_control_coast_decelerate_switch', 'Cruise Control Coast (Decelerate) Switch', 5, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('18FEF111', '65265.601', 'can_bus.cruise_control_resume_switch', 'Cruise Control Resume Switch', 5, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('18FEF111', '65265.602', 'can_bus.cruise_control_accelerate_switch', 'Cruise Control Accelerate Switch', 5, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('18FEF111', '65265.86', 'can_bus.cruise_control_set_speed', 'Cruise Control Set Speed', 6, 1, 1, '(value*scale) - 0', TRUE),
('18FEF111', '65265.976', 'can_bus.pto_governor_state', 'PTO Governor State', 7, 1, 1, '(value >> (1 - 1)) & 31', FALSE),
('18FEF111', '65265.527', 'can_bus.cruise_control_states', 'Cruise Control States', 7, 1, 1, '(value >> (6 - 1)) & 7', FALSE),
('18FEF111', '65265.968', 'can_bus.engine_idle_increment_switch', 'Engine Idle Increment Switch', 8, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('18FEF111', '65265.967', 'can_bus.engine_idle_decrement_switch', 'Engine Idle Decrement Switch', 8, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('18FEF111', '65265.966', 'can_bus.engine_diagnostic_test_mode_switch', 'Engine Diagnostic Test Mode Switch', 8, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('18FEF111', '65265.1237', 'can_bus.engine_shutdown_override_switch', 'Engine Shutdown Override Switch', 8, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('0CF00A00', '61450.2659', 'can_bus.engine_exhaust_gas_recirculation_1_mass_flow_rate', 'Engine Exhaust Gas Recirculation 1 Mass Flow Rate', 1, 2, 0.05, '(value*scale) - 0', TRUE),
('0CF00A00', '61450.132', 'can_bus.engine_intake_air_mass_flow_rate', 'Engine Intake Air Mass Flow Rate', 3, 2, 0.05, '(value*scale) - 0', TRUE),
('0CF00A00', '61450.5257', 'can_bus.engine_exhaust_gas_recirculation_2_mass_flow_rate', 'Engine Exhaust Gas Recirculation 2 Mass Flow Rate', 5, 2, 0.05, '(value*scale) - 0', TRUE),
('0CF00A00', '61450.12758', 'can_bus.target_fresh_air_mass_flow', 'Target Fresh Air Mass Flow', 7, 2, 0.05, '(value*scale) - 0', TRUE),
('18F0010B', '61441.561', 'can_bus.asr_engine_control_active', 'ASR Engine Control Active', 1, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('18F0010B', '61441.562', 'can_bus.asr_brake_control_active', 'ASR Brake Control Active', 1, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('18F0010B', '61441.563', 'can_bus.anti_lock_braking_abs_active', 'Anti-Lock Braking (ABS) Active', 1, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('18F0010B', '61441.1121', 'can_bus.ebs_brake_switch', 'EBS Brake Switch', 1, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('18F0010B', '61441.521', 'can_bus.brake_pedal_position', 'Brake Pedal Position', 2, 1, 0.4, '(value*scale) - 0', TRUE),
('18F0010B', '61441.575', 'can_bus.abs_off_road_switch', 'ABS Off-road Switch', 3, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('18F0010B', '61441.576', 'can_bus.asr_off_road_switch', 'ASR Off-road Switch', 3, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('18F0010B', '61441.577', 'can_bus.asr_hill_holder_switch', 'ASR "Hill Holder" Switch', 3, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('18F0010B', '61441.1238', 'can_bus.traction_control_override_switch', 'Traction Control Override Switch', 3, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('18F0010B', '61441.972', 'can_bus.accelerator_interlock_switch', 'Accelerator Interlock Switch', 4, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('18F0010B', '61441.971', 'can_bus.engine_derate_switch', 'Engine Derate Switch', 4, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('18F0010B', '61441.970', 'can_bus.engine_auxiliary_shutdown_switch', 'Engine Auxiliary Shutdown Switch', 4, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('18F0010B', '61441.969', 'can_bus.remote_accelerator_enable_switch', 'Remote Accelerator Enable Switch', 4, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('18F0010B', '61441.973', 'can_bus.engine_retarder_selection', 'Engine Retarder Selection', 5, 1, 0.4, '(value*scale) - 0', TRUE),
('18F0010B', '61441.1243', 'can_bus.abs_fully_operational', 'ABS Fully Operational', 6, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('18F0010B', '61441.1439', 'can_bus.ebs_red_warning_signal', 'EBS Red Warning Signal', 6, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('18F0010B', '61441.1438', 'can_bus.abs_ebs_amber_warning_signal_powered_vehicle', 'ABS/EBS Amber Warning Signal (Powered Vehicle)', 6, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('18F0010B', '61441.1793', 'can_bus.atc_asr_information_signal', 'ATC/ASR Information Signal', 6, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('18F0010B', '61441.1481', 'can_bus.source_address_of_controlling_device_for_brake_control', 'Source Address of Controlling Device for Brake Control', 7, 1, 1, '(value*scale) - 0', TRUE),
('18F0010B', '61441.7941', 'can_bus.railroad_mode_switch', 'Railroad Mode Switch', 8, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('18F0010B', '61441.2911', 'can_bus.halt_brake_switch', 'Halt Brake Switch', 8, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('18F0010B', '61441.1836', 'can_bus.trailer_abs_status', 'Trailer ABS Status', 8, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('18F0010B', '61441.1792', 'can_bus.tractor_mounted_trailer_abs_warning_signal', 'Tractor-Mounted Trailer ABS Warning Signal', 8, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('18FEF803', '65272.123', 'can_bus.transmission_clutch_1_pressure', 'Transmission Clutch 1 Pressure', 1, 1, 16, '(value*scale) - 0', TRUE),
('18FEF803', '65272.124', 'can_bus.transmission_oil_level_1', 'Transmission Oil Level 1', 2, 1, 0.4, '(value*scale) - 0', TRUE),
('18FEF803', '65272.126', 'can_bus.transmission_filter_differential_pressure', 'Transmission Filter Differential Pressure', 3, 1, 2, '(value*scale) - 0', TRUE),
('18FEF803', '65272.127', 'can_bus.transmission_1_oil_pressure', 'Transmission 1 Oil Pressure', 4, 1, 16, '(value*scale) - 0', TRUE),
('18FEF803', '65272.177', 'can_bus.transmission_1_oil_temperature_1', 'Transmission 1 Oil Temperature 1', 5, 2, 0.03125, '(value*scale) -273', TRUE),
('18FEF803', '65272.3027', 'can_bus.transmission_oil_level_1_high_low', 'Transmission Oil Level 1 High / Low', 7, 1, 0.5, '(value*scale) -62.5', TRUE),
('18FEF803', '65272.3028', 'can_bus.transmission_oil_level_1_countdown_timer', 'Transmission Oil Level 1 Countdown Timer', 8, 1, 1, '(value >> (1 - 1)) & 15', FALSE),
('18FEF803', '65272.3026', 'can_bus.transmission_oil_level_1_measurement_status', 'Transmission Oil Level 1 Measurement Status', 8, 1, 1, '(value >> (5 - 1)) & 15', FALSE),
('18FEAE11', '65198.46', 'can_bus.pneumatic_supply_pressure', 'Pneumatic Supply Pressure', 1, 1, 8, '(value*scale) - 0', TRUE),
('18FEAE11', '65198.1086', 'can_bus.parking_and_or_trailer_air_pressure', 'Parking and/or Trailer Air Pressure', 2, 1, 8, '(value*scale) - 0', TRUE),
('18FEAE11', '65198.1087', 'can_bus.service_brake_circuit_1_air_pressure', 'Service Brake Circuit 1 Air Pressure', 3, 1, 8, '(value*scale) - 0', TRUE),
('18FEAE11', '65198.1088', 'can_bus.service_brake_circuit_2_air_pressure', 'Service Brake Circuit 2 Air Pressure', 4, 1, 8, '(value*scale) - 0', TRUE),
('18FEAE11', '65198.1089', 'can_bus.auxiliary_equipment_supply_pressure', 'Auxiliary Equipment Supply Pressure', 5, 1, 8, '(value*scale) - 0', TRUE),
('18FEAE11', '65198.1090', 'can_bus.air_suspension_supply_pressure_1', 'Air Suspension Supply Pressure 1', 6, 1, 8, '(value*scale) - 0', TRUE),
('18FEAE11', '65198.1351', 'can_bus.air_compressor_status', 'Air Compressor Status', 7, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('18FEAE11', '65198.6305', 'can_bus.powertrain_circuit_air_supply_pressure', 'Powertrain Circuit Air Supply Pressure', 8, 1, 8, '(value*scale) - 0', TRUE),
('18F00E00', '61454.3216', 'can_bus.engine_exhaust_1_n_ox_1', 'Engine Exhaust 1 NOx 1', 1, 2, 0.05, '(value*scale) -200', TRUE),
('18F00E00', '61454.3217', 'can_bus.engine_exhaust_1_percent_oxygen_1', 'Engine Exhaust 1 Percent Oxygen 1', 3, 2, 0.000514, '(value*scale) -12', TRUE),
('18F00E00', '61454.3218', 'can_bus.engine_exhaust_1_gas_sensor_1_power_in_range', 'Engine Exhaust 1 Gas Sensor 1 Power In Range', 5, 1, 1, '(value >> (1 - 1)) & 3', FALSE),
('18F00E00', '61454.3219', 'can_bus.engine_exhaust_1_gas_sensor_1_at_temperature', 'Engine Exhaust 1 Gas Sensor 1 at Temperature', 5, 1, 1, '(value >> (3 - 1)) & 3', FALSE),
('18F00E00', '61454.3220', 'can_bus.engine_exhaust_1_n_ox_1_reading_stable', 'Engine Exhaust 1 NOx 1 Reading Stable', 5, 1, 1, '(value >> (5 - 1)) & 3', FALSE),
('18F00E00', '61454.3221', 'can_bus.engine_exhaust_1_wide_range_percent_oxygen_1_reading_stable', 'Engine Exhaust 1 Wide-Range Percent Oxygen 1 Reading Stable', 5, 1, 1, '(value >> (7 - 1)) & 3', FALSE),
('18F00E00', '61454.3222', 'can_bus.engine_exhaust_1_gas_sensor_1_heater_preliminary_fmi', 'Engine Exhaust 1 Gas Sensor 1 Heater Preliminary FMI', 6, 1, 1, '(value >> (1 - 1)) & 31', FALSE),
('18F00E00', '61454.3223', 'can_bus.engine_exhaust_1_gas_sensor_1_heater_control', 'Engine Exhaust 1 Gas Sensor 1 Heater Control', 6, 1, 1, '(value >> (6 - 1)) & 3', FALSE),
('18F00E00', '61454.3224', 'can_bus.engine_exhaust_1_n_ox_sensor_1_preliminary_fmi', 'Engine Exhaust 1 NOx Sensor 1 Preliminary FMI', 7, 1, 1, '(value >> (1 - 1)) & 31', FALSE),
('18F00E00', '61454.5714', 'can_bus.engine_exhaust_1_n_ox_sensor_1_self_diagnosis_status', 'Engine Exhaust 1 NOx Sensor 1 Self-diagnosis Status', 7, 1, 1, '(value >> (6 - 1)) & 7', FALSE),
('18F00E00', '61454.3225', 'can_bus.engine_exhaust_1_oxygen_sensor_1_preliminary_fmi', 'Engine Exhaust 1 Oxygen Sensor 1 Preliminary FMI', 8, 1, 1, '(value >> (1 - 1)) & 31', FALSE),
('18FD0900', '64777.5053', 'can_bus.engine_trip_fuel_high_resolution', 'Engine Trip Fuel (High Resolution)', 1, 4, 0.001, '(value*scale) - 0', TRUE),
('18FD0900', '64777.5054', 'can_bus.engine_total_fuel_used_high_resolution', 'Engine Total Fuel Used (High Resolution)', 5, 4, 0.001, '(value*scale) - 0', TRUE),
('14FD3E00', '64830.4360', 'can_bus.aftertreatment_1_scr_intake_temperature', 'Aftertreatment 1 SCR Intake Temperature', 1, 2, 0.03125, '(value*scale) -273', TRUE),
('14FD3E00', '64830.4361', 'can_bus.aftertreatment_1_scr_intake_temperature_preliminary_fmi', 'Aftertreatment 1 SCR Intake Temperature Preliminary FMI', 3, 1, 1, '(value >> (1 - 1)) & 31', FALSE),
('14FD3E00', '64830.4363', 'can_bus.aftertreatment_1_scr_outlet_temperature', 'Aftertreatment 1 SCR Outlet Temperature', 4, 2, 0.03125, '(value*scale) -273', TRUE),
('14FD3E00', '64830.4362', 'can_bus.aftertreatment_1_scr_outlet_temperature_preliminary_fmi', 'Aftertreatment 1 SCR Outlet Temperature Preliminary FMI', 6, 1, 1, '(value >> (1 - 1)) & 31', FALSE),
('18FE700B', '65136.1585', 'can_bus.powered_vehicle_weight', 'Powered Vehicle Weight', 1, 2, 10, '(value*scale) - 0', TRUE),
('18FE700B', '65136.1760', 'can_bus.gross_combination_vehicle_weight', 'Gross Combination Vehicle Weight', 3, 2, 10, '(value*scale) - 0', TRUE),
('18FE700B', '65136.21792', 'can_bus.gross_combination_vehicle_weight_confidence', 'Gross Combination Vehicle Weight Confidence', 5, 1, 0.5, '(value*scale) - 0', TRUE),
('18FEC111', '65217.917', 'can_bus.total_vehicle_distance_high_resolution', 'Total Vehicle Distance (High Resolution)', 1, 4, 5, '(value*scale) - 0', TRUE),
('18FEC111', '65217.918', 'can_bus.trip_distance_high_resolution', 'Trip Distance (High Resolution)', 5, 4, 5, '(value*scale) - 0', TRUE),
('18FEE617', '65254.959', 'can_bus.seconds', 'Seconds', 1, 1, 0.25, '(value*scale) - 0', TRUE),
('18FEE617', '65254.960', 'can_bus.minutes', 'Minutes', 2, 1, 1, '(value*scale) - 0', TRUE),
('18FEE617', '65254.961', 'can_bus.hours', 'Hours', 3, 1, 1, '(value*scale) - 0', TRUE),
('18FEE617', '65254.963', 'can_bus.month', 'Month', 4, 1, 1, '(value*scale) - 0', TRUE),
('18FEE617', '65254.962', 'can_bus.day', 'Day', 5, 1, 0.25, '(value*scale) - 0', TRUE),
('18FEE617', '65254.964', 'can_bus.year', 'Year', 6, 1, 1, '(value*scale) + 1985', TRUE),
('18FEE617', '65254.1601', 'can_bus.local_minute_offset', 'Local Minute Offset', 7, 1, 1, '(value*scale) -125', TRUE),
('18FEE617', '65254.1602', 'can_bus.local_hour_offset', 'Local Hour Offset', 8, 1, 1, '(value*scale) -125', TRUE),
('18FEF511', '65269.108', 'can_bus.barometric_pressure', 'Barometric Pressure', 1, 1, 0.5, '(value*scale) - 0', TRUE),
('18FEF511', '65269.170', 'can_bus.cab_interior_temperature', 'Cab Interior Temperature', 2, 2, 0.03125, '(value*scale) -273', TRUE),
('18FEF511', '65269.171', 'can_bus.ambient_air_temperature', 'Ambient Air Temperature', 4, 2, 0.03125, '(value*scale) -273', TRUE),
('18FEF511', '65269.172', 'can_bus.engine_intake_1_air_temperature', 'Engine Intake 1 Air Temperature', 6, 1, 1, '(value*scale) -40', TRUE),
('18FEF511', '65269.79', 'can_bus.road_surface_temperature', 'Road Surface Temperature', 7, 2, 0.03125, '(value*scale) -273', TRUE),
('18FEF600', '65270.81', 'can_bus.aftertreatment_1_diesel_particulate_filter_intake_pressure', 'Aftertreatment 1 Diesel Particulate Filter Intake Pressure', 1, 1, 0.5, '(value*scale) - 0', TRUE),
('18FEF600', '65270.102', 'can_bus.engine_intake_manifold_1_pressure', 'Engine Intake Manifold #1 Pressure', 2, 1, 2, '(value*scale) - 0', TRUE),
('18FEF600', '65270.105', 'can_bus.engine_intake_manifold_1_temperature', 'Engine Intake Manifold 1 Temperature', 3, 1, 1, '(value*scale) -40', TRUE),
('18FEF600', '65270.106', 'can_bus.engine_intake_air_pressure', 'Engine Intake Air Pressure', 4, 1, 2, '(value*scale) - 0', TRUE),
('18FEF600', '65270.107', 'can_bus.engine_air_filter_1_differential_pressure', 'Engine Air Filter 1 Differential Pressure', 5, 1, 0.05, '(value*scale) - 0', TRUE),
('18FEF600', '65270.173', 'can_bus.engine_exhaust_temperature', 'Engine Exhaust Temperature', 6, 2, 0.03125, '(value*scale) -273', TRUE),
('18FEF600', '65270.112', 'can_bus.engine_coolant_filter_differential_pressure', 'Engine Coolant Filter Differential Pressure', 8, 1, 0.5, '(value*scale) - 0', TRUE)
ON CONFLICT (can_id_code, pgn_spn_code) DO UPDATE SET label = excluded.label;


COMMIT;