begin;

-- Before Deployment
-- -- Backup table uis_PERMISSION_CATEGORIES, uis_PERMISSIONS, uis_permission_groups, uis_permission_group_rights

-- Remove Permission Category
update "uis_PERMISSION_CATEGORIES" set description = 'DEPRECATED';

-- add client package column
ALTER TABLE "uis_PERMISSION_CATEGORIES" 
ADD COLUMN client_package_code VARCHAR(40);

ALTER TABLE "uis_PERMISSION_CATEGORIES"
ADD CONSTRAINT fk_uis_permission_category_clients_package foreign key (client_package_code) references "uis_CLIENT_PACKAGES" (code);

-- add 5 new categories
INSERT INTO public."uis_PERMISSION_CATEGORIES" (
	code, title, subtitle, icon, "label", description, display_sequence, client_package_code
) VALUES
	('PLATFORM', 'Platform', 'Platformdashboards', 'assets.svg', 'Platform', '-', 1, 'CORE_MODULE'),
	('TYREOPTIMAX', 'TyreOptimax', 'TyreOptimax', 'assets.svg', 'TyreOptimax', '-', 3, 'TYRE_OPTIMAX'),
    ('FLEETOPTIMAX', 'FleetOptimax', 'FleetOptimax', 'assets.svg', 'FleetOptimax', '-', 4, 'FLEET_OPTIMAX'),
	('ASSET', 'Asset', 'Asset', 'assets.svg', 'Asset', '-', 5, 'GENERAL_ASSETFINDR'),
	('WORKSHOPOPTIMAX', 'WorkshopOptimax', 'WorkshopOptimax', 'assets.svg', 'WorkshopOptimax', '-', 6, 'WORKSHOP_OPTIMAX'),
	('DIGISPECT', 'Digispect', 'Digispect', 'assets.svg', 'Digispect', '-', 7, 'DIGISPECT_BASIC')
ON CONFLICT (code) DO NOTHING;

-- update work order permission category as it is already exist
UPDATE "uis_PERMISSION_CATEGORIES"
SET
  title = 'Work Order',
  subtitle = 'Work Order',
  icon = 'assets.svg',
  label = 'Work Order',
  description = '-',
  display_sequence = 2,
  client_package_code = 'GENERAL_ASSETFINDR'
WHERE
  code = 'WORK_ORDER';
	

-- Create 17 new view permissions for each categories
INSERT INTO "uis_PERMISSIONS" (
	code,
	user_permission_category_code,
	"label",
	description,
	display_sequence
) VALUES
	('VIEW_DASHBOARD_MENU', 'PLATFORM', 'View Dashboard Menu', 'Permission to view dashboard', 0),
	('VIEW_WORK_ORDER_MENU', 'WORK_ORDER', 'View Work Order Menu', 'Permission to view work order', 0),
	('VIEW_TYRE_INSPECTION_MENU', 'TYREOPTIMAX', 'View Tyre Inspection Menu', 'Permission to view tyre inspection', 0),
	('VIEW_FLEET_ASSET_LIST', 'FLEETOPTIMAX', 'View Fleet Asset Menu', 'Permission to view fleet asset', 0),
	('VIEW_TYRES_LIST_MENU', 'TYREOPTIMAX', 'View Tyres List Menu', 'Permission to view tyres list', 0),
	('VIEW_LINKED_TYRES_MENU', 'TYREOPTIMAX', 'View Linked Tyres Menu', 'Permission to view linked tyres', 0),
	('VIEW_USERS_MENU', 'TYREOPTIMAX', 'View Users Menu', 'Permission to view users', 0),
	('VIEW_APPROVALS_MENU', 'PLATFORM', 'View Approvals Menu', 'Permission to view approvals', 0),
	('VIEW_UPLOADS_MENU', 'PLATFORM', 'View Uploads Menu', 'Permission to view uploads', 0),
	('VIEW_ASSET_MANAGEMENT_MENU', 'ASSET', 'View Asset Management Menu', 'Permission to view asset management', 0),
	('VIEW_WORKSHOP_CUSTOMER_MENU', 'WORKSHOPOPTIMAX', 'View Workshop Customer Menu', 'Permission to view workshop customer', 0),
	('VIEW_WORKSHOP_VEHICLE_MENU', 'WORKSHOPOPTIMAX', 'View Workshop Vehicle Menu', 'Permission to view workshop vehicle', 0),
	('VIEW_WORKSHOP_PART_SERVICE_MENU', 'WORKSHOPOPTIMAX', 'View Workshop Part Service Menu', 'Permission to view workshop part service', 0),
	('VIEW_WORKSHOP_INVOICE_PAYMENT_MENU', 'WORKSHOPOPTIMAX', 'View Workshop Invoice Payment Menu', 'Permission to view workshop invoice payment', 0),
	('VIEW_WORKSHOP_WORK_ORDER_MENU', 'WORKSHOPOPTIMAX', 'View Workshop Work Order Menu', 'Permission to view workshop work order', 0),
	('VIEW_WORKSHOP_WORK_ORDER_TODO_MENU', 'WORKSHOPOPTIMAX', 'View Workshop Work Order Todo Menu', 'Permission to view workshop work order todo', 0),
	('VIEW_WORKSHOP_MEMBERSHIP_MENU', 'WORKSHOPOPTIMAX', 'View Workshop Membership Menu', 'Permission to view workshop membership', 0),
	('VIEW_ALL_DIGISPECT_INSPECTIONS', 'DIGISPECT', 'View All Digispect Inspections', 'Permission to view all Digispect inspections.', 0)
ON CONFLICT (code) DO NOTHING;

-- Delete dashboard permission

update "uis_PERMISSIONS" set description = 'DEPRECATED' WHERE user_permission_category_code = 'DASHBOARD';

-- mapping current permission to new category

UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'TYREOPTIMAX' WHERE user_permission_category_code = 'INSPECTIONS';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'ASSET' WHERE user_permission_category_code = 'ASSET_VEHICLES';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'TYREOPTIMAX' WHERE user_permission_category_code = 'ASSET_TYRES';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'TYREOPTIMAX' WHERE user_permission_category_code = 'LINKED_TYRES';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'PLATFORM' WHERE user_permission_category_code = 'USERS';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'PLATFORM' WHERE user_permission_category_code = 'APPROVALS';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'PLATFORM' WHERE user_permission_category_code = 'UPLOADS';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'ASSET' WHERE user_permission_category_code = 'ASSET_MANAGEMENT';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'WORKSHOPOPTIMAX' WHERE user_permission_category_code = 'WORKSHOP_CUSTOMER';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'WORKSHOPOPTIMAX' WHERE user_permission_category_code = 'WORKSHOP_VEHICLE';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'WORKSHOPOPTIMAX' WHERE user_permission_category_code = 'WORKSHOP_PART_SERVICE';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'WORKSHOPOPTIMAX' WHERE user_permission_category_code = 'WORKSHOP_INVOICE_PAYMENT';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'WORKSHOPOPTIMAX' WHERE user_permission_category_code = 'WORKSHOP_WORK_ORDER';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'WORKSHOPOPTIMAX' WHERE user_permission_category_code = 'WORKSHOP_WORK_ORDER_TODO';
UPDATE "uis_PERMISSIONS" SET user_permission_category_code = 'WORKSHOPOPTIMAX' WHERE user_permission_category_code = 'WORKSHOP_MEMBERSHIP';

-- delete all package group right
delete from "uis_permission_group_rights";

-- create administrator user group right for each package group 
INSERT INTO public.uis_permission_group_rights (
    id,
    permission_group_id,
    permission_codes,
    is_enable,
    client_id,
    created_at,
    updated_at,
    permission_category_code,
    created_by,
    updated_by
)
SELECT
    'pgr_' || gen_random_uuid() AS id,  -- or use uuid_generate_v4() depending on your DB
    pg.id AS permission_group_id,
    rights.permission_codes,
    true AS is_enable,
    pg.client_id,
    now() AS created_at,
    now() AS updated_at,
    rights.permission_category_code,
    pg.created_by AS created_by,
    pg.updated_by AS updated_by
FROM public.uis_permission_groups pg
CROSS JOIN (
    -- Define default permission rights to insert for each group
    SELECT 
        ARRAY['VIEW_DASHBOARD_MENU', 'VIEW_APPROVALS_MENU', 'VIEW_UPLOADS_MENU', 'SCRAP_TYRE', 'VIEW_USERS_MENU', 'DISPOSE_TYRE', 'UPLOAD_TYRES', 'UPLOAD_VEHICLES', 'UPLOAD_LINKED_TYRES', 'BONUS_PENALTY_TYRE', 'ASSET_MEMBERSHIP']::varchar[] AS permission_codes,
        'PLATFORM'::varchar AS permission_category_code
    UNION ALL
    SELECT 
        ARRAY['VIEW_WORK_ORDER_MENU', 'CREATE_TICKET', 'EDIT_TICKET', 'CHANGE_TICKET_STATUS', 'CLOSE_TICKET', 'WORK_ORDER_ADMIN', 'VIEW_ALL_LIST_TICKET', 'CHANGE_REQUESTOR_TICKET', 'MANAGE_TICKET_FLOW']::varchar[] AS permission_codes,
        'WORK_ORDER'::varchar AS permission_category_code
    UNION ALL
    SELECT 
        ARRAY['VIEW_TYRE_INSPECTION_MENU', 'VIEW_TYRES_LIST_MENU', 'VIEW_LINKED_TYRES_MENU', 'ADD_INSPECTION', 'ADD_TYRES', 'EDIT_TYRES', 'LINK_LINKED_TYRES', 'UNLINK_LINKED_TYRES', 'REPLACE_LINKED_TYRES']::varchar[] AS permission_codes,
        'TYREOPTIMAX'::varchar AS permission_category_code
    UNION ALL
    SELECT 
        ARRAY['VIEW_FLEET_ASSET_LIST']::varchar[] AS permission_codes,
        'FLEETOPTIMAX'::varchar AS permission_category_code
    UNION ALL
    SELECT 
        ARRAY['VIEW_ASSET_MANAGEMENT_MENU', 'ADD_VEHICLES', 'EDIT_VEHICLES', 'ADD_ASSETS', 'EDIT_ASSETS', 'VIEW_ASSET_PURCHASES', 'ADD_ASSET_PURCHASES', 'UPDATE_ASSET_PURCHASES', 'VIEW_ASSET_RENT', 'ADD_ASSET_RENT', 'UPDATE_ASSET_RENT', 'VIEW_ASSET_WARRANTIES', 'ADD_ASSET_WARRANTIES', 'UPDATE_ASSET_WARRANTIES', 'VIEW_ASSET_INSURANCE', 'ADD_ASSET_INSURANCE', 'UPDATE_ASSET_INSURANCE', 'VIEW_LINKED_ASSET', 'ADD_LINKED__ASSET', 'UPDATE_LINKED_ASSET', 'VIEW_ALL_LIST_ASSET', 'TRIGGER_COMMAND_ASSET']::varchar[] AS permission_codes,
        'ASSET'::varchar AS permission_category_code
    UNION ALL
    SELECT 
        ARRAY['VIEW_WORKSHOP_CUSTOMER_MENU', 'VIEW_WORKSHOP_VEHICLE_MENU', 'VIEW_WORKSHOP_PART_SERVICE_MENU', 'VIEW_WORKSHOP_INVOICE_PAYMENT_MENU', 'VIEW_WORKSHOP_WORK_ORDER_MENU', 'VIEW_WORKSHOP_WORK_ORDER_TODO_MENU', 'VIEW_WORKSHOP_MEMBERSHIP_MENU', 'ADD_WORKSHOP_CUSTOMER', 'EDIT_WORKSHOP_CUSTOMER', 'ADD_WORKSHOP_VEHICLES', 'EDIT_WORKSHOP_VEHICLES', 'ADD_WORKSHOP_MEMBERSHIP', 'ADD_WORKSHOP_PACKAGE', 'EDIT_WORKSHOP_PACKAGE', 'CREATE_WORKSHOP_TICKET', 'EDIT_WORKSHOP_TICKET', 'CLOSE_WORKSHOP_TICKET', 'WORK_ORDER_WORKSHOP_ADMIN']::varchar[] AS permission_codes,
        'WORKSHOPOPTIMAX'::varchar AS permission_category_code
    UNION ALL
    SELECT 
        ARRAY['VIEW_ALL_DIGISPECT_INSPECTIONS']::varchar[] AS permission_codes,
        'DIGISPECT'::varchar AS permission_category_code
) AS rights;

-- On deployment
-- -- List client that need non admin package group
-- -- update package group to become non admin

commit;