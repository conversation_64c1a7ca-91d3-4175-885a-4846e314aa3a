BEGIN;
CREATE TABLE IF NOT EXISTS "inv_ORDER_STATUSES" (
    code VARCHAR(20) PRIMARY KEY,
    label VARCHAR(20) NOT NULL,
    description VARCHAR(50) NOT NULL
);

INSERT INTO
    "inv_ORDER_STATUSES" (code, "label", description)
VALUES
    ('OPEN', 'Open', '-'),
    ('COMPLETED', 'Completed', '-'),
    ('INVOICED', 'Invoiced', '-'),
    ('CLOSED', 'Closed', '-') ON CONFLICT (code) DO NOTHING;

CREATE TABLE
    "inv_ORDER_REFERENCES" (
        "code" character varying(255) NOT NULL,
        "description" TEXT NOT NULL,
        "label" character varying(255) NOT NULL,
        PRIMARY KEY ("code")
    );

INSERT INTO
    "inv_ORDER_REFERENCES" (code, description, "label")
VALUES
    ('WORK_ORDER', '-', 'Work Order');

CREATE TABLE IF NOT EXISTS inv_orders (
    id VARCHAR(40) PRIMARY KEY,
    order_number VARCHAR(30) NOT NULL,
    order_date TIMESTAMPTZ NOT NULL,
    due_date TIMESTAMPTZ,
    partner_id VARCHAR(40) NOT NULL,
    reference_code VARCHAR(100) REFERENCES "inv_ORDER_REFERENCES"(code) NOT NULL,
    source_reference_id VARCHAR(40) NOT NULL,
    total INT NOT NULL,
    grand_total INT NOT NULL,
    grand_total_discount_amount INT NOT NULL,
    grand_total_tax INT NOT NULL,
    notes TEXT NOT NULL,
    status_code VARCHAR(20) REFERENCES "inv_ORDER_STATUSES"(code) NOT NULL,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL
);

CREATE TABLE IF NOT EXISTS inv_order_items (
    id VARCHAR(40) PRIMARY KEY,
    order_id VARCHAR(40) REFERENCES inv_orders(id) NOT NULL,
    product_id VARCHAR(40) NOT NULL REFERENCES inv_products(id),
    product_name VARCHAR(100) NOT NULL,
    quantity INT NOT NULL,
    price INT NOT NULL,
    discount_amount INT NOT NULL,
    uom_code VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL
);


COMMIT;