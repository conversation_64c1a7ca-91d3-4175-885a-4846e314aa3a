BEGIN;

ALTER TABLE "inv_memberships"
ADD COLUMN IF NOT EXISTS "number" VARCHAR(20);

CREATE INDEX IF NOT EXISTS idx_inv_memberships_number ON inv_memberships (number) WHERE deleted_at IS NULL;

-- Populate Past Data
WITH cte AS (
  SELECT
    id,
    'MBS-' || 
    to_char(created_at, 'YYMMDD') || '-' ||
    row_number() over (partition by client_id order by created_at) AS new_number,
    "number"
  FROM
    inv_memberships tt
  WHERE
   tt.deleted_at IS NULL
)
UPDATE inv_memberships AS tt SET "number" = cte.new_number FROM cte WHERE tt.id = cte.id;

-- Create Trigger To Set New Number
CREATE OR REPLACE FUNCTION trigger_set_new_membership_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    'MBS-' || 
    to_char(created_at, 'YYMMDD') || '-' ||
    row_number() over (partition by client_id order by created_at) AS new_number,
    number
  FROM
    inv_memberships tt
  WHERE
   tt.deleted_at IS NULL 
   AND client_id = NEW.client_id
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE inv_memberships AS tt SET number = cte.new_number FROM cte WHERE tt.id = cte.id AND (cte.number IS NULL OR cte.number = '');
RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS set_number_after_insert_membership ON inv_memberships;

-- Define Trigger
CREATE TRIGGER set_number_after_insert_membership
  AFTER INSERT
  ON inv_memberships
  FOR EACH ROW
  EXECUTE PROCEDURE trigger_set_new_membership_number();

COMMIT;



