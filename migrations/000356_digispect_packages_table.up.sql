BEGIN;

INSERT INTO "uis_CLIENT_PACKAGES" (code, "label", description)
VALUES
    ('DIGISPECT_LITE', 'Digispect Lite', '-') ON CONFLICT (code) DO NOTHING;


CREATE TABLE
    IF NOT EXISTS "uis_DIGISPECT_PACKAGES" (
        "code" CHARACTER VARYING(40) NOT NULL PRIMARY KEY,
        "label" CHARACTER VARYING(40) NOT NULL,
        "description" CHARACTER VARYING(255),
        "rank" SMALLINT NOT NULL,
        "limit_days" SMALLINT NOT NULL
    );

INSERT INTO
    "uis_DIGISPECT_PACKAGES" (code, "label", description, rank, limit_days)
VALUES
    ('DIGISPECT_LITE', 'Digispect Lite', '-', 3, 31),
    ('DIGISPECT_PRO', 'Digispect Pro', '-', 2, 180),
    ('DIGISPECT_PRO_PLUS', 'Digispect Pro Plus', '-', 1, 365) ON CONFLICT (code) DO NOTHING;

COMMIT;