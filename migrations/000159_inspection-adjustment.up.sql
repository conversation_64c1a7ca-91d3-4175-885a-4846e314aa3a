BEGIN;

ALTER TABLE "ams_asset_inspection_tyre" 
ADD COLUMN IF NOT EXISTS rdt4 numeric(14, 2),
ALTER COLUMN asset_tyre_id DROP NOT NULL,
ALTER COLUMN asset_assignment_id DROP NOT NULL;

ALTER TABLE "ams_asset_inspections" 
ADD COLUMN IF NOT EXISTS inspection_number varchar(40);

CREATE INDEX IF NOT EXISTS idx_ams_asset_inspections_number ON ams_asset_inspections (inspection_number) WHERE deleted_at IS NULL;

-- Populate Past Data
WITH cte AS (
  SELECT
    id,
    'INS-' || 
    to_char(created_at, 'YYMMDD') || '-' ||
    lpad((row_number() over (partition by client_id, date_trunc('day',created_at) order by created_at))::text,4,'0') AS new_inspection_number,
    inspection_number
  FROM
    ams_asset_inspections tt
  WHERE
   tt.deleted_at IS NULL
)
UPDATE ams_asset_inspections AS tt SET inspection_number = cte.new_inspection_number FROM cte WHERE tt.id = cte.id;

-- Create Trigger To Set New Number
CREATE OR REPLACE FUNCTION trigger_set_new_inspection_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    'INS-' || 
    to_char(created_at, 'YYMMDD') || '-' ||
    lpad((row_number() over (partition BY NEW.client_id,  date_trunc('day',created_at) order by created_at))::text,4,'0') AS new_inspection_number,
    inspection_number
  FROM
    ams_asset_inspections tt
  WHERE
   tt.deleted_at IS NULL 
   AND client_id = NEW.client_id
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE ams_asset_inspections AS tt SET inspection_number = cte.new_inspection_number FROM cte WHERE tt.id = cte.id AND (cte.inspection_number IS NULL OR cte.inspection_number = '');
RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS set_number_after_insert_inspection ON ams_asset_inspections;

-- Define Trigger
CREATE TRIGGER set_number_after_insert_inspection
  AFTER INSERT
  ON ams_asset_inspections
  FOR EACH ROW
  EXECUTE PROCEDURE trigger_set_new_inspection_number();

COMMIT;