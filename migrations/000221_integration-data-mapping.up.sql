BEGIN;

-- Create the ins_integration_data_mapping_templates table
CREATE TABLE
    IF NOT EXISTS "ins_integration_data_mapping_templates" (
        code VARCHAR(255) PRIMARY KEY,
        label VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        data_mapping JSONB,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
        deleted_at TIMESTAMPTZ,
        updated_by VA<PERSON><PERSON><PERSON>(40),
        created_by VARCHAR(40)
    );

INSERT INTO
    "ins_integration_data_mapping_templates" (code, "label", description, data_mapping)
VALUES
    ('CUSTOM', 'Custom', 'Custom', NULL),
    ('FLESPI_TELTONIKA', 'Flespi Teltonika', 'Flespi Teltonika', '{
    "ident": "ident", 
    "time": "timestamp",
    "can_bus.can_abs_failure_indicator_status": "can.abs.failure.indicator.status",
    "can_bus.can_additional_front_lights_status": "can.additional.front.lights.status",
    "can_bus.can_additional_rear_lights_status": "can.additional.rear.lights.status",
    "can_bus.can_air_condition_status": "can.air.condition.status",
    "can_bus.can_airbag_indicator_status": "can.airbag.indicator.status",
    "can_bus.can_automatic_retarder_status": "can.automatic.retarder.status",
    "can_bus.can_battery_indicator_status": "can.battery.indicator.status",
    "can_bus.can_car_closed_remote_status": "can.car.closed.remote.status",
    "can_bus.can_car_closed_status": "can.car.closed.status",
    "can_bus.can_central_differential_4_hi_status": "can.central.differential.4hi.status",
    "can_bus.can_central_differential_4_lo_status": "can.central.differential.4lo.status",
    "can_bus.can_check_engine_indicator_status": "can.check.engine.indicator.status",
    "can_bus.can_cng_status": "can.cng.status",
    "can_bus.can_connection_state_1": "can.connection.state.1",
    "can_bus.can_connection_state_2": "can.connection.state.2",
    "can_bus.can_connection_state_3": "can.connection.state.3",
    "can_bus.can_coolant_level_low_indicator_status": "can.coolant.level.low.indicator.status",
    "can_bus.can_cruise_status": "can.cruise.status",
    "can_bus.can_drive_gear_status": "can.drive.gear.status",
    "can_bus.can_driver_seatbelt_indicator_status": "can.driver.seatbelt.indicator.status",
    "can_bus.can_driver_seatbelt_status": "can.driver.seatbelt.status",
    "can_bus.can_dynamic_ignition_status": "can.dynamic.ignition.status",
    "can_bus.can_electric_engine_status": "can.electric.engine.status",
    "can_bus.can_electronic_power_control_status": "can.electronic.power.control.status",
    "can_bus.can_engine_ignition_status": "can.engine.ignition.status",
    "can_bus.can_engine_load_level": "can.engine.load.level",
    "can_bus.can_engine_lock_status": "can.engine.lock.status",
    "can_bus.can_engine_motorhours": "can.engine.motorhours",
    "can_bus.can_engine_rpm": "can.engine.rpm",
    "can_bus.can_engine_temperature": "can.engine.temperature",
    "can_bus.can_engine_working_status": "can.engine.working.status",
    "can_bus.can_eps_indicator_status": "can.eps.indicator.status",
    "can_bus.can_esp_indicator_status": "can.esp.indicator.status",
    "can_bus.can_esp_status": "can.esp.status",
    "can_bus.can_factory_armed_status": "can.factory.armed.status",
    "can_bus.can_front_differential_status": "can.front.differential.status",
    "can_bus.can_front_fog_lights_status": "can.front.fog.lights.status",
    "can_bus.can_front_left_door_status": "can.front.left.door.status",
    "can_bus.can_front_passenger_seatbelt_status": "can.front.passenger.seatbelt.status",
    "can_bus.can_front_passenger_status": "can.front.passenger.status",
    "can_bus.can_front_right_door_status": "can.front.right.door.status",
    "can_bus.can_fuel_consumed": "can.fuel.consumed",
    "can_bus.can_fuel_level_low_indicator_status": "can.fuel.level.low.indicator.status",
    "can_bus.can_glow_plug_indicator_status": "can.glow.plug.indicator.status",
    "can_bus.can_handbrake_indicator_status": "can.handbrake.indicator.status",
    "can_bus.can_handbrake_status": "can.handbrake.status",
    "can_bus.can_high_beam_status": "can.high.beam.status",
    "can_bus.can_hood_status": "can.hood.status",
    "can_bus.can_ignition_key_status": "can.ignition.key.status",
    "can_bus.can_interlock_active": "can.interlock.active",
    "can_bus.can_light_signal_status": "can.light.signal.status",
    "can_bus.can_lights_failure_indicator_status": "can.lights.failure.indicator.status",
    "can_bus.can_lights_hazard_lights_status": "can.lights.hazard.lights.status",
    "can_bus.can_low_beam_status": "can.low.beam.status",
    "can_bus.can_maintenance_required_status": "can.maintenance.required.status",
    "can_bus.can_manual_retarder_status": "can.manual.retarder.status",
    "can_bus.can_module_sleep_mode": "can.module.sleep.mode",
    "can_bus.can_neutral_gear_status": "can.neutral.gear.status",
    "can_bus.can_oil_pressure_indicator_status": "can.oil.pressure.indicator.status",
    "can_bus.can_operator_present_status": "can.operator.present.status",
    "can_bus.can_parking_lights_status": "can.parking.lights.status",
    "can_bus.can_parking_status": "can.parking.status",
    "can_bus.can_passenger_seatbelt_indicator_status": "can.passenger.seatbelt.indicator.status",
    "can_bus.can_pedal_brake_status": "can.pedal.brake.status",
    "can_bus.can_pedal_clutch_status": "can.pedal.clutch.status",
    "can_bus.can_private_status": "can.private.status",
    "can_bus.can_program_id": "can.program.id",
    "can_bus.can_pto_status": "can.pto.status",
    "can_bus.can_ready_to_drive_indicator_status": "can.ready.to.drive.indicator.status",
    "can_bus.can_rear_central_passenger_seatbelt_status": "can.rear.central.passenger.seatbelt.status",
    "can_bus.can_rear_differential_status": "can.rear.differential.status",
    "can_bus.can_rear_fog_lights_status": "can.rear.fog.lights.status",
    "can_bus.can_rear_left_door_status": "can.rear.left.door.status",
    "can_bus.can_rear_left_passenger_seatbelt_status": "can.rear.left.passenger.seatbelt.status",
    "can_bus.can_rear_right_door_status": "can.rear.right.door.status",
    "can_bus.can_rear_right_passenger_seatbelt_status": "can.rear.right.passenger.seatbelt.status",
    "can_bus.can_reverse_gear_status": "can.reverse.gear.status",
    "can_bus.can_roof_opened_status": "can.roof.opened.status",
    "can_bus.can_soot_filter_indicator_status": "can.soot.filter.indicator.status",
    "can_bus.can_standalone_engine": "can.standalone.engine",
    "can_bus.can_stop_indicator_status": "can.stop.indicator.status",
    "can_bus.can_throttle_pedal_level": "can.throttle.pedal.level",
    "can_bus.can_tire_pressure_low_status": "can.tire.pressure.low.status",
    "can_bus.can_tracker_counted_fuel_consumed": "can.tracker.counted.fuel.consumed",
    "can_bus.can_tracker_counted_mileage": "can.tracker.counted.mileage",
    "can_bus.can_trailer_axle_lift_status_1": "can.trailer.axle.lift.status.1",
    "can_bus.can_trailer_axle_lift_status_2": "can.trailer.axle.lift.status.2",
    "can_bus.can_trip_engine_motorhours": "can.trip.engine.motorhours",
    "can_bus.can_trunk_status": "can.trunk.status",
    "can_bus.can_vehicle_battery_charging_status": "can.vehicle.battery.charging.status",
    "can_bus.can_vehicle_mileage": "can.vehicle.mileage",
    "can_bus.can_vehicle_speed": "can.vehicle.speed",
    "can_bus.can_warning_indicator_status": "can.warning.indicator.status",
    "can_bus.can_wear_brake_pads_indicator_status": "can.wear.brake.pads.indicator.status",
    "can_bus.can_webasto_status": "can.webasto.status",
    "can_bus.can_fuel_level": "can.fuel.level",
    "can_bus.can_engine_oil_level": "can.engine.oil.level",
    "gps.position_altitude": "position.altitude",
    "gps.position_direction": "position.direction",
    "gps.position_hdop": "position.hdop",
    "gps.position_latitude": "position.latitude",
    "gps.position_longitude": "position.longitude",
    "gps.position_pdop": "position.pdop",
    "gps.position_satellites": "position.satellites",
    "gps.position_speed": "position.speed",
    "gps.position_valid": "position.valid",
    "gps.vehicle_mileage": "vehicle.mileage",
    "general.battery_current": "battery.current",
    "general.battery_voltage": "battery.voltage",
    "general.gsm_signal_level": "gsm.signal.level",
    "general.movement_status": "movement.status",
    "general.external_powersource_voltage": "external.powersource.voltage"
}')
     ON CONFLICT (code) DO NOTHING;

ALTER TABLE "ins_integrations"
ADD COLUMN data_mapping_code VARCHAR(255) REFERENCES "ins_integration_data_mapping_templates" (code),
ADD COLUMN custom_data_mapping JSONB;


UPDATE ins_integrations SET custom_data_mapping = NULL, data_mapping_code = 'FLESPI_TELTONIKA' WHERE integration_target_type_code = 'FLESPI_TRACKING';

UPDATE
  ins_integrations
SET
  data_mapping_code = 'CUSTOM',
  custom_data_mapping = '{"ident": "deviceid", "time": "time", "compressor.press_run_time": "mod3", "compressor.press_load_time": "mod4", "compressor.press_run_state_1": "mod8", "compressor.press_run_state_2": "mod9", "compressor.press_machine_status": "cp1", "compressor.press_phase_a_current": "mod5", "compressor.press_phase_b_current": "mod6", "compressor.press_phase_c_current": "mod7", "compressor.press_air_feed_pressure": "mod1", "compressor.press_current_imbalance": "cp10", "compressor.press_lube_oil_used_time": "mod14", "compressor.press_air_filter_used_time": "mod13", "compressor.press_oil_filter_used_time": "mod11", "compressor.press_lube_grease_used_time": "mod15", "compressor.press_air_exhaust_temperature": "mod2", "compressor.press_oil_separator_used_time": "mod12"}'::jsonb
WHERE
  identifier_json = '{"device_id": "ZZ2EVE9FF"}'::jsonb;  

COMMIT;