BEGIN;

CREATE TABLE
    IF NOT EXISTS "ins_INTEGRATION_STATUSES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "ins_INTEGRATION_STATUSES" (code, "label", description)
VALUES
    ('ACTIVE', 'Active', '-'),
    ('INACTIVE', 'Inactive', '-'),
    ('DELETED', 'Deleted', '-') ON CONFLICT (code) DO NOTHING;

CREATE TABLE
    IF NOT EXISTS "ins_INTEGRATION_TARGET_TYPE" (
        code VARCHAR(255) PRIMARY KEY,
        label VARCHAR(255) NOT NULL,
        description VARCHAR(255) NOT NULL,
        integration_type_code VARCHAR(20) REFERENCES "ins_INTEGRATION_TYPE" (code),
        integration_target_code VARCHAR(20) REFERENCES "ins_INTEGRATION_TARGET" (code),
        identifier_json_template JSONB
    );

INSERT INTO
    "ins_INTEGRATION_TARGET_TYPE" (
        code,
        label,
        description,
        integration_type_code,
        integration_target_code,
        identifier_json_template
    )
VALUES
    ('GPS_ID_TRACKING', 'GPS.id Tracking', '-', 'TRACKING', 'GPS_ID', '{"imei":"string"}') ON CONFLICT (code) DO NOTHING;

CREATE TABLE
    IF NOT EXISTS "ins_integrations" (
        id VARCHAR(40) PRIMARY KEY,
        internal_reference_id VARCHAR(40) NOT NULL,
        integration_target_type_code VARCHAR(20) NOT NULL REFERENCES "ins_INTEGRATION_TARGET_TYPE" (code),
        identifier_json JSONB,
        integration_account_id VARCHAR(40) NOT NULL REFERENCES "ins_integration_account" (id),
        integration_target_code VARCHAR(20) NOT NULL REFERENCES "ins_INTEGRATION_TARGET" (code),
        status_code VARCHAR(20) NOT NULL REFERENCES "ins_INTEGRATION_STATUSES" (code),
        last_success_sync_time TIMESTAMPTZ,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL,
        client_id VARCHAR(40) NOT NULL
    );

COMMIT;