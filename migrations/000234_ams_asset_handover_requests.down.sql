BEGIN;

ALTER TABLE ams_asset_assignments
DROP COLUMN IF EXISTS unassigned_by_user_id,
DROP COLUMN IF EXISTS assigned_by_user_id;

-- Drop the ams_asset_handover_request_statuses table
DROP TABLE IF EXISTS ams_asset_handover_request_statuses;

-- Drop the ams_asset_handover_requests table
DROP TABLE IF EXISTS ams_asset_handover_requests;

-- Remove the added columns from the ams_assets table
ALTER TABLE ams_assets
DROP COLUMN IF EXISTS handover_form_template_id,
DROP COLUMN IF EXISTS handover_need_inspection;

COMMIT;