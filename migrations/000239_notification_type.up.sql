BEGIN;

-- Create the "nts_NOTIFICATION_TYPES" table
CREATE TABLE IF NOT EXISTS "nts_NOTIFICATION_TYPES" (
    code VARCHAR(50) PRIMARY KEY,
    label VARCHAR(50) NOT NULL,
    description TEXT
);

INSERT INTO
    "nts_NOTIFICATION_TYPES" (code, "label", description)
VALUES
    ('USER_ACTIVITY','User Activity', '-'),
    ('ALERT','Alert', '-'),
    ('REMINDER','Reminder', '-')
    ON CONFLICT (code) DO NOTHING;

-- Create the "nts_NOTIFICATION_TYPES" table
CREATE TABLE IF NOT EXISTS "nts_NOTIFICATION_CONTENT_TYPES" (
    code VARCHAR(50) PRIMARY KEY,
    label VARCHAR(50) NOT NULL,
    description TEXT
);

INSERT INTO
    "nts_NOTIFICATION_CONTENT_TYPES" (code, "label", description)
VALUES
    ('WARNING','Warning', '-'),
    ('ALERT','Alert', '-')
    ON CONFLICT (code) DO NOTHING;

ALTER TABLE nts_notifications
ADD COLUMN IF NOT EXISTS type_code VARCHAR(50) REFERENCES "nts_NOTIFICATION_TYPES" (code) DEFAULT 'USER_ACTIVITY',
ADD COLUMN IF NOT EXISTS content_type_code VARCHAR(50) REFERENCES "nts_NOTIFICATION_CONTENT_TYPES" (code);

UPDATE nts_notifications
SET
    source_code = 'ALERT'
WHERE
    type_code = 'ALERT';

ALTER TABLE ins_alerts
ADD COLUMN IF NOT EXISTS related_asset_id VARCHAR(40);

COMMIT;