BEGIN;

ALTER TABLE "ams_asset_inspection_tyre" 
ADD COLUMN IF NOT EXISTS failed_visual_checking B<PERSON><PERSON>EAN DEFAULT false,
ADD COLUMN IF NOT EXISTS require_rotation_tyre BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS require_spooring_vehicle B<PERSON><PERSON>EAN DEFAULT false;


CREATE TABLE IF NOT EXISTS "ams_ASSET_INSPECTION_ASSIGNMENT_TYPES" (
    code VARCHAR(255) PRIMARY KEY,
    label VARCHAR(255) NOT NULL,
    description TEXT NOT NULL
);

INSERT INTO
    "ams_ASSET_INSPECTION_ASSIGNMENT_TYPES" (code, "label", description)
VALUES
    ('CUSTOM_FORM', 'Custom Form', '-'),
    ('VEHICLE_TYRE_INSPECTION', 'Vehicle Tyre Inspection', '-')
ON CONFLICT (code) DO NOTHING;

CREATE TABLE IF NOT EXISTS ams_asset_inspection_assignments (
    id VARCHAR(40) PRIMARY KEY,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    deleted_at TIMESTAMP,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    inspection_id VARCHAR(40) NOT NULL,
    user_id VARCHAR(40),
    user_name VARCHAR(255),
    type_code VARCHAR(255) NOT NULL,
    CONSTRAINT fk_inspection_id FOREIGN KEY(inspection_id) REFERENCES ams_asset_inspections(id),
    CONSTRAINT fk_type_code FOREIGN KEY(type_code) REFERENCES "ams_ASSET_INSPECTION_ASSIGNMENT_TYPES"(code),
    UNIQUE(inspection_id, type_code)
);

COMMIT;