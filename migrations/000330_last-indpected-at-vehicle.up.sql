BEGIN;

ALTER TABLE ams_asset_vehicles
ADD COLUMN IF NOT EXISTS last_inspected_at TIMESTAMPTZ;

WITH cte AS (
SELECT
	asset_vehicle_id,
	max(created_at) last_inspected_at
FROM
	ams_asset_inspection_vehicle aaiv
WHERE
	asset_vehicle_id IS NOT NULL
GROUP BY
	asset_vehicle_id)
	UPDATE
	ams_asset_vehicles
SET
	last_inspected_at = cte.last_inspected_at
FROM
	cte
WHERE
	cte.asset_vehicle_id = ams_asset_vehicles.asset_id;

COMMIT;