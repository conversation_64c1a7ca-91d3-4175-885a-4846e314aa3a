BEGIN;

ALTER TABLE "ams_asset_tyres"
ADD COLUMN IF NOT EXISTS updated_by VA<PERSON><PERSON><PERSON>(40),
ADD COLUMN IF NOT EXISTS created_by VARCHAR(40);

ALTER TABLE "ams_asset_vehicles"
ADD COLUMN IF NOT EXISTS updated_by VA<PERSON><PERSON><PERSON>(40),
ADD COLUMN IF NOT EXISTS created_by VARC<PERSON>R(40);

UPDATE ams_asset_tyres
SET total_lifetime = 0
WHERE total_lifetime IS NULL;

ALTER TABLE ams_asset_tyres
ALTER COLUMN total_lifetime SET DEFAULT 0;

ALTER TABLE ams_asset_tyres
ALTER COLUMN total_lifetime SET NOT NULL;

CREATE OR REPLACE FUNCTION ams_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
  _new jsonb;
  _old jsonb;
  _k text;
  _id varchar(44);
BEGIN
  _id := concat('ams_log_', uuid_generate_v4());
  _new := to_jsonb(NEW);
  _old := to_jsonb(OLD);

  FOR _k IN SELECT * FROM jsonb_object_keys(_new)
  LOOP
    IF _new[_k] = _old[_k]
      THEN _new = _new - _k; _old = _old - _k;
    END IF;
  END LOOP;

  INSERT INTO ams_logs(id, table_name, client_id, created_by, created_at, previous_value,new_value)
    VALUES(_id, TG_TABLE_NAME, NEW.client_id , NEW.updated_by, NEW.updated_at, _old,_new);
  
  IF TG_TABLE_NAME = 'ams_assets' 
    THEN  INSERT INTO ams_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.id);
  ELSIF TG_TABLE_NAME = 'ams_asset_vehicles' 
    THEN  INSERT INTO ams_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET_VEHICLE', NEW.asset_id);
  ELSIF TG_TABLE_NAME = 'ams_asset_tyres' 
    THEN  INSERT INTO ams_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET_TYRE', NEW.asset_id);
  END IF;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_trigger_ams_asset_vehicles ON ams_asset_vehicles;
DROP TRIGGER IF EXISTS update_trigger_ams_asset_tyres ON ams_asset_tyres;

CREATE TRIGGER update_trigger_ams_asset_vehicles
AFTER INSERT OR UPDATE ON ams_asset_vehicles
FOR EACH ROW
EXECUTE FUNCTION ams_trigger_function();

CREATE TRIGGER update_trigger_ams_asset_tyres
AFTER INSERT OR UPDATE ON ams_asset_tyres
FOR EACH ROW
EXECUTE FUNCTION ams_trigger_function();

COMMIT;