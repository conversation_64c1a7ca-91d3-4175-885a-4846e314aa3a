INSERT INTO
    "sts_ATTACHMENT_REFERENCE" (code, "label", description)
VALUES
    ('BONUS_PENALTY', 'Bonus Penalty', 'Bonus Penalty') ON CONFLICT (code) DO NOTHING;

INSERT INTO 
	"uis_PERMISSIONS" (code, user_permission_category_code, label, description, display_sequence) 
VALUES
('BONUS_PENALTY_TYRE', 'APPROVALS', 'Approve Bonus & Penalty Tyre', 'Users can approve bonus penalty.', 1);  
 
INSERT INTO
    "app_APPROVAL_SOURCES" (code, "label", description,user_permission_codes)
VALUES
    ('BONUS_PENALTY_TYRE', 'Bonus & Penalty Tyre', '-','{"BONUS_PENALTY_TYRE"}');

ALTER TABLE "ams_linked_asset_vehicle_tyres"
ADD COLUMN IF NOT EXISTS "is_claimed_bonus_penalty" BOOLEAN DEFAULT FALSE; 