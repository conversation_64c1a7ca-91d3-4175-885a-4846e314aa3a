-- Create "ins_translogic_calibration" table
CREATE TABLE
    IF NOT EXISTS "ins_translogic_calibration" (
        id VARCHAR(40) NOT NULL,
        device_id VARCHAR(40) NOT NULL,
        idle_value DECIMAL(10,2) DEFAULT 0,
        tread_depth_value DECIMAL(10,2) DEFAULT 0,
        pressure_value DECIMAL(10,2) DEFAULT 0,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40),
        created_by <PERSON>RC<PERSON><PERSON>(40),
        PRIMAR<PERSON> KEY ("id")
    );

-- Create index "ins_translogic_calibration_deleted_at" to table: "ins_translogic_calibration"
CREATE INDEX "idx_ins_translogic_calibration_deleted_at" ON "ins_translogic_calibration" ("deleted_at");

-- <PERSON>reate index "ins_translogic_calibration_device_id" to table: "ins_translogic_calibration"
CREATE INDEX "idx_ins_translogic_calibration_device_id" ON "ins_translogic_calibration" ("device_id");