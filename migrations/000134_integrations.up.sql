BEGIN;

-- ins_INTEGRATION_TYPE table
CREATE TABLE
    IF NOT EXISTS "ins_INTEGRATION_TYPE" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "ins_INTEGRATION_TYPE" (code, "label", description)
VALUES
    ('TRACKING', 'Tracking', 'Tracking') ON CONFLICT (code) DO NOTHING;

CREATE TABLE
    IF NOT EXISTS "ins_INTEGRATION_TARGET" (
        code VARCHAR(255) PRIMARY KEY,
        label VARCHAR(255) NOT NULL,
        description VARCHAR(255) NOT NULL,
        auth_credential_json_template JSONB
    );

INSERT INTO
    "ins_INTEGRATION_TARGET" (
        code,
        "label",
        description,
        auth_credential_json_template
    )
VALUES
    (
        'GPS_ID',
        'GPS.id',
        'Integration with GPS.id',
        '{"username": "string","password": "string"}'
    ) ON CONFLICT (code) DO NOTHING;

-- ins_INTEGRATION_STATUSES table
CREATE TABLE
    IF NOT EXISTS "ins_INTEGRATION_ACCOUNT_STATUSES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "ins_INTEGRATION_ACCOUNT_STATUSES" (code, "label", description)
VALUES
    ('ACTIVE', 'Active', '-'),
    ('INACTIVE', 'Inactive', '-'),
    ('DELETED', 'Deleted', '-') ON CONFLICT (code) DO NOTHING;

-- ins_integration_account table
CREATE TABLE
    IF NOT EXISTS "ins_integration_account" (
        id VARCHAR(40) PRIMARY KEY,
        target_code VARCHAR(20) REFERENCES "ins_INTEGRATION_TARGET" (code),
        auth_credential_json TEXT,
        status_code VARCHAR(20) REFERENCES "ins_INTEGRATION_ACCOUNT_STATUSES" (code),
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );

COMMIT;