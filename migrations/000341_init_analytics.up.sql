BEGIN;

CREATE TABLE
    IF NOT EXISTS "uis_ANALYTIC_TYPES" (
        code VARCHAR(40) PRIMARY KEY,
        label VARCHAR(40) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "uis_ANALYTIC_TYPES" (code, "label", description)
VALUES
    ('DASHBOARD_OVERVIEW_CHART', 'Dashboard Overview Chart', '-'),
    ('DASHBOARD_OVERVIEW_CARD', 'Dashboard Overview Card', '-')
ON CONFLICT (code) DO NOTHING;

CREATE TABLE
    IF NOT EXISTS "uis_ANALYTICS" (
        code VARCHAR(50) PRIMARY KEY,
        label VARCHAR(50) NOT NULL,
        description VARCHAR(50) NOT NULL,
        sequence INT NOT NULL DEFAULT 0,
        analytic_type_code VARCHAR(40) NOT NULL,
        CONSTRAINT fk_analytic_type
            FOREIGN KEY (analytic_type_code)
            REFERENCES "uis_ANALYTIC_TYPES"(code)
    );

INSERT INTO
    "uis_ANALYTICS" (code, "label", description, sequence, analytic_type_code)
VALUES
    ('TOTAL_ASSETS', 'Total Assets', '-', 1, 'DASHBOARD_OVERVIEW_CARD'),
    ('ASSETS_WITH_EXPIRED_COMPONENT', 'Assets With Expired Component', '-', 2, 'DASHBOARD_OVERVIEW_CARD'),
    ('TOTAL_WORK_ORDERS', 'Total Work Orders', '-', 3, 'DASHBOARD_OVERVIEW_CARD'),
    ('UNASSIGNED_WORK_ORDERS', 'Unassigned Work Orders', '-', 4, 'DASHBOARD_OVERVIEW_CARD'),
    ('NOT_IN_PROGRESS_MORE_THAN_48_HOURS', 'Not In Progress More Than 48 Hours', '-', 5, 'DASHBOARD_OVERVIEW_CARD'),
    ('OVERDUE_WORK_ORDERS', 'Overdue Work Orders', '-', 6, 'DASHBOARD_OVERVIEW_CARD'),
    ('ON_HOLD_WORK_ORDERS', 'On Hold Work Orders', '-', 7, 'DASHBOARD_OVERVIEW_CARD'),
    ('INSTALLED_TYRES_WITH_CRITICAL_TREAD_DEPTH', 'Installed Tyres with Critical Tread Depth', '-', 8, 'DASHBOARD_OVERVIEW_CARD'),
    ('INSTALLED_TYRES_NOT_INSPECTED_THIS_MONTH', 'Installed Tyres Not Inspected This Month', '-', 9, 'DASHBOARD_OVERVIEW_CARD'),
    ('ASSET_TYRE_WITH_TYRE_ALERTS', 'Asset Tyre with Tyre Alerts', '-', 10, 'DASHBOARD_OVERVIEW_CARD'),
    ('ASSETS_WITH_OPERATIONAL_ALERTS', 'Assets with Operational Alerts', '-', 11, 'DASHBOARD_OVERVIEW_CARD'),
    ('ASSETS_UNDER_MAINTENANCE', 'Assets Under Maintenance', '-', 12, 'DASHBOARD_OVERVIEW_CARD'),
    ('ASSET_STATUS', 'Asset Status', '-', 13, 'DASHBOARD_OVERVIEW_CHART'),
    ('WORK_ORDER_STATUS', 'Work Order Status', '-', 14, 'DASHBOARD_OVERVIEW_CHART'),
    ('ASSET_BRANDS', 'Asset Brands', '-', 15, 'DASHBOARD_OVERVIEW_CHART'),
    ('ASSET_CATEGORIES_&_SUBCATEGORIES', 'Asset Categories Subcategories', '-', 16, 'DASHBOARD_OVERVIEW_CHART')
ON CONFLICT (code) DO NOTHING;


CREATE TABLE
    IF NOT EXISTS "uis_analytic_display_configs" (
        id VARCHAR(40) PRIMARY KEY,
        analytic_code VARCHAR(50) NOT NULL,
        sequence INT NOT NULL DEFAULT 0,
        user_id VARCHAR(40) NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        is_active BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL,
        deleted_at TIMESTAMPTZ,
        CONSTRAINT fk_analytic
            FOREIGN KEY (analytic_code)
            REFERENCES "uis_ANALYTICS"(code),
        CONSTRAINT unique_analytic_user_client
            UNIQUE (analytic_code, user_id, client_id)
    );

COMMIT;
