BEGIN;

INSERT INTO
    "ins_INTEGRATION_TARGET" (
        code,
        "label",
        description,
        auth_credential_json_template
    )
VALUES
    (
        'FLESPI',
        'AssetFindr - GPS',
        'Integration with AssetFindr - GPS',
        '{}'
    ) ON CONFLICT (code) DO NOTHING;

INSERT INTO
    "ins_INTEGRATION_TARGET_TYPE" (
        code,
        label,
        description,
        integration_type_code,
        integration_target_code,
        identifier_json_template
    )
VALUES
    (
        'FLESPI_TRACKING',
        'Flespi Tracking',
        '-',
        'TRACKING',
        'FLESPI',
        '{"imei":"string"}'
    ) ON CONFLICT (code) DO NOTHING;

INSERT INTO
    ins_integration_account (
        id,
        target_code,
        auth_credential_json,
        status_code,
        client_id,
        created_at,
        updated_at,
        deleted_at,
        updated_by,
        created_by,
        "name"
    )
VALUES
    (
        'ina_df079830-d362-426d-b783-535dc7d48dd1',
        'FLESPI',
        '',
        'ACTIVE',
        'GENERAL',
        NOW(),
        NOW(),
        NULL,
        'admin',
        'admin',
        'AssetFinder GPS Account'
    ) ON CONFLICT (id) DO NOTHING;

COMMIT;