BEGIN;

INSERT INTO
    "ams_ASSET_CATEGORIES" (code, "label", description)
VALUES
    (
        'GENERAL_COMPUTER_AND_LAPTOP',
        'Computer and Laptop',
        '-'
    ),
    (
        'GENERAL_HANDPHONE_AND_TABLET',
        'Handphone and Tablet',
        '-'
    ) ON CONFLICT (code) DO NOTHING;

DELETE FROM "ams_ASSET_CATEGORY_MAPPING"
WHERE
    category_code IN (
        'GENERAL_COMPUTERAND_LAPTOP',
        'GENERAL_HANDPHONEAND_TABLET',
        'GENERAL'
    );

DELETE FROM "ams_ASSET_CATEGORIES"
WHERE
    code IN (
        'ELECTRONIC',
        'FURNITURE',
        'COMPUTER_AND_LAPTOP',
        'HANDPHONE_AND_TABLET',
        'GENERAL_COMPUTERAND_LAPTOP',
        'GENERAL_HANDPHONEAND_TABLET',
        'ELECTRONIC',
        'FURNITURE',
        'COMPUTER_AND_LAPTOP',
        'HANDPHONE_AND_TABLET'
    );

INSERT INTO
    "ams_ASSET_CATEGORY_MAPPING" (category_code, sub_category_code)
VALUES
    ('GENERAL_COMPUTER_AND_LAPTOP', 'LAPTOPS'),
    ('GENERAL_COMPUTER_AND_LAPTOP', 'MONITORS'),
    ('GENERAL_COMPUTER_AND_LAPTOP', 'KEYBOARDS'),
    ('GENERAL_COMPUTER_AND_LAPTOP', 'MICE'),
    (
        'GENERAL_COMPUTER_AND_LAPTOP',
        'COMPUTER_ACCESSORIES'
    ),
    ('GENERAL_COMPUTER_AND_LAPTOP', 'CPU_TOWERS'),
    ('GENERAL_COMPUTER_AND_LAPTOP', 'UPS_UNITS'),
    (
        'GENERAL_COMPUTER_AND_LAPTOP',
        'EXTERNAL_HARD_DRIVES'
    ),
    ('GENERAL_COMPUTER_AND_LAPTOP', 'RAM_MODULES'),
    (
        'GENERAL_COMPUTER_AND_LAPTOP',
        'NETWORK_SWITCHES_ROUTERS'
    ),
    (
        'GENERAL_COMPUTER_AND_LAPTOP',
        'HEADSETS_MICROPHONES'
    ),
    ('GENERAL_COMPUTER_AND_LAPTOP', 'WEBCAMS'),
    ('GENERAL_HANDPHONE_AND_TABLET', 'HANDPHONES'),
    ('GENERAL_HANDPHONE_AND_TABLET', 'TABLETS'),
    (
        'GENERAL_HANDPHONE_AND_TABLET',
        'MOBILE_ACCESSORIES'
    ) ON CONFLICT (category_code, sub_category_code) DO NOTHING;

ALTER TABLE "ams_ASSET_CATEGORIES"
ADD COLUMN IF NOT EXISTS is_general BOOLEAN DEFAULT TRUE;

UPDATE "ams_ASSET_CATEGORIES"
SET
    is_general = FALSE
WHERE
    code IN ('VEHICLE', 'TYRE');

ALTER TABLE "ams_assets"
ALTER COLUMN asset_category_code TYPE VARCHAR(255),
ALTER COLUMN sub_category_code TYPE VARCHAR(255);

UPDATE ams_assets
SET
    asset_category_code = 'GENERAL_VEHICLE',
    sub_category_code = 'VEHICLE'
WHERE
    sub_category_code = 'GENERAL_VEHICLE';

UPDATE ams_assets
SET
    asset_category_code = 'GENERAL_TYRE',
    sub_category_code = 'TYRE'
WHERE
    sub_category_code = 'GENERAL_TYRE';

UPDATE ams_assets
SET
    asset_category_code = 'GENERAL_TYRE',
    sub_category_code = 'TYRE'
WHERE
    sub_category_code = 'TYRE'
    AND asset_category_code = 'GENERAL';

UPDATE ams_assets
SET
    asset_category_code = 'GENERAL_COMPUTER_AND_LAPTOP',
    sub_category_code = 'LAPTOPS'
WHERE
    sub_category_code = 'COMPUTER_AND_LAPTOP'
    AND asset_category_code = 'GENERAL';

UPDATE ams_assets
SET
    asset_category_code = 'GENERAL_HANDPHONE_AND_TABLET',
    sub_category_code = 'HANDPHONES'
WHERE
    sub_category_code = 'HANDPHONE_AND_TABLET'
    AND asset_category_code = 'GENERAL';

UPDATE ams_assets
SET
    asset_category_code = 'GENERAL_ELECTRONIC',
    sub_category_code = 'TELEVISIONS'
WHERE
    sub_category_code = 'ELECTRONIC'
    AND asset_category_code = 'GENERAL';

UPDATE ams_assets
SET
    asset_category_code = 'GENERAL_FURNITURE',
    sub_category_code = 'DESKS_TABLES'
WHERE
    sub_category_code = 'FURNITURE'
    AND asset_category_code = 'GENERAL';
  
DELETE FROM "ams_ASSET_SUB_CATEGORIES"
WHERE code IN ('GENERAL_VEHICLE', 'GENERAL_TYRE');

INSERT INTO "ams_ASSET_CATEGORY_MAPPING"
(category_code, sub_category_code)
VALUES('GENERAL_VEHICLE', 'VEHICLE'), ('GENERAL_TYRE', 'TYRE')  ON CONFLICT (category_code, sub_category_code) DO NOTHING;;


DELETE FROM "ams_ASSET_CATEGORIES"
WHERE
    code IN (
        'GENERAL'
    );
COMMIT;