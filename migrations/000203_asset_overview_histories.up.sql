CREATE TABLE IF NOT EXISTS "ams_asset_vehicle_stats_histories" (
    datetime TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    asset_id VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL,
    vehicle_km BIGINT,
    vehicle_hm BIGINT
);

CREATE TABLE IF NOT EXISTS "fns_journal_ref_sum_amount_histories" (
    datetime TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    reference_id VARCHAR(40),
    source_code VARCHAR(255),
    client_id VARCHAR(40),
    amount BIGINT
);