BEGIN;

-- Populate Past Data
WITH cte AS (
  SELECT
    id,
    'WO-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    lpad((row_number() over (partition by client_id, date_trunc('day',timezone('-7', created_at)) order by created_at))::text,4,'0') AS new_ticket_number,
    ticket_number
  FROM
    tks_tickets tt
  WHERE
   tt.deleted_at IS NULL
)
UPDATE tks_tickets AS tt SET ticket_number = cte.new_ticket_number FROM cte WHERE tt.id = cte.id;

-- Create Trigger To Set New Number
CREATE OR REPLACE FUNCTION trigger_set_new_ticket_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    'WO-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    lpad((row_number() over (partition BY NEW.client_id,  date_trunc('day',timezone('-7', created_at)) order by created_at))::text,4,'0') AS new_ticket_number,
    ticket_number
  FROM
    tks_tickets tt
  WHERE
   tt.deleted_at IS NULL 
   AND client_id = NEW.client_id
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE tks_tickets AS tt SET ticket_number = cte.new_ticket_number FROM cte WHERE tt.id = cte.id AND (cte.ticket_number IS NULL OR cte.ticket_number = '');
RETURN NEW;
END;
$$;

DROP VIEW "app_approval_requests_views";

CREATE VIEW app_approval_requests_views 
AS SELECT
  'AP-' || 
    to_char(timezone('-7', created_at),
  'YYMMDD') || '-' ||
    lpad((ROW_NUMBER() OVER (PARTITION BY client_id,
  date_trunc('day',
  timezone('-7', created_at))
ORDER BY
  created_at))::TEXT,
  4,
  '0') AS approval_number,
  *
FROM
  app_approval_requests aar;

CREATE OR REPLACE FUNCTION trigger_set_new_transaction_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    CASE
    WHEN it.transaction_type_code = 'STOCK_IN' THEN
    'INV-IN-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    lpad((row_number() over (partition BY NEW.client_id,  date_trunc('day',timezone('-7', created_at)) order by created_at))::text,4,'0')
    WHEN it.transaction_type_code = 'STOCK_OUT' THEN 
    'INV-OUT-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    lpad((row_number() over (partition BY NEW.client_id,  date_trunc('day',timezone('-7', created_at)) order by created_at))::text,4,'0')
    WHEN it.transaction_type_code = 'STOCK_TRANSFER' THEN 
    'INV-TR-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    lpad((row_number() over (partition BY NEW.client_id,  date_trunc('day',timezone('-7', created_at)) order by created_at))::text,4,'0')
    END
    AS new_transaction_number,
    transaction_number
  FROM
    inv_inventory_transactions it
  WHERE
   it.deleted_at IS NULL 
   AND client_id = NEW.client_id
   AND transaction_type_code = NEW.transaction_type_code
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE inv_inventory_transactions AS it SET transaction_number = cte.new_transaction_number FROM cte WHERE it.id = cte.id AND (cte.transaction_number IS NULL OR cte.transaction_number = '');
RETURN NEW;
END;
$$;


CREATE OR REPLACE FUNCTION trigger_set_new_bap_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    'BNP-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    lpad((row_number() over (partition BY NEW.client_id,  date_trunc('day',timezone('-7', created_at)) order by created_at))::text,4,'0') AS new_bap_number,
    bap_number
  FROM
    ams_bonus_penalties	 bp
  WHERE
   bp.deleted_at IS NULL 
   AND client_id = NEW.client_id
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE ams_bonus_penalties AS bp SET bap_number = cte.new_bap_number FROM cte WHERE bp.id = cte.id AND (cte.bap_number IS NULL OR cte.bap_number = '');
RETURN NEW;
END;
$$;

-- Populate Past Data
WITH cte AS (
  SELECT
    id,
    'INS-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    lpad((row_number() over (partition by client_id, date_trunc('day',timezone('-7', created_at)) order by created_at))::text,4,'0') AS new_inspection_number,
    inspection_number
  FROM
    ams_asset_inspections tt
  WHERE
   tt.deleted_at IS NULL
)
UPDATE ams_asset_inspections AS tt SET inspection_number = cte.new_inspection_number FROM cte WHERE tt.id = cte.id;

-- Create Trigger To Set New Number
CREATE OR REPLACE FUNCTION trigger_set_new_inspection_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    'INS-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    lpad((row_number() over (partition BY NEW.client_id,  date_trunc('day',timezone('-7', created_at)) order by created_at))::text,4,'0') AS new_inspection_number,
    inspection_number
  FROM
    ams_asset_inspections tt
  WHERE
   tt.deleted_at IS NULL 
   AND client_id = NEW.client_id
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE ams_asset_inspections AS tt SET inspection_number = cte.new_inspection_number FROM cte WHERE tt.id = cte.id AND (cte.inspection_number IS NULL OR cte.inspection_number = '');
RETURN NEW;
END;
$$;

-- Populate Past Data
WITH cte AS (
  SELECT
    id,
    'KLB-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    row_number() over (partition by client_id order by created_at) AS new_number,
    "number"
  FROM
    ins_translogic_calibration tt
  WHERE
   tt.deleted_at IS NULL
)
UPDATE ins_translogic_calibration AS tt SET "number" = cte.new_number FROM cte WHERE tt.id = cte.id;

-- Create Trigger To Set New Number
CREATE OR REPLACE FUNCTION trigger_set_new_translogic_calibration_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    'KLB-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    row_number() over (partition by client_id order by created_at) AS new_number,
    number
  FROM
    ins_translogic_calibration tt
  WHERE
   tt.deleted_at IS NULL 
   AND client_id = NEW.client_id
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE ins_translogic_calibration AS tt SET number = cte.new_number FROM cte WHERE tt.id = cte.id AND (cte.number IS NULL OR cte.number = '');
RETURN NEW;
END;
$$;

COMMIT;