CREATE TABLE IF NOT EXISTS "ams_asset_tyres_tread_configs" (
    id VARCHAR(40) PRIMARY KEY,
    brand_name VARCHAR(100) NOT NULL,
    retread_type VARCHAR(100) NOT NULL,
    original_tread_depth INT NOT NULL DEFAULT 0,
    width INT NOT NULL DEFAULT 0,
    weight INT NOT NULL DEFAULT 0,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    deleted_at TIMESTAMPTZ,
    updated_by VA<PERSON><PERSON>R(40) NOT NULL,
    created_by VA<PERSON>HAR(40) NOT NULL
);

ALTER TABLE "ams_asset_tyres_treads" 
ADD COLUMN IF NOT EXISTS tyres_tread_config_id VARCHAR(40),
ADD CONSTRAINT fk_tyres_tread_config_id
FOREIGN KEY (tyres_tread_config_id) REFERENCES "ams_asset_tyres_tread_configs"(id);
