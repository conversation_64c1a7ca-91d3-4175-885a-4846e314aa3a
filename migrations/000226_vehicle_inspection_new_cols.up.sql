BEGIN;

ALTER TABLE "ams_asset_inspection_vehicle" 
ALTER COLUMN asset_vehicle_id DROP NOT NULL,
ADD COLUMN IF NOT EXISTS axle_configuration VARCHAR(255)[],
ADD COLUMN IF NOT EXISTS custom_brand_name VA<PERSON>HAR(255),
ADD COLUMN IF NOT EXISTS custom_model_name VA<PERSON>HAR(255),
ADD COLUMN IF NOT EXISTS custom_reference_number VARCHAR(255);


WITH cte AS (
SELECT
  axle_configuration,
  asset_id
FROM
  ams_asset_vehicles aav )
UPDATE
  ams_asset_inspection_vehicle
SET
  axle_configuration = cte.axle_configuration  
  FROM cte
WHERE
  asset_vehicle_id = cte.asset_id;

COMMIT;