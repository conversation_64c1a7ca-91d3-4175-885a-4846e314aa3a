BEGIN;

-- Create table for package statuses
CREATE TABLE IF NOT EXISTS "inv_PACKAGE_STATUSES" (
    code VARCHAR(255) PRIMARY KEY,
    label VARCHAR(255) NOT NULL,
    description TEXT
);

INSERT INTO
    "inv_PACKAGE_STATUSES" (code, "label", description)
VALUES
    ('ACTIVE', 'Active', '-'),
    ('INACTIVE', 'Inactive', '-') ON CONFLICT (code) DO NOTHING;


-- Create table for packages
CREATE TABLE IF NOT EXISTS inv_packages (
    id VARCHAR(40) PRIMARY KEY,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    period_days SMALLINT NOT NULL,
    start_date TIMESTAMPTZ,
    end_date TIMESTAMPTZ,
    status_code VARCHAR(255) NOT NULL REFERENCES "inv_PACKAGE_STATUSES"(code),
    photo TEXT,
    price BIGINT
);

-- Create table for package benefits
CREATE TABLE IF NOT EXISTS inv_package_benefits (
    id VARCHAR(40) PRIMARY KEY,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    package_id VARCHAR(40) NOT NULL REFERENCES inv_packages(id),
    limitation TEXT,
    requirement TEXT,
    quantity INT,
    product_id VARCHAR(40) NOT NULL REFERENCES inv_products(id)
);

COMMIT;