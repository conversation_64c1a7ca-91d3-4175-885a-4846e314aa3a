BEGIN;

INSERT INTO
    "ins_ALERT_ACTION_TYPES" (code, "label", description)
VALUES
    ('WEB_MOBILE_APP_NOTIFICATION', 'Web / Mobile App Notification', '-');


CREATE TABLE IF NOT EXISTS "ins_ALERT_PARAMETER_DATA_TYPES" (
    code VARCHAR(255) PRIMARY KEY
);

INSERT INTO
    "ins_ALERT_PARAMETER_DATA_TYPES" (code)
VALUES
    ('NUMERIC'),
    ('BOOLEAN'),
    ('STRING')
     ON CONFLICT (code) DO NOTHING;

CREATE TABLE IF NOT EXISTS "ins_ALERT_PARAMETER_TYPES" (
    code VARCHAR(50) PRIMARY KEY
);

INSERT INTO
    "ins_ALERT_PARAMETER_TYPES" (code)
VALUES
    ('ORIGINAL'),
    ('GENERATED_ON_INPUT')
     ON CONFLICT (code) DO NOTHING;

CREATE TABLE IF NOT EXISTS "ins_ALERT_PARAMETERS" (
    code VARCHAR(255) PRIMARY KEY,
    label VARCHAR(255),
    description VARCHAR(255),
    unit VARCHAR(255),
    data_type VARCHAR(20) REFERENCES "ins_ALERT_PARAMETER_DATA_TYPES" (code),
    source_codes VARCHAR(255)[],
    type_code VARCHAR(50) REFERENCES "ins_ALERT_PARAMETER_TYPES" (code)
);

INSERT INTO
    "ins_ALERT_PARAMETERS" (code,label,description,unit,data_type,source_codes,type_code)
VALUES
    ('can_bus.can_abs_failure_indicator_status','ABS Failure Indicator Status','Status indikator kegagalan sistem rem anti-lock (ABS).','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_additional_front_lights_status','Additional Front Lights Status','Status lampu tambahan di bagian depan kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_additional_rear_lights_status','Additional Rear Lights Status','Status lampu tambahan di bagian belakang kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_air_condition_status','Air Condition Status','Status pengoperasian sistem pendingin udara.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_airbag_indicator_status','Airbag Indicator Status','Status indikator sistem airbag kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_automatic_retarder_status','Automatic Retarder Status','Status sistem retarder otomatis pada kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_battery_indicator_status','Battery Indicator Status','Status indikator baterai kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_car_closed_remote_status','Car Closed Remote Status','Status kendaraan tertutup menggunakan remote.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_car_closed_status','Car Closed Status','Status kendaraan tertutup secara umum.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_central_differential_4_hi_status','Central Differential 4 Hi Status','status mode penggerak empat roda dalam posisi high gear aktif.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_central_differential_4_lo_status','Central Differential 4 Lo Status','status mode penggerak empat roda dalam posisi low gear aktif.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_check_engine_indicator_status','Check Engine Indicator Status','Status indikator pemeriksaan mesin.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_cng_status','Compresseed Natural Gas Status','Status penggunaan gas alam terkompresi (CNG) pada kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_connection_state_1','Connection State 1','Status koneksi perangkat atau sistem terkait.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_connection_state_2','Connection State 2','Status koneksi perangkat atau sistem terkait.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_connection_state_3','Connection State 3','Status koneksi perangkat atau sistem terkait.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_coolant_level_low_indicator_status','Coolant Level Low Indicator Status','Status indikator level cairan pendingin rendah.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_cruise_status','Cruise Control Status','Status sistem kontrol kecepatan kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_drive_gear_status','Drive Gear Status','Status gigi penggerak kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_driver_seatbelt_indicator_status','Driver Seatbelt Indicator Status','Status indikator sabuk pengaman pengemudi.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_driver_seatbelt_status','Driver Seatbelt Status','Status pemakaian sabuk pengaman pengemudi.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_dynamic_ignition_status','Dynamic Ignition Status','Status sistem pengapian dinamis kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_electric_engine_status','Electric Engine Status','Status mesin listrik kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_electronic_power_control_status','Electronic Power Control Status','Status kontrol daya elektronik.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_engine_ignition_status','Engine Ignition Status','Status sistem pengapian mesin.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_engine_load_level','Engine Load Level','Tingkat beban kerja mesin.','%','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_engine_lock_status','Engine Lock Status','Status penguncian mesin kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_engine_motorhours','Engine Motorhours','Total jam operasi mesin sejak instalasi gateway.','h','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_engine_oil_level','Engine Oil Level','Level oli mesin kendaraan.','%','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_engine_rpm','Engine Rpm','Kecepatan putaran mesin dalam satuan RPM (putaran per menit).','RPM','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_engine_temperature','Engine Temperature','Suhu mesin kendaraan.','°C','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_engine_working_status','Engine Working Status','Status operasional mesin kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_eps_indicator_status','Electric Power Steering Indicator Status','Status indikator sistem kemudi listrik.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_esp_indicator_status','Electronic Stability Program Indicator Status','Status indikator program stabilitas elektronik.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_esp_status','Electronic Stability Program Status','Status program stabilitas elektronik.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_factory_armed_status','Factory Armed Status','Status sistem keamanan bawaan pabrik.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_front_differential_status','Front Differential Status','Status diferensial depan kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_front_fog_lights_status','Front Fog Lights Status','Status lampu kabut depan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_front_left_door_status','Front Left Door Status','Status pintu depan kiri kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_front_passenger_seatbelt_status','Front Passenger Seatbelt Status','Status sabuk pengaman penumpang depan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_front_passenger_status','Front Passenger Status','Status keberadaan penumpang depan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_front_right_door_status','Front Right Door Status','Status pintu depan kanan kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_fuel_consumed','Fuel Consumed','Total bahan bakar yang telah digunakan kendaraan.','L','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_fuel_level','Fuel Level','Level bahan bakar dalam tangki kendaraan.','%','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_fuel_level_low_indicator_status','Fuel Level Low Indicator Status','Status indikator level bahan bakar rendah.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_glow_plug_indicator_status','Glow Plug Indicator Status','Status indikator glow plug mesin diesel.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_handbrake_indicator_status','Handbrake Indicator Status','Status indikator rem tangan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_handbrake_status','Handbrake Status','Status pengoperasian rem tangan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_high_beam_status','High Beam Status','Status lampu jauh kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_hood_status','Hood Status','Status kap mesin kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_ignition_key_status','Ignition Key Status','Status kunci pengapian kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_interlock_active','Interlock Active','Status aktif interlock kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_light_signal_status','Light Signal Status','Status sinyal lampu kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_lights_failure_indicator_status','Lights Failure Indicator Status','Status indikator kegagalan lampu.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_lights_hazard_lights_status','Lights Hazard Lights Status','Status lampu hazard kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_low_beam_status','Low Beam Status','Status lampu dekat kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_maintenance_required_status','Maintenance Required Status','Status kebutuhan perawatan kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_manual_retarder_status','Manual Retarder Status','Status sistem retarder manual.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_module_sleep_mode','Module Sleep Mode','Status mode tidur modul sistem.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_neutral_gear_status','Neutral Gear Status','Status posisi gigi netral.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_oil_pressure_indicator_status','Oil Pressure Indicator Status','Status indikator tekanan oli.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_operator_present_status','Operator Present Status','Status keberadaan operator kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_parking_lights_status','Parking Lights Status','Status lampu parkir.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_parking_status','Parking Status','Status kendaraan dalam mode parkir.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_passenger_seatbelt_indicator_status','Passenger Seatbelt Indicator Status','Status indikator sabuk pengaman penumpang.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_pedal_brake_status','Pedal Brake Status','Status pengoperasian pedal rem.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_pedal_clutch_status','Pedal Clutch Status','Status pengoperasian pedal kopling.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_private_status','Private Status','Status mode pribadi kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_program_id','Program Id','Identifikasi program yang sedang berjalan.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_pto_status','Power Take-off Status','Status pengoperasian power take-off.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_ready_to_drive_indicator_status','Ready To Drive Indicator Status','Status indikator siap berkendara.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_rear_central_passenger_seatbelt_status','Rear Central Passenger Seatbelt Status','Status sabuk pengaman penumpang tengah belakang.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_rear_differential_status','Rear Differential Status','Status diferensial belakang kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_rear_fog_lights_status','Rear Fog Lights Status','Status lampu kabut belakang.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_rear_left_door_status','Rear Left Door Status','Status pintu belakang kiri kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_rear_left_passenger_seatbelt_status','Rear Left Passenger Seatbelt Status','Status sabuk pengaman penumpang belakang kiri.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_rear_right_door_status','Rear Right Door Status','Status pintu belakang kanan kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_rear_right_passenger_seatbelt_status','Rear Right Passenger Seatbelt Status','Status sabuk pengaman penumpang belakang kanan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_reverse_gear_status','Reverse Gear Status','Status gigi mundur kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_roof_opened_status','Roof Opened Status','Status atap kendaraan terbuka.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_soot_filter_indicator_status','Soot Filter Indicator Status','Status indikator filter partikel mesin diesel.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_standalone_engine','Standalone Engine','Status mesin independen.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_stop_indicator_status','Stop Indicator Status','Status indikator berhenti kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_throttle_pedal_level','Throttle Pedal Level','Tingkat pengoperasian pedal gas.','%','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_tire_pressure_low_status','Tire Pressure Low Status','Status tekanan ban rendah.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_tracker_counted_fuel_consumed','Tracker Counted Fuel Consumed','Bahan bakar yang dihitung oleh pelacak kendaraan.','L','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_tracker_counted_mileage','Tracker Counted Mileage','Jarak tempuh yang dihitung oleh pelacak kendaraan.','km','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_trailer_axle_lift_status_1','Trailer Axle Lift Status 1','Status pengangkatan gandar trailer 1.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_trailer_axle_lift_status_2','Trailer Axle Lift Status 2','Status pengangkatan gandar trailer 2.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_trip_engine_motorhours','Trip Engine Motorhours','Total jam operasi mesin.','h','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_trunk_status','Trunk Status','Status bagasi kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_vehicle_battery_charging_status','Vehicle Battery Charging Status','Status pengisian baterai kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_vehicle_mileage','Vehicle Mileage','Jarak tempuh total kendaraan.','km','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_vehicle_speed','Vehicle Speed','Kecepatan kendaraan saat ini.','km/h','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_warning_indicator_status','Warning Indicator Status','Status indikator peringatan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_wear_brake_pads_indicator_status','Wear Brake Pads Indicator Status','Status indikator keausan kampas rem.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('can_bus.can_webasto_status','Webasto Status','Status pengoperasian sistem pemanas Webasto.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('compressor.press_air_exhaust_temperature','Compressor Air Exhaust Temperature','Suhu udara buangan dari kompresor.','°C','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_air_feed_pressure','Compressor Air Feed Pressure','Tekanan udara yang dihasilkan oleh kompresor.','psi','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_air_filter_used_time','Compressor Air Filter Used Time','Waktu penggunaan filter udara kompresor.','h','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_current_imbalance','Compressor Current Imbalance','Ketidakseimbangan arus kompresor.','%','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_load_time','Compressor Load Time','Waktu beban kerja kompresor.','h','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_lube_grease_used_time','Compressor Lube Grease Used Time','Waktu penggunaan gemuk pelumas kompresor.','h','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_lube_oil_used_time','Compressor Lube Oil Used Time','Waktu penggunaan oli pelumas kompresor.','h','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_machine_status','Compressor Machine Status','Status keseluruhan mesin kompresor.','-','STRING','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_oil_filter_used_time','Compressor Oil Filter Used Time','Waktu penggunaan filter oli kompresor.','h','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_oil_separator_used_time','Compressor Oil Separator Used Time','Waktu penggunaan pemisah oli kompresor.','h','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_phase_a_current','Compressor Phase A Current','Arus fase A pada kompresor.','A','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_phase_b_current','Compressor Phase B Current','Arus fase B pada kompresor.','A','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_phase_c_current','Compressor Phase C Current','Arus fase C pada kompresor.','A','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_run_state_1','Compressor Run State 1','Status 1 operasional kompresor.','-','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_run_state_2','Compressor Run State 2','Status 2 operasional kompresor.','-','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('compressor.press_run_time','Compressor Run Time','Total waktu operasional kompresor.','h','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('general.battery_current','General Battery Current','Arus baterai kendaraan.','A','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('general.battery_voltage','General Battery Voltage','Tegangan baterai kendaraan.','V','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('general.digital_input','General Digital Input','Status input digital sistem.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('general.digital_input_hm','General Digital Input HM','Status input digital dengan mode HM.','-','BOOLEAN','{FLESPI_TELTONIKA}','GENERATED_ON_INPUT'),
    ('general.digital_output','General Digital Output','Status output digital sistem.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('general.external_powersource_voltage','General External Powersource Voltage','Tegangan dari sumber daya eksternal.','V','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('general.gsm_signal_level','General Gsm Signal Level','Kekuatan sinyal GSM kendaraan.','dBm','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('general.movement_status','General Movement Status','Status pergerakan umum kendaraan.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('gps.position_altitude','Gps Position Altitude','Ketinggian kendaraan berdasarkan GPS.','m','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('gps.position_direction','Gps Position Direction','Arah pergerakan kendaraan berdasarkan GPS.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('gps.position_hdop','Gps Position Hdop','Horizontal dilution of precision pada data GPS.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('gps.position_latitude','Gps Position Latitude','Lokasi lintang kendaraan berdasarkan GPS.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('gps.position_longitude','Gps Position Longitude','Lokasi bujur kendaraan berdasarkan GPS.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('gps.position_pdop','Gps Position Pdop','Positional dilution of precision pada data GPS.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('gps.position_satellites','Gps Position Satellites','Jumlah satelit GPS yang terdeteksi.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('gps.position_speed','Gps Position Speed','Kecepatan kendaraan berdasarkan data GPS.','km/h','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('gps.position_valid','Gps Position Valid','Validitas data posisi GPS.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('gps.vehicle_mileage','Gps Vehicle Mileage','Jarak tempuh kendaraan berdasarkan data GPS.','km','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL'),
    ('tyre.battery_voltage','Tyre Battery Voltage','Tegangan baterai pada sistem ban.','V','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('tyre.pressure','Tyre Pressure','Tekanan ban kendaraan.','psi','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL'),
    ('tyre.temperature','Tyre Temperature','Suhu ban kendaraan.','°C','NUMERIC','{SENDQUIP_GATEWAY}','ORIGINAL')
     ON CONFLICT (code) DO NOTHING;


CREATE TABLE IF NOT EXISTS "ins_ALERT_CONDITION_TYPES" (
    code VARCHAR(255) PRIMARY KEY,
    label VARCHAR(255),
    description VARCHAR(255)
);

INSERT INTO
    "ins_ALERT_CONDITION_TYPES" (code, "label", description)
VALUES
    ('AND', 'AND', 'AND'),
    ('OR', 'OR', 'OR')
     ON CONFLICT (code) DO NOTHING;

CREATE TABLE IF NOT EXISTS "ins_ALERT_CONDITION_OPERATORS" (
    code VARCHAR(255) PRIMARY KEY,
    label VARCHAR(255),
    data_types VARCHAR(20)[],
    description VARCHAR(255)
);

INSERT INTO "ins_ALERT_CONDITION_OPERATORS" (code, label, data_types, description) VALUES
('IS_EQUAL_TO', 'Is equal to', ARRAY['NUMERIC', 'STRING', 'BOOLEAN'], '(=)'),
('IS_NOT_EQUAL_TO', 'Is not equal to', ARRAY['NUMERIC', 'STRING', 'BOOLEAN'], '(≠)'),
('GREATER_THAN', 'Greater than', ARRAY['NUMERIC'], '(>)'),
('LESS_THAN', 'Less than', ARRAY['NUMERIC'], '(<)'),
('GREATER_THAN_OR_EQUAL_TO', 'Greater than or equal to', ARRAY['NUMERIC'], '(≥)'),
('LESS_THAN_OR_EQUAL_TO', 'Less than or equal to', ARRAY['NUMERIC'], '(≤)')
ON CONFLICT (code) DO NOTHING;




CREATE TABLE IF NOT EXISTS "ins_alert_configs_v2" (
    id VARCHAR(40) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    "description" TEXT,
    status_code VARCHAR(255) NOT NULL REFERENCES "ins_ALERT_CONFIG_STATUSES"(code),
    asset_ids VARCHAR(40)[],

    condition_type_codes VARCHAR(255) REFERENCES "ins_ALERT_CONDITION_TYPES"(code) DEFAULT 'AND',

    use_actions BOOLEAN,
    action_type_codes VARCHAR(255)[],
    action_user_recipients VARCHAR(40)[],
    action_notify_asset_assignee BOOLEAN,

    ticket_auto_create BOOLEAN,
    ticket_priority_code VARCHAR(255),
    ticket_category_code VARCHAR(255),
    ticket_desc TEXT,
    ticket_assigned_user_id VARCHAR(40),

    deactivate_after_reach BOOLEAN,
    happen_on_asset_ids VARCHAR(40)[],

    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);

CREATE TABLE IF NOT EXISTS "ins_alert_config_conditions" (
    id VARCHAR(40) PRIMARY KEY,
    alert_config_id VARCHAR(40) REFERENCES "ins_alert_configs_v2" (id),
    parameter_code VARCHAR(255) REFERENCES "ins_ALERT_PARAMETERS" (code),
    operator_code VARCHAR(255) REFERENCES "ins_ALERT_CONDITION_OPERATORS" (code), 
    val JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);


CREATE TABLE IF NOT EXISTS "ins_alerts_v2" (
    id VARCHAR(40) PRIMARY KEY,
    alert_config_id VARCHAR(40) REFERENCES "ins_alert_configs_v2"(id),
    asset_id VARCHAR(40),
    parent_asset_id VARCHAR(40),
    "time" TIMESTAMPTZ,
    recorded_value JSONB DEFAULT '{}'::jsonb,
    is_read BOOLEAN DEFAULT false NOT NULL, 
    read_datetime timestamptz NULL,
    ticket_id VARCHAR(40),

    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);

COMMIT;