BEGIN;

INSERT INTO
    "ins_ALERT_CONFIG_CATEGORIES" (code, "label", description, asset_category_code)
VALUES
    ('TYRE_SENSOR', 'Tyre Sensor', '-', 'TYRE') 
    ON CONFLICT (code) DO NOTHING;

INSERT INTO
    "ins_ALERT_CONFIG_SUB_CATEGORIES" (code, "label", description, category_code, uom_codes)
VALUES
    ('TYRE_PRESSURE', 'Tyre Pressure', '-', 'TYRE_SENSOR', '{"psi"}') ON CONFLICT (code) DO NOTHING;


INSERT INTO
    "ins_ALERT_TRIGGERS" (sub_category_code, code, "label", description)
VALUES
    ('TYRE_PRESSURE', 'TYRE_LOW_PRESSURE', 'Tyre pressure drops below a certain threshold', '-')

    ON CONFLICT (code) DO NOTHING;

COMMIT;
