BEGIN;

ALTER TABLE ams_asset_vehicles
ADD COLUMN IF NOT EXISTS max_rtd_diff_tolerance SMALLINT DEFAULT 20,
ADD COLUMN IF NOT EXISTS axle_configuration_v2 JSONB DEFAULT '[]'::jsonb;

UPDATE ams_asset_vehicles
SET axle_configuration_v2 = (
    SELECT jsonb_agg(
        jsonb_build_object(
            'axle', element, 
            'pressure_min', 10, 
            'pressure_max', 20
        )
    )
    FROM unnest(axle_configuration) AS element
) WHERE axle_configuration != '{}' AND axle_configuration IS NOT NULL;

ALTER TABLE ams_asset_vehicles
RENAME COLUMN axle_configuration TO axle_configuration_legacy;

ALTER TABLE ams_asset_vehicles
RENAME COLUMN axle_configuration_v2 TO axle_configuration;

ALTER TABLE ams_vehicles
ADD COLUMN IF NOT EXISTS axle_configuration_v2 JSONB DEFAULT '[]'::jsonb;

UPDATE ams_vehicles
SET axle_configuration_v2 = (
    SELECT jsonb_agg(
        jsonb_build_object(
            'axle', element, 
            'pressure_min', 10, 
            'pressure_max', 20
        )
    )
    FROM unnest(axle_configuration) AS element
) WHERE axle_configuration != '{}' AND axle_configuration IS NOT NULL;

ALTER TABLE ams_vehicles
RENAME COLUMN axle_configuration TO axle_configuration_legacy;

ALTER TABLE ams_vehicles
RENAME COLUMN axle_configuration_v2 TO axle_configuration;


ALTER TABLE ams_asset_inspection_vehicle
ADD COLUMN IF NOT EXISTS max_rtd_diff_tolerance SMALLINT,
ADD COLUMN IF NOT EXISTS axle_configuration_v2 JSONB DEFAULT '[]'::jsonb;

UPDATE ams_asset_inspection_vehicle
SET axle_configuration_v2 = (
    SELECT jsonb_agg(
        jsonb_build_object(
            'axle', element, 
            'pressure_min', 10, 
            'pressure_max', 20
        )
    )
    FROM unnest(axle_configuration) AS element
) WHERE axle_configuration != '{}' AND axle_configuration IS NOT NULL;

ALTER TABLE ams_asset_inspection_vehicle
RENAME COLUMN axle_configuration TO axle_configuration_legacy;

ALTER TABLE ams_asset_inspection_vehicle
RENAME COLUMN axle_configuration_v2 TO axle_configuration;

ALTER TABLE ams_asset_inspection_tyre
ADD COLUMN IF NOT EXISTS is_mismatch BOOLEAN;

ALTER TABLE ams_linked_asset_vehicle_tyres
ADD COLUMN IF NOT EXISTS is_mismatch BOOLEAN;

COMMIT;