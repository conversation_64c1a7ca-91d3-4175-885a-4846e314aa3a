CREATE TABLE IF NOT EXISTS "ams_DIGISPECT_CONFIG_TYPES" (
    "code" CHARACTER VARYING(40) NOT NULL,
    "label" CHARACTER VARYING(40) NOT NULL,
    "description" CHARACTER VARYING(40) NOT NULL,
    PRIMARY KEY (code)
);

INSERT INTO "ams_DIGISPECT_CONFIG_TYPES" values ('VEHICLE','Vehicle',''),('TYRE','Tyre','');

CREATE TABLE IF NOT EXISTS "ams_DIGISPECT_CONFIG_STATUSES" (
    "code" CHARACTER VARYING(40) NOT NULL,
    "label" CHARACTER VARYING(40) NOT NULL,
    "description" CHARACTER VARYING(40) NOT NULL,
    PRIMARY KEY (code)
);

INSERT INTO "ams_DIGISPECT_CONFIG_STATUSES" values ('ACTIVE','Active',''),('INACTIVE','Inactive','');

CREATE TABLE IF NOT EXISTS "ams_digispect_brand_configs" (
    "id" CHARACTER VARYING(40) NOT NULL,
    "client_id" CHARACTER VARYING(40) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "deleted_at" TIMESTAMPTZ NULL,
    "created_by" CHARACTER VARYING(40) NULL,
    "updated_by" CHARACTER VARYING(40) NULL,
    "brand_name" CHARACTER VARYING(40) NOT NULL,
    "type_code" CHARACTER VARYING(40)[] NOT NULL,
    "status_code" character varying(20) NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS "ams_digispect_configs" (
    "id" CHARACTER VARYING(40) NOT NULL,
    "client_id" CHARACTER VARYING(40) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "deleted_at" TIMESTAMPTZ NULL,
    "created_by" CHARACTER VARYING(40) NULL,
    "updated_by" CHARACTER VARYING(40) NULL,
    "brand_digispect_id" CHARACTER VARYING(40) NULL,
    "type_code" CHARACTER VARYING(40) NULL,
    "model" CHARACTER VARYING(40) NULL,
    "size" CHARACTER VARYING(40) NULL,
    "status_code" character varying(20) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_ams_digispect_configs_type 
        FOREIGN KEY (type_code) 
        REFERENCES ams_DIGISPECT_CONFIG_TYPES (code) 
        ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT fk_ams_digispect_configs_status 
        FOREIGN KEY (status_code) 
        REFERENCES ams_DIGISPECT_CONFIG_STATUSES (code) 
        ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT fk_ams_digispect_configs_brand 
        FOREIGN KEY (brand_digispect_id) 
        REFERENCES ams_digispect_brand_configs (id) 
        ON UPDATE NO ACTION ON DELETE NO ACTION
);