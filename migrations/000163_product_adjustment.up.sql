BEGIN;

CREATE TABLE
    IF NOT EXISTS "inv_PRODUCT_TYPES" (
        code VARCHAR(255) PRIMARY KEY,
        label VARCHAR(255) NOT NULL,
        description TEXT NOT NULL
    );

INSERT INTO
    "inv_PRODUCT_TYPES" (code, "label", description)
VALUES
    ('PRODUCT', 'Product', '-'),
    ('SERVICE', 'Service', '-') ON CONFLICT (code) DO NOTHING;

ALTER TABLE "inv_products"
ADD COLUMN IF NOT EXISTS sales_price BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS product_type_code VARCHAR(255) REFERENCES "inv_PRODUCT_TYPES" (code) NOT NULL DEFAULT 'PRODUCT';

COMMIT;