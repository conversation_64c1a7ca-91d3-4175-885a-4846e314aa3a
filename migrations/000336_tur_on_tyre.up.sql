BEGIN;


CREATE TABLE IF NOT EXISTS "ams_UTILIZATION_RATE_PERCENTAGE_STATUSES" (
    code VARCHAR(20) PRIMARY KEY,
    label VARCHAR(255) NOT NULL,
    description VARCHAR(255) NOT NULL
);

INSERT INTO
    "ams_UTILIZATION_RATE_PERCENTAGE_STATUSES" (code, "label", description)
VALUES
    ('GOOD','Good', '0 - 30% TUR (70% left)'),
    ('MODERATE','Moderate', '31-60% TUR (40% left)'),
    ('LOW','Low', '61 - 80% TUR (20% left)'),
    ('CRITICAL','Critical', '81 - 100% TUR (worn out)')
    ON CONFLICT (code) DO NOTHING;

ALTER TABLE ams_asset_tyres
ADD COLUMN IF NOT EXISTS utilization_rate_percentage_status_code VARCHAR(20) REFERENCES "ams_UTILIZATION_RATE_PERCENTAGE_STATUSES" (code);

WITH cte AS (
    SELECT
        asset_id,
        CASE start_thread_depth WHEN NULL THEN NULL WHEN 0 THEN NULL ELSE ((start_thread_depth - average_rtd) / start_thread_depth) * 100 END AS utilization_rate_percentage
    FROM
        ams_asset_tyres
)
UPDATE ams_asset_tyres
SET
    utilization_rate_percentage = cte.utilization_rate_percentage,
    utilization_rate_percentage_status_code = CASE
        WHEN cte.utilization_rate_percentage <= 30 THEN 'GOOD'
        WHEN cte.utilization_rate_percentage <= 60 THEN 'MODERATE'
        WHEN cte.utilization_rate_percentage <= 80 THEN 'LOW'
        WHEN cte.utilization_rate_percentage > 80 THEN 'CRITICAL'
    END
FROM
    cte
WHERE
    cte.asset_id = ams_asset_tyres.asset_id;

CREATE OR REPLACE FUNCTION update_tyre_utilization_rate_percentage()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.start_thread_depth IS NULL THEN
        NEW.utilization_rate_percentage = NULL;
    ELSIF NEW.start_thread_depth = 0 THEN
        NEW.utilization_rate_percentage = NULL;
    ELSE
        NEW.utilization_rate_percentage = ((NEW.start_thread_depth - NEW.average_rtd) / NEW.start_thread_depth) * 100;
    END IF;
    IF NEW.utilization_rate_percentage <= 30 THEN
        NEW.utilization_rate_percentage_status_code = 'GOOD';
    ELSIF NEW.utilization_rate_percentage <= 60 THEN
        NEW.utilization_rate_percentage_status_code = 'MODERATE';
    ELSIF NEW.utilization_rate_percentage <= 80 THEN
        NEW.utilization_rate_percentage_status_code = 'LOW';
    ELSIF NEW.utilization_rate_percentage > 80 THEN
        NEW.utilization_rate_percentage_status_code = 'CRITICAL';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_tyre_utilization_rate_percentage ON ams_asset_tyres;

CREATE TRIGGER update_tyre_utilization_rate_percentage
BEFORE INSERT OR UPDATE ON ams_asset_tyres
FOR EACH ROW
EXECUTE FUNCTION update_tyre_utilization_rate_percentage();

COMMIT;