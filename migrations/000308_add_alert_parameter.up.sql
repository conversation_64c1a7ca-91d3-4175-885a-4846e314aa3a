-- CAN BUS IOT PARAMETER

INSERT INTO
    "ins_ALERT_PARAMETERS" (code,label,description,unit,data_type,source_codes,type_code,icon_path)
VALUES
    ('can_bus.can_engine_torque','Engine Torque','Torsi mesin kendaraan.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),
    ('can_bus.can_ambient_air_temperature','Ambient Air Temperature','Suhu udara di sekitar kendaraan.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),
    ('can_bus.can_engine_coolant_temperature','Engine Coolant Temperature','<PERSON><PERSON> cairan pendingin mesin.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),
    ('can_bus.can_fuel_consumption','Fuel Consumption','Konsumsi bahan bakar kendaraan dalam satuan liter per jam.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),
    ('can_bus.can_fuel_economy','Fuel Economy','Efisiensi bahan bakar atau jarak tempuh per liter bahan bakar.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),
    ('can_bus.can_mil_status','Malfunction Indicator Lamp Status','Status Malfunction Indicator Lamp (lampu indikator kerusakan).','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),
    ('can_bus.can_wheel_speed','Vehicle Speed','Kecepatan roda kendaraan.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),

    ('general.gsm_operator_code','Operator Network Status','Informasi simcard terhubung ke operator jaringan seluler.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),
    ('general.sd_status','External Memory Card Status','Status kartu SD pada perangkat Teltonika.','-','BOOLEAN','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),
    ('general.x_acceleration','Accelerometer','Accelerometer pada sumbu X.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),
    ('general.y_acceleration','Accelerometer','Accelerometer pada sumbu Y.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),
    ('general.z_acceleration','Accelerometer','Accelerometer pada sumbu Z.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),
    ('general.pto_drive_engagement_enum','Power Take-off','Status Power Take-Off (PTO).','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg'),
    ('general.fuel_consumed','Total Fuel Consumed','Total bahan bakar yang telah digunakan sejak awal periode pemantauan.','-','NUMERIC','{FLESPI_TELTONIKA}','ORIGINAL','https://storage.googleapis.com/assetfindr_public/icons/fleet-optimax/engine-working-status.svg')
ON CONFLICT (code) DO NOTHING;
    
    
    