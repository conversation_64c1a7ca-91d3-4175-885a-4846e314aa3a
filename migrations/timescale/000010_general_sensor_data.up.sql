BEGIN;

CREATE TABLE
    IF NOT EXISTS ssr_general_sensor_data (
        asset_id VARCHAR(40) NOT NULL,
        time TIMESTAMPTZ NOT NULL,
        ident VARCHAR(40),
        created_at TIMESTAMPTZ,
        integration_id varchar(40),
        client_id VARCHAR(40),
        battery_current DOUBLE PRECISION,
        battery_voltage DOUBLE PRECISION,
        gsm_signal_level INT,
        movement_status BOOLEAN
    );

SELECT
    create_hypertable ('ssr_general_sensor_data', by_range ('time'));

SELECT
    add_retention_policy ('ssr_general_sensor_data', INTERVAL '7 days');

COMMIT;