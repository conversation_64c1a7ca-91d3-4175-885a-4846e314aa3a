-- CAN BUS IOT PARAMETER

ALTER TABLE "ssr_can_bus_sensor_data" 
ADD COLUMN IF NOT EXISTS can_engine_torque DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS can_ambient_air_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS can_engine_coolant_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS can_fuel_consumption DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS can_fuel_economy DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS can_mil_status BOOLEAN,
ADD COLUMN IF NOT EXISTS can_wheel_speed DOUBLE PRECISION;

-- 
-- Big Query Alter Query, run this manually
-- 

-- ALTER TABLE `assetfindr.sensor_data.can_bus_sensor_data_v2` 
-- ADD COLUMN can_engine_torque FLOAT64,
-- ADD COLUMN can_ambient_air_temperature FLOAT64,
-- ADD COLUMN can_engine_coolant_temperature FLOAT64,
-- ADD COLUMN can_fuel_consumption FLOAT64,
-- ADD COLUMN can_fuel_economy FLOAT64,
-- ADD COLUMN can_mil_status BOOL,
-- ADD COLUMN can_wheel_speed FLOAT64;




-- GENERAL IOT PARAMETER
ALTER TABLE "ssr_general_sensor_data" 
ADD COLUMN IF NOT EXISTS gsm_operator_code INT,
ADD COLUMN IF NOT EXISTS sd_status BOOLEAN,
ADD COLUMN IF NOT EXISTS x_acceleration DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS y_acceleration DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS z_acceleration DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS pto_drive_engagement_enum INT,
ADD COLUMN IF NOT EXISTS fuel_consumed DOUBLE PRECISION;

-- 
-- Big Query Alter Query, run this manually
-- 

-- ALTER TABLE `assetfindr.sensor_data.general_sensor_data` 
-- ADD COLUMN gsm_operator_code INTEGER,
-- ADD COLUMN sd_status BOOLEAN,
-- ADD COLUMN x_acceleration FLOAT64,
-- ADD COLUMN y_acceleration FLOAT64,
-- ADD COLUMN z_acceleration FLOAT64,
-- ADD COLUMN pto_drive_engagement_enum INTEGER,
-- ADD COLUMN fuel_consumed FLOAT64;

