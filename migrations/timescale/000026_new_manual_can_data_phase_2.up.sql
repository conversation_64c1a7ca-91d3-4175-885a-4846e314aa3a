ALTER TABLE "ssr_can_bus_sensor_data"
ADD COLUMN IF NOT EXISTS front_axle_speed DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS relative_speed_front_axle_left_wheel DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS relative_speed_front_axle_right_wheel DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS relative_speed_rear_axle_1_left_wheel DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS relative_speed_rear_axle_1_right_wheel DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS relative_speed_rear_axle_2_left_wheel DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS relative_speed_rear_axle_2_right_wheel DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_fuel_rate DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_instantaneous_fuel_economy DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_average_fuel_economy DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_throttle_valve_1_position_1 DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_throttle_valve_2_position DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_driveline_engaged DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_torque_converter_lockup_engaged DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_shift_in_process DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_torque_converter_lockup_transition_in_process DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_output_shaft_speed DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS percent_clutch_slip DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_momentary_overspeed_enable DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS progressive_shift_disable DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS momentary_engine_maximum_power_enable DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_input_shaft_speed DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS source_address_of_controlling_device_for_transmission_control DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_torque_mode DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS actual_engine_percent_torque_fractional DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS driver_s_demand_engine_percent_torque DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS actual_engine_percent_torque DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_speed DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS source_address_of_controlling_device_for_engine_control DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_starter_mode DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_demand_percent_torque DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS accelerator_pedal_1_low_idle_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS accelerator_pedal_kickdown_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS road_speed_limit_status DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS accelerator_pedal_2_low_idle_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS accelerator_pedal_1_position DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_percent_load_at_current_speed DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS remote_accelerator_pedal_position DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS accelerator_pedal_2_position DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS vehicle_acceleration_rate_limit_status DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS momentary_engine_maximum_power_enable_feedback DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS dpf_thermal_management_active DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS scr_thermal_management_active DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS actual_maximum_available_engine_percent_torque DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS estimated_pumping_percent_torque DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS two_speed_axle_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS parking_brake_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS cruise_control_pause_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS park_brake_release_inhibit_request DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS wheel_based_vehicle_speed DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS cruise_control_active DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS cruise_control_enable_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS brake_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS clutch_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS cruise_control_set_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS cruise_control_coast_decelerate_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS cruise_control_resume_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS cruise_control_accelerate_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS cruise_control_set_speed DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS pto_governor_state DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS cruise_control_states DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_idle_increment_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_idle_decrement_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_diagnostic_test_mode_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_shutdown_override_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_gas_recirculation_1_mass_flow_rate DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_intake_air_mass_flow_rate DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_gas_recirculation_2_mass_flow_rate DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS target_fresh_air_mass_flow DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS asr_engine_control_active DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS asr_brake_control_active DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS anti_lock_braking_abs_active DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS ebs_brake_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS brake_pedal_position DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS abs_off_road_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS asr_off_road_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS asr_hill_holder_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS traction_control_override_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS accelerator_interlock_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_derate_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_auxiliary_shutdown_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS remote_accelerator_enable_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_retarder_selection DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS abs_fully_operational DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS ebs_red_warning_signal DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS abs_ebs_amber_warning_signal_powered_vehicle DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS atc_asr_information_signal DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS source_address_of_controlling_device_for_brake_control DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS railroad_mode_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS halt_brake_switch DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS trailer_abs_status DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS tractor_mounted_trailer_abs_warning_signal DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_clutch_1_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_oil_level_1 DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_filter_differential_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_1_oil_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_1_oil_temperature_1 DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_oil_level_1_high_low DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_oil_level_1_countdown_timer DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS transmission_oil_level_1_measurement_status DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS pneumatic_supply_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS parking_and_or_trailer_air_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS service_brake_circuit_1_air_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS service_brake_circuit_2_air_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS auxiliary_equipment_supply_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS air_suspension_supply_pressure_1 DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS air_compressor_status DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS powertrain_circuit_air_supply_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_n_ox_1 DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_percent_oxygen_1 DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_gas_sensor_1_power_in_range DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_gas_sensor_1_at_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_n_ox_1_reading_stable DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_wide_range_percent_oxygen_1_reading_stable DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_gas_sensor_1_heater_preliminary_fmi DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_gas_sensor_1_heater_control DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_n_ox_sensor_1_preliminary_fmi DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_n_ox_sensor_1_self_diagnosis_status DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_oxygen_sensor_1_preliminary_fmi DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_trip_fuel_high_resolution DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_total_fuel_used_high_resolution DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS aftertreatment_1_scr_intake_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS aftertreatment_1_scr_intake_temperature_preliminary_fmi DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS aftertreatment_1_scr_outlet_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS aftertreatment_1_scr_outlet_temperature_preliminary_fmi DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS powered_vehicle_weight DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS gross_combination_vehicle_weight DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS gross_combination_vehicle_weight_confidence DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS total_vehicle_distance_high_resolution DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS trip_distance_high_resolution DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "seconds" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "minutes" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "hours" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "month" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "day" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "year" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS local_minute_offset DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS local_hour_offset DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS barometric_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS cab_interior_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS ambient_air_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_intake_1_air_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS road_surface_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS aftertreatment_1_diesel_particulate_filter_intake_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_intake_manifold_1_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_intake_manifold_1_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_intake_air_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_air_filter_1_differential_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_exhaust_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_coolant_filter_differential_pressure DOUBLE PRECISION;


/*
ALTER TABLE `assetfindr.sensor_data.can_bus_sensor_data_v2`
ADD COLUMN IF NOT EXISTS front_axle_speed FLOAT64,
ADD COLUMN IF NOT EXISTS relative_speed_front_axle_left_wheel FLOAT64,
ADD COLUMN IF NOT EXISTS relative_speed_front_axle_right_wheel FLOAT64,
ADD COLUMN IF NOT EXISTS relative_speed_rear_axle_1_left_wheel FLOAT64,
ADD COLUMN IF NOT EXISTS relative_speed_rear_axle_1_right_wheel FLOAT64,
ADD COLUMN IF NOT EXISTS relative_speed_rear_axle_2_left_wheel FLOAT64,
ADD COLUMN IF NOT EXISTS relative_speed_rear_axle_2_right_wheel FLOAT64,
ADD COLUMN IF NOT EXISTS engine_fuel_rate FLOAT64,
ADD COLUMN IF NOT EXISTS engine_instantaneous_fuel_economy FLOAT64,
ADD COLUMN IF NOT EXISTS engine_average_fuel_economy FLOAT64,
ADD COLUMN IF NOT EXISTS engine_throttle_valve_1_position_1 FLOAT64,
ADD COLUMN IF NOT EXISTS engine_throttle_valve_2_position FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_driveline_engaged FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_torque_converter_lockup_engaged FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_shift_in_process FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_torque_converter_lockup_transition_in_process FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_output_shaft_speed FLOAT64,
ADD COLUMN IF NOT EXISTS percent_clutch_slip FLOAT64,
ADD COLUMN IF NOT EXISTS engine_momentary_overspeed_enable FLOAT64,
ADD COLUMN IF NOT EXISTS progressive_shift_disable FLOAT64,
ADD COLUMN IF NOT EXISTS momentary_engine_maximum_power_enable FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_input_shaft_speed FLOAT64,
ADD COLUMN IF NOT EXISTS source_address_of_controlling_device_for_transmission_control FLOAT64,
ADD COLUMN IF NOT EXISTS engine_torque_mode FLOAT64,
ADD COLUMN IF NOT EXISTS actual_engine_percent_torque_fractional FLOAT64,
ADD COLUMN IF NOT EXISTS driver_s_demand_engine_percent_torque FLOAT64,
ADD COLUMN IF NOT EXISTS actual_engine_percent_torque FLOAT64,
ADD COLUMN IF NOT EXISTS engine_speed FLOAT64,
ADD COLUMN IF NOT EXISTS source_address_of_controlling_device_for_engine_control FLOAT64,
ADD COLUMN IF NOT EXISTS engine_starter_mode FLOAT64,
ADD COLUMN IF NOT EXISTS engine_demand_percent_torque FLOAT64,
ADD COLUMN IF NOT EXISTS accelerator_pedal_1_low_idle_switch FLOAT64,
ADD COLUMN IF NOT EXISTS accelerator_pedal_kickdown_switch FLOAT64,
ADD COLUMN IF NOT EXISTS road_speed_limit_status FLOAT64,
ADD COLUMN IF NOT EXISTS accelerator_pedal_2_low_idle_switch FLOAT64,
ADD COLUMN IF NOT EXISTS accelerator_pedal_1_position FLOAT64,
ADD COLUMN IF NOT EXISTS engine_percent_load_at_current_speed FLOAT64,
ADD COLUMN IF NOT EXISTS remote_accelerator_pedal_position FLOAT64,
ADD COLUMN IF NOT EXISTS accelerator_pedal_2_position FLOAT64,
ADD COLUMN IF NOT EXISTS vehicle_acceleration_rate_limit_status FLOAT64,
ADD COLUMN IF NOT EXISTS momentary_engine_maximum_power_enable_feedback FLOAT64,
ADD COLUMN IF NOT EXISTS dpf_thermal_management_active FLOAT64,
ADD COLUMN IF NOT EXISTS scr_thermal_management_active FLOAT64,
ADD COLUMN IF NOT EXISTS actual_maximum_available_engine_percent_torque FLOAT64,
ADD COLUMN IF NOT EXISTS estimated_pumping_percent_torque FLOAT64,
ADD COLUMN IF NOT EXISTS two_speed_axle_switch FLOAT64,
ADD COLUMN IF NOT EXISTS parking_brake_switch FLOAT64,
ADD COLUMN IF NOT EXISTS cruise_control_pause_switch FLOAT64,
ADD COLUMN IF NOT EXISTS park_brake_release_inhibit_request FLOAT64,
ADD COLUMN IF NOT EXISTS wheel_based_vehicle_speed FLOAT64,
ADD COLUMN IF NOT EXISTS cruise_control_active FLOAT64,
ADD COLUMN IF NOT EXISTS cruise_control_enable_switch FLOAT64,
ADD COLUMN IF NOT EXISTS brake_switch FLOAT64,
ADD COLUMN IF NOT EXISTS clutch_switch FLOAT64,
ADD COLUMN IF NOT EXISTS cruise_control_set_switch FLOAT64,
ADD COLUMN IF NOT EXISTS cruise_control_coast_decelerate_switch FLOAT64,
ADD COLUMN IF NOT EXISTS cruise_control_resume_switch FLOAT64,
ADD COLUMN IF NOT EXISTS cruise_control_accelerate_switch FLOAT64,
ADD COLUMN IF NOT EXISTS cruise_control_set_speed FLOAT64,
ADD COLUMN IF NOT EXISTS pto_governor_state FLOAT64,
ADD COLUMN IF NOT EXISTS cruise_control_states FLOAT64,
ADD COLUMN IF NOT EXISTS engine_idle_increment_switch FLOAT64,
ADD COLUMN IF NOT EXISTS engine_idle_decrement_switch FLOAT64,
ADD COLUMN IF NOT EXISTS engine_diagnostic_test_mode_switch FLOAT64,
ADD COLUMN IF NOT EXISTS engine_shutdown_override_switch FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_gas_recirculation_1_mass_flow_rate FLOAT64,
ADD COLUMN IF NOT EXISTS engine_intake_air_mass_flow_rate FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_gas_recirculation_2_mass_flow_rate FLOAT64,
ADD COLUMN IF NOT EXISTS target_fresh_air_mass_flow FLOAT64,
ADD COLUMN IF NOT EXISTS asr_engine_control_active FLOAT64,
ADD COLUMN IF NOT EXISTS asr_brake_control_active FLOAT64,
ADD COLUMN IF NOT EXISTS anti_lock_braking_abs_active FLOAT64,
ADD COLUMN IF NOT EXISTS ebs_brake_switch FLOAT64,
ADD COLUMN IF NOT EXISTS brake_pedal_position FLOAT64,
ADD COLUMN IF NOT EXISTS abs_off_road_switch FLOAT64,
ADD COLUMN IF NOT EXISTS asr_off_road_switch FLOAT64,
ADD COLUMN IF NOT EXISTS asr_hill_holder_switch FLOAT64,
ADD COLUMN IF NOT EXISTS traction_control_override_switch FLOAT64,
ADD COLUMN IF NOT EXISTS accelerator_interlock_switch FLOAT64,
ADD COLUMN IF NOT EXISTS engine_derate_switch FLOAT64,
ADD COLUMN IF NOT EXISTS engine_auxiliary_shutdown_switch FLOAT64,
ADD COLUMN IF NOT EXISTS remote_accelerator_enable_switch FLOAT64,
ADD COLUMN IF NOT EXISTS engine_retarder_selection FLOAT64,
ADD COLUMN IF NOT EXISTS abs_fully_operational FLOAT64,
ADD COLUMN IF NOT EXISTS ebs_red_warning_signal FLOAT64,
ADD COLUMN IF NOT EXISTS abs_ebs_amber_warning_signal_powered_vehicle FLOAT64,
ADD COLUMN IF NOT EXISTS atc_asr_information_signal FLOAT64,
ADD COLUMN IF NOT EXISTS source_address_of_controlling_device_for_brake_control FLOAT64,
ADD COLUMN IF NOT EXISTS railroad_mode_switch FLOAT64,
ADD COLUMN IF NOT EXISTS halt_brake_switch FLOAT64,
ADD COLUMN IF NOT EXISTS trailer_abs_status FLOAT64,
ADD COLUMN IF NOT EXISTS tractor_mounted_trailer_abs_warning_signal FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_clutch_1_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_oil_level_1 FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_filter_differential_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_1_oil_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_1_oil_temperature_1 FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_oil_level_1_high_low FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_oil_level_1_countdown_timer FLOAT64,
ADD COLUMN IF NOT EXISTS transmission_oil_level_1_measurement_status FLOAT64,
ADD COLUMN IF NOT EXISTS pneumatic_supply_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS parking_and_or_trailer_air_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS service_brake_circuit_1_air_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS service_brake_circuit_2_air_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS auxiliary_equipment_supply_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS air_suspension_supply_pressure_1 FLOAT64,
ADD COLUMN IF NOT EXISTS air_compressor_status FLOAT64,
ADD COLUMN IF NOT EXISTS powertrain_circuit_air_supply_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_n_ox_1 FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_percent_oxygen_1 FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_gas_sensor_1_power_in_range FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_gas_sensor_1_at_temperature FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_n_ox_1_reading_stable FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_wide_range_percent_oxygen_1_reading_stable FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_gas_sensor_1_heater_preliminary_fmi FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_gas_sensor_1_heater_control FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_n_ox_sensor_1_preliminary_fmi FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_n_ox_sensor_1_self_diagnosis_status FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_1_oxygen_sensor_1_preliminary_fmi FLOAT64,
ADD COLUMN IF NOT EXISTS engine_trip_fuel_high_resolution FLOAT64,
ADD COLUMN IF NOT EXISTS engine_total_fuel_used_high_resolution FLOAT64,
ADD COLUMN IF NOT EXISTS aftertreatment_1_scr_intake_temperature FLOAT64,
ADD COLUMN IF NOT EXISTS aftertreatment_1_scr_intake_temperature_preliminary_fmi FLOAT64,
ADD COLUMN IF NOT EXISTS aftertreatment_1_scr_outlet_temperature FLOAT64,
ADD COLUMN IF NOT EXISTS aftertreatment_1_scr_outlet_temperature_preliminary_fmi FLOAT64,
ADD COLUMN IF NOT EXISTS powered_vehicle_weight FLOAT64,
ADD COLUMN IF NOT EXISTS gross_combination_vehicle_weight FLOAT64,
ADD COLUMN IF NOT EXISTS gross_combination_vehicle_weight_confidence FLOAT64,
ADD COLUMN IF NOT EXISTS total_vehicle_distance_high_resolution FLOAT64,
ADD COLUMN IF NOT EXISTS trip_distance_high_resolution FLOAT64,
ADD COLUMN IF NOT EXISTS seconds FLOAT64,
ADD COLUMN IF NOT EXISTS minutes FLOAT64,
ADD COLUMN IF NOT EXISTS hours FLOAT64,
ADD COLUMN IF NOT EXISTS month FLOAT64,
ADD COLUMN IF NOT EXISTS day FLOAT64,
ADD COLUMN IF NOT EXISTS year FLOAT64,
ADD COLUMN IF NOT EXISTS local_minute_offset FLOAT64,
ADD COLUMN IF NOT EXISTS local_hour_offset FLOAT64,
ADD COLUMN IF NOT EXISTS barometric_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS cab_interior_temperature FLOAT64,
ADD COLUMN IF NOT EXISTS ambient_air_temperature FLOAT64,
ADD COLUMN IF NOT EXISTS engine_intake_1_air_temperature FLOAT64,
ADD COLUMN IF NOT EXISTS road_surface_temperature FLOAT64,
ADD COLUMN IF NOT EXISTS aftertreatment_1_diesel_particulate_filter_intake_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS engine_intake_manifold_1_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS engine_intake_manifold_1_temperature FLOAT64,
ADD COLUMN IF NOT EXISTS engine_intake_air_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS engine_air_filter_1_differential_pressure FLOAT64,
ADD COLUMN IF NOT EXISTS engine_exhaust_temperature FLOAT64,
ADD COLUMN IF NOT EXISTS engine_coolant_filter_differential_pressure FLOAT64;
*/

