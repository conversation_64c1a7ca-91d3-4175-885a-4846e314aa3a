BEGIN;

CREATE TABLE
    IF NOT EXISTS ssr_can_bus_sensor_data (
        asset_id VARCHAR(40) NOT NULL,
        time TIMESTAMPTZ NOT NULL,
        ident VARCHAR(255),
        created_at TIMESTAMPTZ,
        integration_id varchar(40),
        client_id VARCHAR(40),
        can_abs_failure_indicator_status BOOLEAN,
        can_additional_front_lights_status BOOLEAN,
        can_additional_rear_lights_status BOOLEAN,
        can_air_condition_status BOOLEAN,
        can_airbag_indicator_status BOOLEAN,
        can_automatic_retarder_status BOOLEAN,
        can_battery_indicator_status BOOLEAN,
        can_car_closed_remote_status BOOLEAN,
        can_car_closed_status BOOLEAN,
        can_central_differential_4_hi_status BOOLEAN,
        can_central_differential_4_lo_status BOOLEAN,
        can_check_engine_indicator_status BOOLEAN,
        can_cng_status BOOLEAN,
        can_connection_state_1 INTEGER,
        can_connection_state_2 INTEGER,
        can_connection_state_3 INTEGER,
        can_coolant_level_low_indicator_status BOOLEAN,
        can_cruise_status BOOLEAN,
        can_drive_gear_status BOOLEAN,
        can_driver_seatbelt_indicator_status BOOLEAN,
        can_driver_seatbelt_status BOOLEAN,
        can_dynamic_ignition_status BOOLEAN,
        can_electric_engine_status BOOLEAN,
        can_electronic_power_control_status BOOLEAN,
        can_engine_ignition_status BOOLEAN,
        can_engine_load_level INTEGER,
        can_engine_lock_status BOOLEAN,
        can_engine_motorhours FLOAT8,
        can_engine_rpm INTEGER,
        can_engine_temperature FLOAT8,
        can_engine_working_status BOOLEAN,
        can_eps_indicator_status BOOLEAN,
        can_esp_indicator_status BOOLEAN,
        can_esp_status BOOLEAN,
        can_factory_armed_status BOOLEAN,
        can_front_differential_status BOOLEAN,
        can_front_fog_lights_status BOOLEAN,
        can_front_left_door_status BOOLEAN,
        can_front_passenger_seatbelt_status BOOLEAN,
        can_front_passenger_status BOOLEAN,
        can_front_right_door_status BOOLEAN,
        can_fuel_consumed FLOAT8,
        can_fuel_level_low_indicator_status BOOLEAN,
        can_glow_plug_indicator_status BOOLEAN,
        can_handbrake_indicator_status BOOLEAN,
        can_handbrake_status BOOLEAN,
        can_high_beam_status BOOLEAN,
        can_hood_status BOOLEAN,
        can_ignition_key_status BOOLEAN,
        can_interlock_active BOOLEAN,
        can_light_signal_status BOOLEAN,
        can_lights_failure_indicator_status BOOLEAN,
        can_lights_hazard_lights_status BOOLEAN,
        can_low_beam_status BOOLEAN,
        can_maintenance_required_status BOOLEAN,
        can_manual_retarder_status BOOLEAN,
        can_module_sleep_mode BOOLEAN,
        can_neutral_gear_status BOOLEAN,
        can_oil_pressure_indicator_status BOOLEAN,
        can_operator_present_status BOOLEAN,
        can_parking_lights_status BOOLEAN,
        can_parking_status BOOLEAN,
        can_passenger_seatbelt_indicator_status BOOLEAN,
        can_pedal_brake_status BOOLEAN,
        can_pedal_clutch_status BOOLEAN,
        can_private_status BOOLEAN,
        can_program_id INTEGER,
        can_pto_status BOOLEAN,
        can_ready_to_drive_indicator_status BOOLEAN,
        can_rear_central_passenger_seatbelt_status BOOLEAN,
        can_rear_differential_status BOOLEAN,
        can_rear_fog_lights_status BOOLEAN,
        can_rear_left_door_status BOOLEAN,
        can_rear_left_passenger_seatbelt_status BOOLEAN,
        can_rear_right_door_status BOOLEAN,
        can_rear_right_passenger_seatbelt_status BOOLEAN,
        can_reverse_gear_status BOOLEAN,
        can_roof_opened_status BOOLEAN,
        can_soot_filter_indicator_status BOOLEAN,
        can_standalone_engine BOOLEAN,
        can_stop_indicator_status BOOLEAN,
        can_throttle_pedal_level INTEGER,
        can_tire_pressure_low_status BOOLEAN,
        can_tracker_counted_fuel_consumed FLOAT8,
        can_tracker_counted_mileage FLOAT8,
        can_trailer_axle_lift_status_1 BOOLEAN,
        can_trailer_axle_lift_status_2 BOOLEAN,
        can_trip_engine_motorhours FLOAT8,
        can_trunk_status BOOLEAN,
        can_vehicle_battery_charging_status BOOLEAN,
        can_vehicle_mileage FLOAT8,
        can_vehicle_speed INTEGER,
        can_warning_indicator_status BOOLEAN,
        can_wear_brake_pads_indicator_status BOOLEAN,
        can_webasto_status BOOLEAN
    );


SELECT create_hypertable('ssr_can_bus_sensor_data', by_range('time'));

SELECT add_retention_policy('ssr_can_bus_sensor_data', INTERVAL '7 days');


COMMIT;