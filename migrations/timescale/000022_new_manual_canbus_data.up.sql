ALTER TABLE "ssr_can_bus_sensor_data"
ADD COLUMN IF NOT EXISTS engine_coolant_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_fuel_1_temperature_1 DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_oil_temperature_1 DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_turbocharger_1_oil_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_intercooler_temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_charge_air_cooler_thermostat_opening DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_fuel_delivery_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_extended_crankcase_blow_by_pressure DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_oil_level DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_oil_pressure_1 DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_crankcase_pressure_1 DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_coolant_pressure_1 DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_coolant_level_1 DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_total_hours_of_operation DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS engine_total_revolutions DOUBLE PRECISION;