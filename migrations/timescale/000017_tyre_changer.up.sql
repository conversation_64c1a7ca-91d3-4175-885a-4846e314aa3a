BEGIN;

CREATE TABLE
    IF NOT EXISTS ssr_tyre_changer_data (
        asset_id VARCHAR(40) NOT NULL,
        time TIMESTAMPTZ NOT NULL,
        ident VARCHAR(40),
        created_at TIMESTAMPTZ,
        integration_id varchar(40),
        client_id VARCHAR(40),
        current DOUBLE PRECISION,
        power DOUBLE PRECISION,
        today DOUBLE PRECISION,
        total DOUBLE PRECISION,
        voltage DOUBLE PRECISION,
        yesterday DOUBLE PRECISION,
        hm BIGINT
    );

SELECT
    create_hypertable ('ssr_tyre_changer_data', by_range ('time'));

CREATE TABLE
    IF NOT EXISTS ssr_tyre_changer_hm_data (
        integration_id varchar(40) PRIMARY KEY,
        asset_id VARCHAR(40) NOT NULL,
        time TIMESTAMPTZ NOT NULL,
        ident VARCHAR(40),
        created_at TIMESTAMPTZ,
        client_id VARCHAR(40),
        hm BIGINT
    );

CREATE TABLE
    IF NOT EXISTS ssr_tyre_changer_state_data (
        asset_id VARCHAR(40) NOT NULL,
        time TIMESTAMPTZ NOT NULL,
        ident VARCHAR(40),
        created_at TIMESTAMPTZ,
        integration_id varchar(40),
        client_id VARCHAR(40),
        uptime VARCHAR(255),
        uptime_sec BIGINT
    );

SELECT
    create_hypertable ('ssr_tyre_changer_state_data', by_range ('time'));

COMMIT;