BEGIN;

CREATE TABLE
    IF NOT EXISTS ssr_tyre_sensor_data (
        asset_id VARCHAR(40) NOT NULL,
        time TIMESTAMPTZ NOT NULL,
        ident VARCHAR(40),
        created_at TIMESTAMPTZ,
        integration_id varchar(40),
        client_id VARCHAR(40),
        ident_mac_address VARCHAR(255),
        battery_voltage DOUBLE PRECISION,
        pressure DOUBLE PRECISION,
        temperature DOUBLE PRECISION
    );

SELECT
    create_hypertable ('ssr_tyre_sensor_data', by_range ('time'));

SELECT
    add_retention_policy ('ssr_tyre_sensor_data', INTERVAL '30 days');

COMMIT;