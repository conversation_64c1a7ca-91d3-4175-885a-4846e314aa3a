BEGIN;

CREATE TABLE
    IF NOT EXISTS ssr_compressor_sensor_data (
        asset_id VARCHAR(40) NOT NULL,
        time TIMESTAMPTZ NOT NULL,
        device_id VARCHAR(40),
        created_at TIMESTAMPTZ,
        integration_id varchar(40),
        client_id VARCHAR(40),
        press_air_feed_pressure DOUBLE PRECISION,
        press_air_exhaust_temperature DOUBLE PRECISION,
        press_run_time DOUBLE PRECISION,
        press_load_time DOUBLE PRECISION,
        press_phase_a_current DOUBLE PRECISION,
        press_phase_b_current DOUBLE PRECISION,
        press_phase_c_current DOUBLE PRECISION,
        press_run_state_1 DOUBLE PRECISION,
        press_run_state_2 DOUBLE PRECISION,
        press_oil_filter_used_time DOUBLE PRECISION,
        press_oil_separator_used_time DOUBLE PRECISION,
        press_air_filter_used_time DOUBLE PRECISION,
        press_lube_oil_used_time DOUBLE PRECISION,
        press_lube_grease_used_time DOUBLE PRECISION
    );

SELECT
    create_hypertable ('ssr_compressor_sensor_data', by_range ('time'));

SELECT
    add_retention_policy ('ssr_compressor_sensor_data', INTERVAL '7 days');

COMMIT;