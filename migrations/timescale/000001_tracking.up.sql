BEGIN;

CREATE TABLE
    IF NOT EXISTS "geo_LOCATION_HISTORY_TARGETS" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "geo_LOCATION_HISTORY_TARGETS" (code, "label", description)
VALUES
    ('GPSID', 'GPS.ID', '-') ON CONFLICT (code) DO NOTHING;
   
CREATE TABLE
    IF NOT EXISTS geo_location_histories (
        time TIMESTAMPTZ NOT NULL,
        id VARCHAR(40),
        long DOUBLE PRECISION NOT NULL,
        lat DOUBLE PRECISION NOT NULL,
        km BIGINT NOT NULL,
        speed INT NOT NULL,
        angle SMALLINT NOT NULL,
        imei TEXT NOT NULL,
        asset_id VARCHAR(40) NOT NULL,
        target_code VARCHAR(20) REFERENCES "geo_LOCATION_HISTORY_TARGETS" (code) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        client_id VARCHAR(40) NOT NULL
    );
 
CREATE UNIQUE INDEX "idx_geo_location_histories_pk" ON "geo_location_histories" ("id","time");
CREATE INDEX "idx_geo_location_histories_asset_id" ON "geo_location_histories" ("asset_id");
CREATE INDEX "idx_geo_location_histories_target_code" ON "geo_location_histories" ("target_code");

SELECT create_hypertable('geo_location_histories', by_range('time'));

COMMIT;