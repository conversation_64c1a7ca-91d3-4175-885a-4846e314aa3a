BEGIN;

ALTER TABLE geo_location_histories
ADD COLUMN IF NOT EXISTS integration_id varchar(40);

CREATE TABLE
    IF NOT EXISTS "geo_pre_gps_vehicle_meter" (
        id VARCHAR(40) NOT NULL,
        integration_id VARCHAR(40) NOT NULL,
        asset_id VARCHAR(40) NOT NULL,
        first_vehicle_km BIGINT NOT NULL,
        first_gps_km BIGINT NOT NULL,
        target_code VARCHAR(20) REFERENCES "geo_LOCATION_HISTORY_TARGETS" (code) NOT NULL,
        updated_at TIMESTAMPTZ,
        created_at TIMESTAMPTZ NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        deleted_at TIMESTAMPTZ
    );

COMMIT;