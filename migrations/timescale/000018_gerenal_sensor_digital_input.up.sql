BEGIN;

ALTER TABLE "ssr_general_sensor_data" 
ADD COLUMN IF NOT EXISTS digital_input BOOLEAN,
ADD COLUMN IF NOT EXISTS digital_output BOOLEAN;

CREATE TABLE
    IF NOT EXISTS ssr_digital_input_pre_hm (
        asset_id VARCHAR(40) NOT NULL,
        ident VARCHAR(40),
        created_at TIMESTAMPTZ,
        integration_id varchar(40),
        client_id VARCHAR(40),
        pre_hm DOUBLE PRECISION
    );

COMMIT;