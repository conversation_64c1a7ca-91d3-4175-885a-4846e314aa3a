BEGIN;

-- Migration for creating geo_geofence_in_out_history_status
CREATE TABLE
    IF NOT EXISTS "geo_GEOFENCE_IN_OUT_HISTORY_STATUS" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

-- Migration for creating geo_geofence_in_out_history
CREATE TABLE
    IF NOT EXISTS geo_geofence_in_out_history (
        id VARCHAR(40) PRIMARY KEY,
        asset_id VARCHAR(40) NOT NULL,
        geofence_id VARCHAR(40) NOT NULL,
        long DOUBLE PRECISION NOT NULL,
        lat DOUBLE PRECISION NOT NULL,
        time TIMESTAMPTZ NOT NULL,
        identifier JSONB,
        status_code VARCHAR(20) NOT NULL REFERENCES "geo_GEOFENCE_IN_OUT_HISTORY_STATUS" (code),
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
        deleted_at TIMESTAMPTZ
    );

CREATE INDEX idx_geo_geofence_in_out_history_created_at ON geo_geofence_in_out_history (created_at);

INSERT INTO
    "geo_GEOFENCE_IN_OUT_HISTORY_STATUS" (code, label, description)
VALUES
    ('IN', 'In', 'Asset entered the geofence'),
    ('OUT', 'Out', 'Asset exited the geofence') ON CONFLICT (code) DO NOTHING;

COMMIT;