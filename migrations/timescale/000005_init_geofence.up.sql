BEGIN;

CREATE TABLE
    IF NOT EXISTS "geo_GEOFENCE_TYPES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "geo_GEOFENCE_TYPES" (code, "label", description)
VALUES
    ('RECTANGLE', 'Rectangle', '-'),
    ('POLYGON', 'Polygon', '-') ON CONFLICT (code) DO NOTHING;

ALTER TABLE geo_fences RENAME TO geo_geofences;
ALTER TABLE "geo_geofences" DROP COLUMN IF EXISTS asset_id;
ALTER TABLE "geo_geofences" RENAME area TO coordinates;

ALTER TABLE "geo_geofences"
ADD COLUMN IF NOT EXISTS "name" VARCHAR(100) NOT NULL,
ADD COLUMN IF NOT EXISTS "type_code" VARCHAR(20) NOT NULL,
ADD COLUMN IF NOT EXISTS "description" VARCHAR(255),
ADD COLUMN IF NOT EXISTS "perimeter" NUMERIC(14, 2) NOT NULL,
ADD COLUMN IF NOT EXISTS "area" NUMERIC(14, 2) NOT NULL,
ADD COLUMN IF NOT EXISTS "height" NUMERIC(14, 2),
ADD COLUMN IF NOT EXISTS "color" VARCHAR(100);

ALTER TABLE geo_geofences
ADD CONSTRAINT fk_geo_geofences_type_code foreign key (type_code) references "geo_GEOFENCE_TYPES" (code);
   
CREATE TABLE
    IF NOT EXISTS geo_geofence_references (
        id VARCHAR(40) PRIMARY KEY,
        asset_id VARCHAR(40) NOT NULL,
        geofence_id VARCHAR(40) REFERENCES "geo_geofences" (id) NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );

CREATE INDEX "idx_geo_geofence_references_deleted_at" ON "geo_geofence_references" ("deleted_at");

COMMIT;