BEGIN;

ALTER TABLE "ins_alerts"
ADD COLUMN IF NOT EXISTS "time" TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS "recorded_value" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "is_read" bool DEFAULT false NOT NULL, 
ADD COLUMN IF NOT EXISTS "read_datetime" timestamptz NULL;

ALTER TABLE "ins_ALERT_CONFIG_CATEGORIES" RENAME COLUMN asset_sub_category_code TO asset_category_code;

INSERT INTO "tks_TICKET_CATEGORIES"  ("code" , "label" , "description")
VALUES 
('ASSET_ALERT', 'Asset Alert', '-'),
('TYRE_ALERT', 'Tyre Alert', '-') ON CONFLICT (code) DO NOTHING;

INSERT INTO
    "nts_NOTIFICATION_SOURCE" (code, label, description, created_at, updated_at)
VALUES
    (
        'ALERT',
        '<PERSON><PERSON>',
        '<PERSON><PERSON>',
        NOW (),
        NOW ()
    ) ON CONFLICT (code) DO NOTHING;

ALTER TABLE tks_tickets ADD COLUMN IF NOT EXISTS "is_archived" BOOLEAN DEFAULT FALSE NOT NULL;

COMMIT;