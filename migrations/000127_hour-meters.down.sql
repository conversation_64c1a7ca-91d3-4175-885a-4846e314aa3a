BEGIN;

ALTER TABLE "ams_asset_vehicles"
DROP COLUMN IF EXISTS vehicle_hm,
DROP COLUMN IF EXISTS use_kilometer,
DROP COLUMN IF EXISTS use_hourmeter;

ALTER TABLE "ams_asset_tyres"
DROP COLUMN IF EXISTS total_hm;


ALTER TABLE "ams_asset_tyres_treads"
DROP COLUMN IF EXISTS total_hm;

ALTER TABLE "ams_linked_asset_vehicle_tyres"
DROP COLUMN IF EXISTS on_linked_vehicle_hm,
DROP COLUMN IF EXISTS on_linked_tyre_hm,
DROP COLUMN IF EXISTS on_unlinked_vehicle_hm,
DROP COLUMN IF EXISTS on_unlinked_tyre_hm;

ALTER TABLE "ams_asset_inspection_vehicle"
DROP COLUMN IF EXISTS vehicle_hm;

ALTER TABLE "ams_asset_inspection_tyre"
DROP COLUMN IF EXISTS vehicle_hm;

COMMIT;