CREATE TABLE
    "ams_ASSET_STATUS_REQUEST_GRADES" (
        "code" character varying(20) NOT NULL,
        "label" character varying(20) NOT NULL,
        "description" character varying(255) NOT NULL,
        PRIMARY KEY ("code")
    );

INSERT INTO
    "ams_ASSET_STATUS_REQUEST_GRADES" (code, "label", description)
VALUES
    ('A', 'A', '-'),
    ('B', 'B', '-'),
    ('C', 'C', '-') ON CONFLICT (code) DO NOTHING;

ALTER TABLE "ams_asset_status_requests"
ADD COLUMN IF NOT EXISTS grade_code VARCHAR(20) REFERENCES "ams_ASSET_STATUS_REQUEST_GRADES" (code),
ADD COLUMN IF NOT EXISTS grade_reason TEXT;

INSERT INTO
    "ams_ASSET_STATUS_REQUEST_REASONS" (code, "label", description, asset_category_code)
VALUES
    ('GENERAL', 'General', '-', 'TYRE'),
    ('TUBE', 'Tube', '-', 'TYRE'),
    ('FLAP', 'Flap', '-', 'TYRE') ON CONFLICT (code) DO NOTHING;

ALTER TABLE "ams_ASSET_STATUS_REQUEST_SUB_REASONS"
ALTER COLUMN "code" TYPE VARCHAR(255),
ALTER COLUMN "description" TYPE TEXT,
ALTER COLUMN "label" TYPE VARCHAR(255);

INSERT INTO "ams_ASSET_STATUS_REQUEST_SUB_REASONS"
    (code, "label", description, reason_code)
VALUES
    ('GENERAL_UNIFORMITY', 'Uniformity', '-', 'GENERAL'),
    ('GENERAL_WORKMANSHIP', 'Workmanship', '-', 'GENERAL'),
    ('GENERAL_CONTAMINATION', 'Contamination', '-', 'GENERAL'),
    ('GENERAL_OTHERS', 'Others', '-', 'GENERAL'),
    ('TUBE_UNDER_CURE', 'Under Cure', '-', 'TUBE'),
    ('TUBE_BLISTER', 'Blister', '-', 'TUBE'),
    ('TUBE_UNDULATION', 'Undulation', '-', 'TUBE'),
    ('TUBE_PINHOLE', 'Pinhole', '-', 'TUBE'),
    ('TUBE_VALVE_STEM', 'Valve Stem', '-', 'TUBE'),
    ('TUBE_CRACK', 'Crack', '-', 'TUBE'),
    ('TUBE_CUT', 'Cut', '-', 'TUBE'),
    ('TUBE_REPAIRED', 'Repaired', '-', 'TUBE'),
    ('TUBE_RUN_FLAT', 'Run Flat', '-', 'TUBE'),
    ('TUBE_OPEN_SPLICE', 'Open Splice', '-', 'TUBE'),
    ('TUBE_WORKMANSHIP', 'Workmanship', '-', 'TUBE'),
    ('TUBE_CONTAMINATION', 'Contamination', '-', 'TUBE'),
    ('TUBE_OTHERS', 'Others', '-', 'TUBE'),
    ('FLAP_UNDER_CURE', 'Under Cure', '-', 'FLAP'),
    ('FLAP_BLISTER', 'Blister', '-', 'FLAP'),
    ('FLAP_UNDULATION', 'Undulation', '-', 'FLAP'),
    ('FLAP_CUT', 'Cut', '-', 'FLAP'),
    ('FLAP_RUN_FLAT', 'Run Flat', '-', 'FLAP'),
    ('FLAP_WORKMANSHIP', 'Workmanship', '-', 'FLAP'),
    ('FLAP_CONTAMINATION', 'Contamination', '-', 'FLAP'),
    ('FLAP_OTHERS', 'Others', '-', 'FLAP'),
    ('TREAD_SEPARATION', 'Separation', '-', 'TREAD'),
    ('TREAD_CUT', 'Cut', '-', 'TREAD'),
    ('TREAD_CHIPPING', 'Chipping', '-', 'TREAD'),
    ('TREAD_CHUNKING', 'Chunking', '-', 'TREAD'),
    ('TREAD_GROOVE_CRACK', 'Groove Crack', '-', 'TREAD'),
    ('TREAD_PUNCTURE', 'Puncture', '-', 'TREAD'),
    ('TREAD_BURST', 'Burst', '-', 'TREAD'),
    ('TREAD_IRREGULAR_WEAR', 'Irregular Wear', '-', 'TREAD'),
    ('TREAD_TEAR', 'Tear', '-', 'TREAD'),
    ('TREAD_DISTORSION', 'Distorsion', '-', 'TREAD'),
    ('TREAD_OPEN_TREAD_SPLICE', 'Open Tread Splice', '-', 'TREAD'),
    ('TREAD_WORKMANSHIP', 'Workmanship', '-', 'TREAD'),
    ('TREAD_CONTAMINATION', 'Contamination', '-', 'TREAD'),
    ('TREAD_OTHERS', 'Others', '-', 'TREAD'),
    ('SHOULDER_SEPARATION', 'Separation', '-', 'SHOULDER'),
    ('SHOULDER_CUT', 'Cut', '-', 'SHOULDER'),
    ('SHOULDER_BREAK', 'Break', '-', 'SHOULDER'),
    ('SHOULDER_WORKMANSHIP', 'Workmanship', '-', 'SHOULDER'),
    ('SHOULDER_CONTAMINATION', 'Contamination', '-', 'SHOULDER'),
    ('SHOULDER_OTHERS', 'Others', '-', 'SHOULDER'),
    ('SIDEWALL_SEPARATION', 'Separation', '-', 'SIDEWALL'),
    ('SIDEWALL_CUT', 'Cut', '-', 'SIDEWALL'),
    ('SIDEWALL_BREAK', 'Break', '-', 'SIDEWALL'),
    ('SIDEWALL_RUN_FLAT', 'Run Flat', '-', 'SIDEWALL'),
    ('SIDEWALL_BULGE', 'Bulge', '-', 'SIDEWALL'),
    ('SIDEWALL_WORKMANSHIP', 'Workmanship', '-', 'SIDEWALL'),
    ('SIDEWALL_CONTAMINATION', 'Contamination', '-', 'SIDEWALL'),
    ('SIDEWALL_OTHERS', 'Others', '-', 'SIDEWALL'),
    ('BEAD_SEPARATION', 'Separation', '-', 'BEAD'),
    ('BEAD_CUT ', 'Cut ', '-', 'BEAD'),
    ('BEAD_BREAK', 'Break', '-', 'BEAD'),
    ('BEAD_BURN', 'Burn', '-', 'BEAD'),
    ('BEAD_WORKMANSHIP', 'Workmanship', '-', 'BEAD'),
    ('BEAD_CONTAMINATION', 'Contamination', '-', 'BEAD'),
    ('BEAD_OTHERS', 'OTHERS', '-', 'BEAD'),
    ('INNER_SEPARATION', 'Separation', '-', 'INNER'),
    ('INNER_RUN_FLAT', 'Run Flat', '-', 'INNER'),
    ('INNER_PLY_CORD_EXPOSED', 'Ply Cord Exposed', '-', 'INNER'),
    ('INNER_UNDULATION', 'Undulation', '-', 'INNER'),
    ('INNER_BROKEN_PLY_CORDS', 'Broken Ply Cords (Bias)', '-', 'INNER'),
    ('INNER_WORKMANSHIP', 'Workmanship', '-', 'INNER'),
    ('INNER_CONTAMINATION', 'Contamination', '-', 'INNER'),
    ('INNER_OTHERS', 'OTHERS', '-', 'INNER')
     ON CONFLICT (code) DO NOTHING;