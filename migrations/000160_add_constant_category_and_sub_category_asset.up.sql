BEGIN;

INSERT INTO
    "ams_ASSET_CATEGORIES" (code, "label", description)
VALUES
    ('GENERAL_EARTHMOVING','Earthmoving Equipment', '-'),
    ('GENERAL_ROAD_CONSTRUCTION','Road Construction Equipment ', '-'),
    ('GENERAL_INDUSTRIAL','Industrial Equipment', '-'),
    ('GENERAL_LIFTING','Lifting Equipment', '-'),
    ('GENERAL_MATERIAL_HANDLING','Material Handling Equipment', '-'),
    ('MANUFACTURING','Manufacturing Equipment', '-')
    ON CONFLICT (code) DO NOTHING;

INSERT INTO "ams_ASSET_SUB_CATEGORIES" (code, "label", description)
VALUES
    ('EXCAVATOR', 'Excavator', 'Excavator'),
    ('BULLDOZER', 'Bulldozer', 'Bulldozer'),
    ('WHEEL_LOADER', 'Wheel Loader', 'Wheel Loader'),
    ('MOTOR_GRADER', 'Motor Grader', 'Motor Grader'),
    ('COMPACTOR', 'Compactor', 'Compactor'),
    ('PAVER', 'Paver', 'Paver'),
    ('ALPHALT_FINISHER', 'Alphalt Finisher', 'Alphalt Finisher'),
    ('GENERATOR', 'Generator', 'Generator'),
    ('COMPRESSOR', 'Compressor', 'Compressor'),
    ('CNC_MACHINE', 'CNC Machine', 'CNC Machine'),
    ('MILLING_MACHINE', 'Milling Machine', 'Milling Machine'),
    ('WELDING_MACHINE', 'Welding Machine', 'Welding Machine'),
    ('ROBOTIC_ARM', 'Robotic Arm', 'Robotic Arm'),
    ('CRAWLER_CRANE', 'Crawler Crane', 'Crawler Crane'),
    ('MOBILE_CRANE', 'Mobile Crane', 'Mobile Crane'),
    ('TRUCK_MOUNTED_CRANE', 'Truck Mounted Crane', 'Truck Mounted Crane'),
    ('SCISSOR_LIFT', 'Scissor Lift', 'Scissor Lift'),
    ('FORKLIFT', 'Forklift', 'Forklift'),
    ('TELEHANDLER', 'Telehandler', 'Telehandler'),
    ('CONVEYOR', 'Conveyor', 'Conveyor')
    ON CONFLICT (code) DO NOTHING;

INSERT INTO
    "ams_ASSET_CATEGORY_MAPPING" (category_code, sub_category_code)
VALUES
    ('GENERAL_EARTHMOVING', 'EXCAVATOR'),
    ('GENERAL_EARTHMOVING', 'BULLDOZER'),
    ('GENERAL_EARTHMOVING', 'WHEEL_LOADER'),
    ('GENERAL_ROAD_CONSTRUCTION', 'MOTOR_GRADER'),
    ('GENERAL_ROAD_CONSTRUCTION', 'COMPACTOR'),
    ('GENERAL_ROAD_CONSTRUCTION', 'PAVER'),
    ('GENERAL_ROAD_CONSTRUCTION', 'ALPHALT_FINISHER'),
    ('GENERAL_INDUSTRIAL', 'GENERATOR'),
    ('GENERAL_INDUSTRIAL', 'COMPRESSOR'),
    ('MANUFACTURING', 'CNC_MACHINE'),
    ('MANUFACTURING', 'MILLING_MACHINE'),
    ('MANUFACTURING', 'WELDING_MACHINE'),
    ('MANUFACTURING', 'ROBOTIC_ARM'),
    ('GENERAL_LIFTING', 'CRAWLER_CRANE'),
    ('GENERAL_LIFTING', 'MOBILE_CRANE'),
    ('GENERAL_LIFTING', 'TRUCK_MOUNTED_CRANE'),
    ('GENERAL_LIFTING', 'SCISSOR_LIFT'),
    ('GENERAL_MATERIAL_HANDLING', 'FORKLIFT'),
    ('GENERAL_MATERIAL_HANDLING', 'TELEHANDLER'),
    ('GENERAL_MATERIAL_HANDLING', 'CONVEYOR')
    ON CONFLICT (category_code, sub_category_code) DO NOTHING;

ALTER TABLE "ams_ASSET_VEHICLE_TYPES"
ALTER COLUMN "code" TYPE VARCHAR(255),
ALTER COLUMN "description" TYPE TEXT,
ALTER COLUMN "label" TYPE VARCHAR(255);

INSERT INTO
    "ams_ASSET_VEHICLE_TYPES" (code, "label", description)
VALUES
    ('EARTHMOVING', 'Earthmoving Equipment', '-'),
    (
        'ROAD_CONSTRUCTION',
        'Road Construction Equipment ',
        '-'
    ),
    ('INDUSTRIAL', 'Industrial Equipment', '-'),
    ('LIFTING', 'Lifting Equipment', '-'),
    (
        'MATERIAL_HANDLING',
        'Material Handling Equipment',
        '-'
    ) ON CONFLICT (code) DO NOTHING;

COMMIT;