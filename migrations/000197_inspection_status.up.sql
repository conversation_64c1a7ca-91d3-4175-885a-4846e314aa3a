BEGIN;


CREATE TABLE
    IF NOT EXISTS "ams_ASSET_INSPECTION_STATUSES" (
        "code" VARCHAR(255) NOT NULL,
        "label" VARCHAR(255) NOT NULL,
        "description" TEXT NOT NULL,
        PRIMARY KEY ("code")
    );

-- Populate "ams_ASSET_INSPECTION_STATUSES" table
INSERT INTO
    "ams_ASSET_INSPECTION_STATUSES" (code, "label", description)
VALUES
    ('OPEN', 'Open', 'Open'),
    ('DONE', 'Done', 'Done')
    ON CONFLICT (code) DO NOTHING;

ALTER TABLE "ams_asset_inspections"
ADD COLUMN IF NOT EXISTS "status_code" VARCHAR(255) REFERENCES "ams_ASSET_INSPECTION_STATUSES"(code) DEFAULT 'OPEN';

COMMIT;