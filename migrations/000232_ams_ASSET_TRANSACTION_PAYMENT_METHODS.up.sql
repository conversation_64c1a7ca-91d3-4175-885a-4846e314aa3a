CREATE TABLE 
    "ams_ASSET_TRANSACTION_PAYMENT_METHODS" (
        "code" varchar(20) NOT NULL PRIMARY KEY ,
        "label" VARCHAR(20) NOT NULL,
        "description" varchar(40)
    );

INSERT INTO "ams_ASSET_TRANSACTION_PAYMENT_METHODS" (code,label) 
VALUES 
    ('CASH', 'Cash'),
    ('BANK_TRANSFER', 'Bank Transfer'),
    ('QRIS', 'QRIS'),
    ('CARD', 'Debit/Credit'),
    ('EWALLET', 'E-Wallet'),
    ('EMONEY', 'E-Money');

-- add record Expense
INSERT INTO "ams_ASSET_TRANSACTION_TYPES" values ('EXPENSE','Expense','-');