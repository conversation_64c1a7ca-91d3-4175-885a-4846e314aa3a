CREATE TABLE IF NOT EXISTS "ams_asset_transaction_items" (
    id VARCHAR(40) PRIMARY KEY,
    asset_transaction_id VARCHAR(40) NOT NULL REFERENCES "ams_asset_transactions"(id),
    name VA<PERSON>HAR(255),
    unit_price BIGINT,
    quantity INT,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL
);

-- Add columns to asset_transactions table
ALTER TABLE "ams_asset_transactions"
ADD COLUMN tax_cost BIGINT,
ADD COLUMN discount_amount BIGINT,
ADD COLUMN other_cost BIGINT,
ADD COLUMN sub_total BIGINT;

ALTER TABLE "ams_asset_transactions" 
ALTER COLUMN "purchase_order_date" DROP NOT NULL,
ALTER COLUMN "purchase_order_number" DROP NOT NULL,
ALTER COLUMN "invoice_date" DROP NOT NULL,
ALTER COLUMN "invoice_number" DROP NOT NULL,
ALTER COLUMN "service_start_date" DROP NOT NULL,
ALTER COLUMN "service_end_date" DROP NOT NULL,
ALTER COLUMN "cost" DROP NOT NULL;