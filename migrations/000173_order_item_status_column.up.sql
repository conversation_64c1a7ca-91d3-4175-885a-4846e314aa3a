BEGIN;

CREATE TABLE
    IF NOT EXISTS "inv_ORDER_ITEM_STATUSES" (
        code VARCHAR(255) PRIMARY KEY,
        label VARCHAR(255) NOT NULL,
        description TEXT NOT NULL
    );

INSERT INTO
    "inv_ORDER_ITEM_STATUSES" (code, "label", description)
VALUES
    ('OPEN', 'Open', '-'),
    ('IN_PROGRESS', 'In Progress', '-'),
    ('PAUSE', 'Pause', '-'),
    ('DONE', 'Done', '-');

ALTER TABLE inv_order_items
ADD COLUMN IF NOT EXISTS status_code VARCHAR(255) REFERENCES "inv_ORDER_ITEM_STATUSES" (code),
ADD COLUMN IF NOT EXISTS assignee_user_id VARCHAR(40),
ADD COLUMN IF NOT EXISTS assignee_user_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS note TEXT;

COMMIT;