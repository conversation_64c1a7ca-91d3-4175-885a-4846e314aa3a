-- Reverse for ctn_form_fields
ALTER TABLE "ctn_form_fields"
    DROP CONSTRAINT IF EXISTS "fk_template_field_id";

ALTER TABLE "ctn_form_fields"
    ALTER COLUMN "template_field_id" SET NOT NULL;

ALTER TABLE "ctn_form_fields"
    DROP COLUMN IF EXISTS "template_field_id";

-- Reverse for ctn_forms
ALTER TABLE "ctn_forms"
    ALTER COLUMN "template_id" SET NOT NULL;

ALTER TABLE "ctn_forms"
    DROP CONSTRAINT IF EXISTS "fk_template_id";
