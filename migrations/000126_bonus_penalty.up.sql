begin;
 
CREATE TABLE
    IF NOT EXISTS ams_bonus_penalties (
        id VARCHAR(40) PRIMARY KEY,
        bap_number VARCHAR(20),
        asset_vehicle_id VARCHAR(40) REFERENCES "ams_asset_vehicles" (asset_id) NOT NULL,
        grand_total BIGINT NOT NULL,
        driver_user_id VARCHAR(40) NOT NULL,
        notes TEXT NOT NULL,
        status_code VARCHAR(20) REFERENCES "ams_BONUS_PENALTY_STATUSES" (code) NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );
 
CREATE INDEX "idx_ams_bonus_penalties_deleted_at" ON "ams_bonus_penalties" ("deleted_at"); 

CREATE TABLE
    IF NOT EXISTS ams_bonus_penalty_items (
        id VARCHAR(40) PRIMARY KEY,
        asset_tyre_id VARCHAR(40) REFERENCES "ams_asset_tyres" (asset_id) NOT NULL,
        bonus_penalty_id VARCHAR(40) REFERENCES "ams_bonus_penalties" (id) NOT NULL,
        target_formation_id VARCHAR(40) REFERENCES "ams_bonus_penalty_target_formations" (id) NOT NULL,
        replacement_reason VARCHAR(20) NOT NULL,
        start_km BIGINT NOT NULL,
        end_km BIGINT NOT NULL,
        target_km BIGINT NOT NULL,
        rate_per_km BIGINT NOT NULL,
        max_achievment_km BIGINT NOT NULL,
        achievment_km BIGINT NOT NULL,
        total BIGINT NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );
 
CREATE INDEX "idx_ams_bonus_penalty_items_deleted_at" ON "ams_bonus_penalty_items" ("deleted_at"); 

CREATE OR REPLACE FUNCTION trigger_set_new_bap_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    'BNP-' || 
    to_char(created_at, 'YYMMDD') || '-' ||
    lpad((row_number() over (partition BY NEW.client_id,  date_trunc('day',created_at) order by created_at))::text,4,'0') AS new_bap_number,
    bap_number
  FROM
    ams_bonus_penalties	 bp
  WHERE
   bp.deleted_at IS NULL 
   AND client_id = NEW.client_id
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE ams_bonus_penalties AS bp SET bap_number = cte.new_bap_number FROM cte WHERE bp.id = cte.id AND (cte.bap_number IS NULL OR cte.bap_number = '');
RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS set_number_after_insert_bap ON ams_bonus_penalties	;

CREATE TRIGGER set_number_after_insert_bap
  AFTER INSERT
  ON ams_bonus_penalties	
  FOR EACH ROW
  EXECUTE PROCEDURE trigger_set_new_bap_number();


commit;