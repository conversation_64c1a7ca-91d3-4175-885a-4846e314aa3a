BEGIN;

DROP TABLE IF EXISTS log_log_references;
DROP TABLE IF EXISTS log_logs;

DROP TRIGGER IF EXISTS create_update_trigger_tks_tickets ON tks_tickets;
DROP TRIGGER IF EXISTS create_update_trigger_ams_assets ON ams_assets;
DROP TRIGGER IF EXISTS create_update_trigger_ams_asset_vehicles ON ams_asset_vehicles;
DROP TRIGGER IF EXISTS create_update_trigger_ams_asset_tyres ON ams_asset_tyres;
DROP TRIGGER IF EXISTS create_update_ams_asset_components ON ams_asset_components;
-- DROP TRIGGER IF EXISTS create_update_ams_linked_assets ON ams_linked_assets;
-- DROP TRIGGER IF EXISTS create_update_ams_asset_handover_requests ON ams_asset_handover_requests;
-- DROP TRIGGER IF EXISTS create_update_ams_asset_assignments ON ams_asset_assignments;
-- DROP TRIGGER IF EXISTS create_update_ams_asset_inspection_tyre ON ams_asset_inspection_tyre;
DROP TRIGGER IF EXISTS create_update_ams_asset_transactions ON ams_asset_transactions;

COMMIT;