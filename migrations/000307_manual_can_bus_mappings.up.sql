BEGIN;



INSERT INTO
    "ins_ALERT_PARAMETER_TYPES" (code)
VALUES
    ('PARSED_MANUAL_CAN'),
    ('RAW_MANUAL_CAN')
     ON CONFLICT (code) DO NOTHING;


INSERT INTO
    "ins_ALERT_PARAMETERS" (code,label,description,unit,data_type,source_codes,type_code)
VALUES
('can_bus.engine_coolant_temperature', 'Engine Coolant Temperature', 'Engine Coolant Temperature', '°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_fuel_1_temperature_1', 'Engine Fuel 1 Temperature 1', 'Engine Fuel 1 Temperature 1', '°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_oil_temperature_1', 'Engine Oil Temperature 1', 'Engine Oil Temperature 1', '°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_turbocharger_1_oil_temperature', 'Engine Turbocharger 1 Oil Temperature', 'Engine Turbocharger 1 Oil Temperature', '°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_intercooler_temperature', 'Engine Intercooler Temperature', 'Engine Intercooler Temperature', '°C', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_charge_air_cooler_thermostat_opening', 'Engine Charge Air Cooler Thermostat Opening', 'Engine Charge Air Cooler Thermostat Opening', '%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.manual.18FEEE00', 'Manual Can Bus 18FEEE00', 'Manual Can Bus 18FEEE00', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.engine_fuel_delivery_pressure', 'Engine Fuel Delivery Pressure', 'Engine Fuel Delivery Pressure', 'Bar', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_extended_crankcase_blow_by_pressure', 'Engine Extended Crankcase Blow-by Pressure', 'Engine Extended Crankcase Blow-by Pressure', 'Bar', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_oil_level', 'Engine Oil Level', 'Engine Oil Level', '%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_oil_pressure_1', 'Engine Oil Pressure 1', 'Engine Oil Pressure 1', 'Bar', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_crankcase_pressure_1', 'Engine Crankcase Pressure 1', 'Engine Crankcase Pressure 1', 'Bar', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_coolant_pressure_1', 'Engine Coolant Pressure 1', 'Engine Coolant Pressure 1', 'Bar', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.engine_coolant_level_1', 'Engine Coolant Level 1', 'Engine Coolant Level 1', '%', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.manual.18FEEF00', 'Manual Can Bus 18FEEF00', 'Manual Can Bus 18FEEF00', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN'),
('can_bus.engine_total_hours_of_operation', 'Engine Total Hours of Operation', 'Engine Total Hours of Operation', 'Hours', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN') ,
('can_bus.engine_total_revolutions', 'Engine Total Revolutions', 'Engine Total Revolutions', 'Hours', 'NUMERIC', '{FLESPI_TELTONIKA}', 'PARSED_MANUAL_CAN'),
('can_bus.manual.18FEE500', 'Manual Can Bus 18FEE500', 'Manual Can Bus 18FEE500', '-', NULL, '{FLESPI_TELTONIKA}', 'RAW_MANUAL_CAN') ON CONFLICT (code) DO NOTHING;



CREATE TABLE IF NOT EXISTS "ins_MANUAL_CAN_BUS_MAPPINGS" (
    can_id_code VARCHAR(255),
    pgn_spn_code VARCHAR(255),
    label VARCHAR(255),
    parameter_code VARCHAR(255) REFERENCES "ins_ALERT_PARAMETERS" (code), 
    start_byte SMALLINT,
    len_byte SMALLINT,
    scale DOUBLE PRECISION,
    formula TEXT,
    description VARCHAR(255),
    PRIMARY KEY (can_id_code, pgn_spn_code)
);

INSERT INTO "ins_MANUAL_CAN_BUS_MAPPINGS" 
(can_id_code, pgn_spn_code, parameter_code, label, start_byte, len_byte, scale, formula)
VALUES
('18FEEE00', '65262.110', 'can_bus.engine_coolant_temperature','Engine Coolant Temperature',1,1,1, '(value*scale) - 40'),
('18FEEE00', '65262.174', 'can_bus.engine_fuel_1_temperature_1','Engine Fuel 1 Temperature 1',2,1,1, '(value*scale) - 40'),
('18FEEE00', '65262.175', 'can_bus.engine_oil_temperature_1','Engine Oil Temperature 1',3,2,0.03125, '(value*scale) - 273'),
('18FEEE00', '65262.176', 'can_bus.engine_turbocharger_1_oil_temperature','Engine Turbocharger 1 Oil Temperature',5,2,0.03125, '(value*scale) - 273'),
('18FEEE00', '65262.52',  'can_bus.engine_intercooler_temperature', 'Engine Intercooler Temperature',7,1,1, '(value*scale) - 40'),
('18FEEE00', '65262.1134','can_bus.engine_charge_air_cooler_thermostat_opening', 'Engine Charge Air Cooler Thermostat Opening',8,1,0.4, 'value*scale'),
('18FEEF00','65263.94',  'can_bus.engine_fuel_delivery_pressure', 'Engine Fuel Delivery Pressure',1,1,4,'(value*scale)/100'),
('18FEEF00','65263.22',  'can_bus.engine_extended_crankcase_blow_by_pressure', 'Engine Extended Crankcase Blow by Pressure',2,1,0.05,'(value*scale)/100'),
('18FEEF00','65263.98',  'can_bus.engine_oil_level', 'Engine Oil Level',3,1,0.4,'value*scale'),
('18FEEF00','65263.100', 'can_bus.engine_oil_pressure_1', 'Engine Oil Pressure 1',4,1,4,'(value*scale)/100'),
('18FEEF00','65263.101', 'can_bus.engine_crankcase_pressure_1', 'Engine Crankcase Pressure 1',5,2,0.0078125,'(value*scale - 250)/100'),
('18FEEF00','65263.109', 'can_bus.engine_coolant_pressure_1', 'Engine Coolant Pressure 1',7,1,2,'(value*scale)/100'),
('18FEEF00','65263.111', 'can_bus.engine_coolant_level_1', 'Engine Coolant Level 1',8,1,0.4,'value*scale'),
('18FEE500', '65253.247', 'can_bus.engine_total_hours_of_operation', 'Engine Total Hours of Operation', 1, 4, 0.05, 'value*scale'),
('18FEE500', '65253.249', 'can_bus.engine_total_revolutions', 'Engine Total Revolutions', 5, 4, 1000, 'value*scale') ON CONFLICT (can_id_code, pgn_spn_code) DO NOTHING;



COMMIT;