BEGIN;

ALTER TABLE "uis_CLIENT_PACKAGES" 
    ALTER COLUMN code TYPE VARCHAR(40),
    ALTER COLUMN label TYPE VARCHAR(40);

INSERT INTO
    "uis_CLIENT_PACKAGES" (code, "label", description)
VALUES
    ('CORE_MODULE', 'Core Module', 'Core Module'),
    ('GENERAL_CUSTOMER', 'General Customer', 'General Customer'),
    ('GENERAL_VEHICLE', 'General Vehicle', 'General Vehicle'),
    ('WORKSHOP_OPTIMAX_CUSTOMER', 'Workshop Optimax Customer', 'Workshop Optimax Customer'),
    ('WORKSHOP_OPTIMAX_VEHICLE', 'Workshop Optimax Vehicle', 'Workshop Optimax Vehicle'),
    ('GENERAL_TODO', 'General Todo', 'General Todo'),
    ('GENERAL_ASSETFINDR', 'General Assetfindr', 'General Assetfindr'),
    ('WORKSHOP_OPTIMAX', 'Workshop Optimax', 'Workshop Optimax'),
    ('TYRE_OPTIMAX', 'Tyre Optimax', 'Tyre Optimax'),
    ('FLEET_OPTIMAX', 'Fleet Optimax', 'Fleet Optimax'),
    ('MONITOR_OPTIMAX', 'Monitor Optimax', 'Monitor Optimax'),
    ('TRACK_OPTIMAX_AF', 'Track Optimax Af', 'Track Optimax Af'),
    ('WIALON_OPTIMAX', 'Wialon Optimax', 'Wialon Optimax'),
    ('DIGISPECT_PRO', 'Digispect Pro', 'Digispect Pro'),
    ('DIGISPECT_PRO_PLUS', 'Digispect Pro Plus', 'Digispect Pro Plus'),
    ('DIGISPECT_BASIC', 'Digispect Basic', 'Digispect Basic'),
    ('DIGISPECT_INSPECTION_FLOW_SCENARIO_1', 'Scenario 1', 'Digispect Inspection Flow Scenario 1'),
    ('DIGISPECT_INSPECTION_FLOW_SCENARIO_2', 'Scenario 2', 'Digispect Inspection Flow Scenario 2'),
    ('DIGISPECT_INSPECTION_FLOW_SCENARIO_3', 'Scenario 3', 'Digispect Inspection Flow Scenario 3'),
    ('DIGISPECT_INSPECTION_FLOW_SCENARIO_4', 'Scenario 4', 'Digispect Inspection Flow Scenario 4'),
    ('DIGISPECT_INSPECTION_FLOW_SCENARIO_5', 'Scenario 5', 'Digispect Inspection Flow Scenario 5'),
    ('DIGISPECT_INSPECTION_FLOW_SCENARIO_6', 'Scenario 6', 'Digispect Inspection Flow Scenario 6'),
    ('DIGISPECT_INSPECTION_FLOW_SCENARIO_7', 'Scenario 7', 'Digispect Inspection Flow Scenario 7') ON CONFLICT (code) DO NOTHING;

CREATE TABLE
    IF NOT EXISTS "uis_CLIENT_CONFIGS" (
        code VARCHAR(40) PRIMARY KEY,
        label VARCHAR(40) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "uis_CLIENT_CONFIGS" (code, "label", description)
VALUES
    ('HIDE_FLEET_OPTIMAX_REPORT', 'Hide Fleet Optimax Report', '-'),
    ('HIDE_TRACK_OPTIMAX_GEOFENCE', 'Hide Track Optimax Geofence', '-'),
    ('HIDE_TYRE_OPTIMAX_TYRE_REPORT', 'Hide Tyre Optimax Tyre Report', '-'),
    ('HIDE_TYRE_OPTIMAX_BONUS_PENALTY', 'Hide Tyre Optimax Bonus Penalty', '-'),
    ('HIDE_APPROVAL', 'Hide Approval', '-'),
    ('HIDE_REPORT', 'Hide Report', '-'),
    ('HIDE_SETTING_LOCATION', 'Hide Setting Location', '-'),
    ('HIDE_SETTING_VENDOR', 'Hide Setting Vendor', '-'),
    ('HIDE_LIBRARY_ASSET_CONFIG', 'Hide Library Asset Config', '-'),
    ('HIDE_LIBRARY_WO_CONFIG', 'Hide Library Wo Config', '-'),
    ('HIDE_LIBRARY_TEMPLATE_CONFIG', 'Hide Library Template Config', '-'),
    ('HIDE_LIBRARY_BULK_UPLOAD_CONFIG', 'Hide Library Bulk Upload Config', '-'),
    ('HIDE_FLEET_OPTIMAX_DASHBOARD', 'Hide fleet OptimaX dashboard', '-'),
    ('HIDE_FLEET_ASSET_LIST', 'hide fleetOptimaX asset list', '-'),
    ('HIDE_TRACK_OPTIMAX_AF', 'hide trackOptimaX tracking', '-'),
    ('HIDE_TYRE_OPTIMAX_TYRE_DASHBOARD', 'hide tyreOptimaX dashboard', '-'),
    ('HIDE_TYRE_OPTIMAX_TYRE_LIST', 'hide tyreOptimaX tyre List', '-'),
    ('HIDE_TYRE_OPTIMAX_LINKED_TYRE', 'hide tyreOptimaX linked tyre', '-'),
    ('HIDE_TYRE_OPTIMAX_TYRE_INSPECTION', 'hide tyreOptimaX tyre inspection', '-'),
    ('HIDE_WORKSHOPOPTIMAX_DASHBOARD', 'hide workshopOptimaX dashboard', '-'),
    ('HIDE_WORKSHOPOPTIMAX_CUSTOMER', 'hide workshopOptimaX customer', '-'),
    ('HIDE_WORKSHOPOPTIMAX_VEHICLE', 'hide workshopOptimaX vehicle', '-'),
    ('HIDE_WORKSHOPOPTIMAX_WORK_ORDER', 'hide workshopOptimaX work order', '-'),
    ('HIDE_WORKSHOPOPTIMAX_PACKAGE_MEMBERSHIP', 'hide workshopOptimaX package membership', '-'),
    ('HIDE_WORKSHOPOPTIMAX_INVOICE_PAYMENT', 'hide workshopOptimaX invoice payment', '-'),
    ('HIDE_WORKSHOPOPTIMAX_REPORT', 'hide workshopOptimaX report', '-'),
    ('HIDE_SETTING_COMPANY', 'hide setting company', '-'),
    ('HIDE_SETTING_USER_MANAGEMENT', 'hide setting user management', '-'),
    ('HIDE_LIBRARY_INTEGRATION', 'hide library integration', '-') ON CONFLICT (code) DO NOTHING;

    COMMIT;
