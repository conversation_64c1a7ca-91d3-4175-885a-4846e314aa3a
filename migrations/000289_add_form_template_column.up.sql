-- Form fields

ALTER TABLE "ctn_form_fields"
    ADD COLUMN IF NOT EXISTS "template_field_id" CHARACTER VARYING(40);

ALTER TABLE "ctn_form_fields" 
    ALTER COLUMN "template_field_id" DROP NOT NULL;

ALTER TABLE "ctn_form_fields"
    ADD CONSTRAINT "fk_template_field_id" FOREIGN KEY (template_field_id) REFERENCES "ctn_form_template_fields"(id);


-- Form
ALTER TABLE "ctn_forms" 
    ALTER COLUMN "template_id" DROP NOT NULL;

ALTER TABLE "ctn_forms"
    ADD CONSTRAINT "fk_template_id" FOREIGN KEY (template_id) REFERENCES "ctn_form_templates"(id);
