DROP TABLE IF EXISTS "ins_alerts";

-- Drop the table ins_alert_config_trigger_states
DROP TABLE IF EXISTS "ins_alert_config_trigger_states";

-- Drop the table ins_alert_config_triggers
DROP TABLE IF EXISTS "ins_alert_config_triggers";

-- Drop the table ins_alert_configs
DROP TABLE IF EXISTS "ins_alert_configs";

-- Drop the table ins_ALERT_ACTION_TYPES
DROP TABLE IF EXISTS "ins_ALERT_ACTION_TYPES";

-- Drop the table ins_ALERT_STATE_CATEGORIES
DROP TABLE IF EXISTS "ins_ALERT_STATE_CATEGORIES";

-- Drop the table ins_ALERT_TRIGGERS
DROP TABLE IF EXISTS "ins_ALERT_TRIGGERS";

-- Drop the table ins_ALERT_CONFIG_SUB_CATEGORIES
DROP TABLE IF EXISTS "ins_ALERT_CONFIG_SUB_CATEGORIES";

-- Drop the table ins_ALERT_CONFIG_CATEGORIES
DROP TABLE IF EXISTS "ins_ALERT_CONFIG_CATEGORIES";

-- Drop the table ins_ALERT_CONFIG_STATUSES
DROP TABLE IF EXISTS "ins_ALERT_CONFIG_STATUSES";
