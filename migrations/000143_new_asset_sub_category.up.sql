BEGIN;


ALTER TABLE "ams_ASSET_CATEGORIES" 
ALTER COLUMN code TYPE VARCHAR(255),
ALTER COLUMN "label" TYPE VARCHAR(255),
ALTER COLUMN description TYPE TEXT;

INSERT INTO
    "ams_ASSET_CATEGORIES" (code, "label", description)
VALUES
    ('GENERAL_VEHICLE','Vehicle', '-'),
    ('GENERAL_TYRE','Tyre ', '-'),
    ('GENERAL_ELECTRONIC','Electronic', '-'),
    ('GENERAL_FURNITURE','Furniture', '-'),
    ('GENERAL_COMPUTERAND_LAPTOP','Computer and Laptop', '-'),
    ('GENERAL_HANDPHONEAND_TABLET','Handphone and Tablet', '-')
    ON CONFLICT (code) DO NOTHING;

ALTER TABLE "ams_ASSET_SUB_CATEGORIES" 
ALTER COLUMN code TYPE VARCHAR(255),
ALTER COLUMN "label" TYPE VARCHAR(255),
ALTER COLUMN description TYPE TEXT;

INSERT INTO "ams_ASSET_SUB_CATEGORIES" (code, "label", description)
VALUES
    ('CARS', 'Cars', 'Cars'),
    ('TRUCKS', 'Trucks', 'Trucks'),
    ('EXCAVATORS', 'Excavators', 'Excavators'),
    ('LOADERS', 'Loaders', 'Loaders'),
    ('BULLDOZERS', 'Bulldozers', 'Bulldozers'),
    ('DRILLING_RIGS', 'Drilling Rigs', 'Drilling Rigs'),
    ('UTILITY_VEHICLES', 'Utility Vehicles', 'Utility Vehicles'),
    ('CRANES', 'Cranes', 'Cranes'),
    ('FORKLIFTS', 'Forklifts', 'Forklifts'),
    ('TRAILERS', 'Trailers', 'Trailers'),
    ('BUSES', 'Buses', 'Buses'),
    ('VANS', 'Vans', 'Vans'),
    ('AIRCRAFT', 'Aircraft', 'Aircraft'),
    ('BOATS', 'Boats', 'Boats'),
    ('CONSTRUCTION_EQUIPMENT', 'Construction Equipment', 'Construction Equipment'),
    ('UTILITY_TERRAIN_VEHICLES', 'Utility Terrain Vehicles (UTVs)', 'Utility Terrain Vehicles (UTVs)'),
    ('GOLF_CARTS', 'Golf Carts', 'Golf Carts'),
    ('SNOW_VEHICLES', 'Snow Vehicles', 'Snow Vehicles'),
    ('MOTORCYCLES', 'Motorcycles', 'Motorcycles'),
    ('TRACTORS', 'Tractors', 'Tractors'),
    ('ELECTRIC_VEHICLES', 'Electric Vehicles (EVs)', 'Electric Vehicles (EVs)'),
    ('NEW_TIRES', 'New Tyres', 'New Tyres'),
    ('USED_TIRES', 'Used/Second-hand Tyres', 'Used/Second-hand Tyres'),
    ('RETREADED_TIRES', 'Retreaded Tyres', 'Retreaded Tyres'),
    ('TYRE_ACCESSORIES', 'Tyre Accessories', 'Tyre Accessories'),
    ('TELEVISIONS', 'Televisions (TVs)', 'Televisions (TVs)'),
    ('DIGITAL_CAMERAS', 'Digital Cameras', 'Digital Cameras'),
    ('GAMING_CONSOLES', 'Gaming Consoles', 'Gaming Consoles'),
    ('SMART_WATCHES', 'Smart Watches', 'Smart Watches'),
    ('FITNESS_TRACKERS', 'Fitness Trackers', 'Fitness Trackers'),
    ('SMART_HOME_DEVICES', 'Smart Home Devices', 'Smart Home Devices (e.g., smart speakers, smart thermostats, smart lights)'),
    ('E_READERS', 'E-readers', 'E-readers'),
    ('DRONES', 'Drones (Unmanned Aerial Vehicles)', 'Drones (Unmanned Aerial Vehicles)'),
    ('SMART_APPLIANCES', 'Smart Appliances', 'Smart Appliances (e.g., smart refrigerators, smart washing machines, smart ovens)'),
    ('PORTABLE_MEDIA_PLAYERS', 'Portable Media Players', 'Portable Media Players (e.g., MP3 players, portable DVD players)'),
    ('VIRTUAL_REALITY_HEADSETS', 'Virtual Reality (VR) Headsets', 'Virtual Reality (VR) Headsets'),
    ('AUGMENTED_REALITY_GLASSES', 'Augmented Reality (AR) Glasses', 'Augmented Reality (AR) Glasses'),
    ('GPS_NAVIGATION_DEVICES', 'GPS Navigation Devices', 'GPS Navigation Devices'),
    ('PRINTERS', 'Printers', 'Printers'),
    ('SCANNERS', 'Scanners', 'Scanners'),
    ('DIGITAL_CLOCKS_WATCHES', 'Digital Clocks and Watches', 'Digital Clocks and Watches'),
    ('FAX_MACHINES', 'Fax Machines', 'Fax Machines'),
    ('PROJECTORS', 'Projector', 'Projector'),
    ('DESKS_TABLES', 'Desks and Tables', 'Desks and Tables'),
    ('SEATING', 'Seating', 'Seating'),
    ('STORAGE_ORGANIZATION', 'Storage and Organization', 'Storage and Organization'),
    ('PRESENTATION_COLLABORATION', 'Presentation and Collaboration', 'Presentation and Collaboration'),
    ('DECOR_AMBIANCE', 'Decor and Ambiance', 'Decor and Ambiance'),
    ('IDENTIFICATION_SIGNAGE', 'Identification and Signage', 'Identification and Signage'),
    ('LAPTOPS', 'Laptop', 'Laptop'),
    ('MONITORS', 'Monitors', 'Monitors'),
    ('KEYBOARDS', 'Keyboards', 'Keyboards'),
    ('MICE', 'Mice', 'Mice'),
    ('COMPUTER_ACCESSORIES', 'Accessories', 'Accessories (e.g., docking stations, cables, adapters)'),
    ('CPU_TOWERS', 'CPU Towers', 'CPU Towers (for desktop computers)'),
    ('UPS_UNITS', 'UPS (Uninterruptible Power Supply) Units', 'UPS (Uninterruptible Power Supply) Units'),
    ('EXTERNAL_HARD_DRIVES', 'External Hard Drives', 'External Hard Drives'),
    ('RAM_MODULES', 'RAM (Random Access Memory) Modules', 'RAM (Random Access Memory) Modules'),
    ('NETWORK_SWITCHES_ROUTERS', 'Network Switches/Routers', 'Network Switches/Routers'),
    ('HEADSETS_MICROPHONES', 'Headsets/Microphones', 'Headsets/Microphones'),
    ('WEBCAMS', 'Webcams', 'Webcams'),
    ('HANDPHONES', 'Handphone', 'Handphone'),
    ('TABLETS', 'Tablet', 'Tablet'),
    ('MOBILE_ACCESSORIES', 'Accessories', 'Accessories')
    ON CONFLICT (code) DO NOTHING;


ALTER TABLE "ams_ASSET_CATEGORY_MAPPING" 
ALTER COLUMN category_code TYPE VARCHAR(255),
ALTER COLUMN sub_category_code TYPE VARCHAR(255);
    
INSERT INTO
    "ams_ASSET_CATEGORY_MAPPING" (category_code, sub_category_code)
VALUES
    ('VEHICLE', 'CARS'),
    ('VEHICLE', 'TRUCKS'),
    ('VEHICLE', 'EXCAVATORS'),
    ('VEHICLE', 'LOADERS'),
    ('VEHICLE', 'BULLDOZERS'),
    ('VEHICLE', 'DRILLING_RIGS'),
    ('VEHICLE', 'UTILITY_VEHICLES'),
    ('VEHICLE', 'CRANES'),
    ('VEHICLE', 'FORKLIFTS'),
    ('VEHICLE', 'TRAILERS'),
    ('VEHICLE', 'BUSES'),
    ('VEHICLE', 'VANS'),
    ('VEHICLE', 'AIRCRAFT'),
    ('VEHICLE', 'BOATS'),
    ('VEHICLE', 'CONSTRUCTION_EQUIPMENT'),
    ('VEHICLE', 'UTILITY_TERRAIN_VEHICLES'),
    ('VEHICLE', 'GOLF_CARTS'),
    ('VEHICLE', 'SNOW_VEHICLES'),
    ('VEHICLE', 'MOTORCYCLES'),
    ('VEHICLE', 'TRACTORS'),
    ('VEHICLE', 'ELECTRIC_VEHICLES'),
    ('TYRE', 'NEW_TIRES'),
    ('TYRE', 'USED_TIRES'),
    ('TYRE', 'RETREADED_TIRES'),
    ('TYRE', 'TYRE_ACCESSORIES') ON CONFLICT (category_code, sub_category_code) DO NOTHING;

INSERT INTO
    "ams_ASSET_CATEGORY_MAPPING" (category_code, sub_category_code)
VALUES
    ('GENERAL_VEHICLE', 'CARS'),
    ('GENERAL_VEHICLE', 'TRUCKS'),
    ('GENERAL_VEHICLE', 'EXCAVATORS'),
    ('GENERAL_VEHICLE', 'LOADERS'),
    ('GENERAL_VEHICLE', 'BULLDOZERS'),
    ('GENERAL_VEHICLE', 'DRILLING_RIGS'),
    ('GENERAL_VEHICLE', 'UTILITY_VEHICLES'),
    ('GENERAL_VEHICLE', 'CRANES'),
    ('GENERAL_VEHICLE', 'FORKLIFTS'),
    ('GENERAL_VEHICLE', 'TRAILERS'),
    ('GENERAL_VEHICLE', 'BUSES'),
    ('GENERAL_VEHICLE', 'VANS'),
    ('GENERAL_VEHICLE', 'AIRCRAFT'),
    ('GENERAL_VEHICLE', 'BOATS'),
    ('GENERAL_VEHICLE', 'CONSTRUCTION_EQUIPMENT'),
    ('GENERAL_VEHICLE', 'UTILITY_TERRAIN_VEHICLES'),
    ('GENERAL_VEHICLE', 'GOLF_CARTS'),
    ('GENERAL_VEHICLE', 'SNOW_VEHICLES'),
    ('GENERAL_VEHICLE', 'MOTORCYCLES'),
    ('GENERAL_VEHICLE', 'TRACTORS'),
    ('GENERAL_VEHICLE', 'ELECTRIC_VEHICLES'),
    ('GENERAL_TYRE', 'NEW_TIRES'),
    ('GENERAL_TYRE', 'USED_TIRES'),
    ('GENERAL_TYRE', 'RETREADED_TIRES'),
    ('GENERAL_TYRE', 'TYRE_ACCESSORIES'),
    ('GENERAL_ELECTRONIC', 'TELEVISIONS'),
    ('GENERAL_ELECTRONIC', 'DIGITAL_CAMERAS'),
    ('GENERAL_ELECTRONIC', 'GAMING_CONSOLES'),
    ('GENERAL_ELECTRONIC', 'SMART_WATCHES'),
    ('GENERAL_ELECTRONIC', 'FITNESS_TRACKERS'),
    ('GENERAL_ELECTRONIC', 'SMART_HOME_DEVICES'),
    ('GENERAL_ELECTRONIC', 'E_READERS'),
    ('GENERAL_ELECTRONIC', 'DRONES'),
    ('GENERAL_ELECTRONIC', 'SMART_APPLIANCES'),
    ('GENERAL_ELECTRONIC', 'PORTABLE_MEDIA_PLAYERS'),
    ('GENERAL_ELECTRONIC', 'VIRTUAL_REALITY_HEADSETS'),
    ('GENERAL_ELECTRONIC', 'AUGMENTED_REALITY_GLASSES'),
    ('GENERAL_ELECTRONIC', 'GPS_NAVIGATION_DEVICES'),
    ('GENERAL_ELECTRONIC', 'PRINTERS'),
    ('GENERAL_ELECTRONIC', 'SCANNERS'),
    ('GENERAL_ELECTRONIC', 'DIGITAL_CLOCKS_WATCHES'),
    ('GENERAL_ELECTRONIC', 'FAX_MACHINES'),
    ('GENERAL_ELECTRONIC', 'PROJECTORS'),
    ('GENERAL_FURNITURE', 'DESKS_TABLES'),
    ('GENERAL_FURNITURE', 'SEATING'),
    ('GENERAL_FURNITURE', 'STORAGE_ORGANIZATION'),
    ('GENERAL_FURNITURE', 'PRESENTATION_COLLABORATION'),
    ('GENERAL_FURNITURE', 'DECOR_AMBIANCE'),
    ('GENERAL_FURNITURE', 'IDENTIFICATION_SIGNAGE'),
    ('GENERAL_COMPUTERAND_LAPTOP', 'LAPTOPS'),
    ('GENERAL_COMPUTERAND_LAPTOP', 'MONITORS'),
    ('GENERAL_COMPUTERAND_LAPTOP', 'KEYBOARDS'),
    ('GENERAL_COMPUTERAND_LAPTOP', 'MICE'),
    ('GENERAL_COMPUTERAND_LAPTOP', 'COMPUTER_ACCESSORIES'),
    ('GENERAL_COMPUTERAND_LAPTOP', 'CPU_TOWERS'),
    ('GENERAL_COMPUTERAND_LAPTOP', 'UPS_UNITS'),
    ('GENERAL_COMPUTERAND_LAPTOP', 'EXTERNAL_HARD_DRIVES'),
    ('GENERAL_COMPUTERAND_LAPTOP', 'RAM_MODULES'),
    ('GENERAL_COMPUTERAND_LAPTOP', 'NETWORK_SWITCHES_ROUTERS'),
    ('GENERAL_COMPUTERAND_LAPTOP', 'HEADSETS_MICROPHONES'),
    ('GENERAL_COMPUTERAND_LAPTOP', 'WEBCAMS'),
    ('GENERAL_HANDPHONEAND_TABLET', 'HANDPHONES'),
    ('GENERAL_HANDPHONEAND_TABLET', 'TABLETS'),
    ('GENERAL_HANDPHONEAND_TABLET', 'MOBILE_ACCESSORIES')
     ON CONFLICT (category_code, sub_category_code) DO NOTHING;

COMMIT;