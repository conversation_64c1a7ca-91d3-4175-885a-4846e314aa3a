BEGIN;

CREATE TABLE IF NOT EXISTS
    "ins_INTEGRATION_MACHINE_STATUSES" (
        "code" character varying(20) NOT NULL,
        "label" character varying(255) NOT NULL,
        "description" character varying(255) NOT NULL,
        PRIMARY KEY ("code")
    );

INSERT INTO
    "ins_INTEGRATION_MACHINE_STATUSES" (code, "label", description)
VALUES
    ('LOCK', 'Lock', 'Lock'),
    ('UNLOCK', 'Unlock', 'Unlock')
    ON CONFLICT (code) DO NOTHING;


ALTER TABLE ins_integrations
ADD COLUMN IF NOT EXISTS machine_status_code VARCHAR(20) REFERENCES "ins_INTEGRATION_MACHINE_STATUSES" (code),
ADD COLUMN IF NOT EXISTS machine_status_last_updated_at TIMESTAMPTZ;
ADD COLUMN IF NOT EXISTS is_enable_command boolean NOT NULL DEFAULT false;

UPDATE ins_integrations SET machine_status_code = 'UNLOCK';

UPDATE ins_integration_data_mapping_templates
SET data_mapping='{"time": "timestamp", "ident": "ident", "gps.position_hdop": "position.hdop", "gps.position_pdop": "position.pdop", "gps.position_speed": "position.speed", "gps.position_valid": "position.valid", "gps.vehicle_mileage": "vehicle.mileage", "general.digital_input": "din.1", "gps.position_altitude": "position.altitude", "gps.position_latitude": "position.latitude", "can_bus.can_cng_status": "can.cng.status", "can_bus.can_engine_rpm": "can.engine.rpm", "can_bus.can_esp_status": "can.esp.status", "can_bus.can_fuel_level": "can.fuel.level", "can_bus.can_program_id": "can.program.id", "can_bus.can_pto_status": "can.pto.status", "general.digital_output": "dout.1", "gps.position_direction": "position.direction", "gps.position_longitude": "position.longitude", "can_bus.can_hood_status": "can.hood.status", "general.battery_current": "battery.current", "general.battery_voltage": "battery.voltage", "general.movement_status": "movement.status", "gps.position_satellites": "position.satellites", "can_bus.can_trunk_status": "can.trunk.status", "general.gsm_signal_level": "gsm.signal.level", "can_bus.can_cruise_status": "can.cruise.status", "can_bus.can_fuel_consumed": "can.fuel.consumed", "can_bus.can_vehicle_speed": "can.vehicle.speed", "can_bus.can_parking_status": "can.parking.status", "can_bus.can_private_status": "can.private.status", "can_bus.can_webasto_status": "can.webasto.status", "can_bus.can_low_beam_status": "can.low.beam.status", "can_bus.can_vehicle_mileage": "can.vehicle.mileage", "can_bus.can_engine_oil_level": "can.engine.oil.level", "can_bus.can_handbrake_status": "can.handbrake.status", "can_bus.can_high_beam_status": "can.high.beam.status", "can_bus.can_interlock_active": "can.interlock.active", "can_bus.can_car_closed_status": "can.car.closed.status", "can_bus.can_drive_gear_status": "can.drive.gear.status", "can_bus.can_engine_load_level": "can.engine.load.level", "can_bus.can_engine_motorhours": "can.engine.motorhours", "can_bus.can_module_sleep_mode": "can.module.sleep.mode", "can_bus.can_standalone_engine": "can.standalone.engine", "can_bus.can_connection_state_1": "can.connection.state.1", "can_bus.can_connection_state_2": "can.connection.state.2", "can_bus.can_connection_state_3": "can.connection.state.3", "can_bus.can_engine_lock_status": "can.engine.lock.status", "can_bus.can_engine_temperature": "can.engine.temperature", "can_bus.can_pedal_brake_status": "can.pedal.brake.status", "can_bus.can_roof_opened_status": "can.roof.opened.status", "can_bus.can_ignition_key_status": "can.ignition.key.status", "can_bus.can_light_signal_status": "can.light.signal.status", "can_bus.can_neutral_gear_status": "can.neutral.gear.status", "can_bus.can_pedal_clutch_status": "can.pedal.clutch.status", "can_bus.can_reverse_gear_status": "can.reverse.gear.status", "can_bus.can_air_condition_status": "can.air.condition.status", "can_bus.can_eps_indicator_status": "can.eps.indicator.status", "can_bus.can_esp_indicator_status": "can.esp.indicator.status", "can_bus.can_factory_armed_status": "can.factory.armed.status", "can_bus.can_throttle_pedal_level": "can.throttle.pedal.level", "can_bus.can_engine_working_status": "can.engine.working.status", "can_bus.can_parking_lights_status": "can.parking.lights.status", "can_bus.can_rear_left_door_status": "can.rear.left.door.status", "can_bus.can_stop_indicator_status": "can.stop.indicator.status", "can_bus.can_driver_seatbelt_status": "can.driver.seatbelt.status", "can_bus.can_electric_engine_status": "can.electric.engine.status", "can_bus.can_engine_ignition_status": "can.engine.ignition.status", "can_bus.can_front_left_door_status": "can.front.left.door.status", "can_bus.can_front_passenger_status": "can.front.passenger.status", "can_bus.can_manual_retarder_status": "can.manual.retarder.status", "can_bus.can_rear_fog_lights_status": "can.rear.fog.lights.status", "can_bus.can_rear_right_door_status": "can.rear.right.door.status", "can_bus.can_trip_engine_motorhours": "can.trip.engine.motorhours", "can_bus.can_airbag_indicator_status": "can.airbag.indicator.status", "can_bus.can_dynamic_ignition_status": "can.dynamic.ignition.status", "can_bus.can_front_fog_lights_status": "can.front.fog.lights.status", "can_bus.can_front_right_door_status": "can.front.right.door.status", "can_bus.can_operator_present_status": "can.operator.present.status", "can_bus.can_tracker_counted_mileage": "can.tracker.counted.mileage", "can_bus.can_battery_indicator_status": "can.battery.indicator.status", "can_bus.can_car_closed_remote_status": "can.car.closed.remote.status", "can_bus.can_rear_differential_status": "can.rear.differential.status", "can_bus.can_tire_pressure_low_status": "can.tire.pressure.low.status", "can_bus.can_warning_indicator_status": "can.warning.indicator.status", "general.external_powersource_voltage": "external.powersource.voltage", "can_bus.can_automatic_retarder_status": "can.automatic.retarder.status", "can_bus.can_front_differential_status": "can.front.differential.status", "can_bus.can_glow_plug_indicator_status": "can.glow.plug.indicator.status", "can_bus.can_handbrake_indicator_status": "can.handbrake.indicator.status", "can_bus.can_trailer_axle_lift_status_1": "can.trailer.axle.lift.status.1", "can_bus.can_trailer_axle_lift_status_2": "can.trailer.axle.lift.status.2", "can_bus.can_lights_hazard_lights_status": "can.lights.hazard.lights.status", "can_bus.can_maintenance_required_status": "can.maintenance.required.status", "can_bus.can_abs_failure_indicator_status": "can.abs.failure.indicator.status", "can_bus.can_soot_filter_indicator_status": "can.soot.filter.indicator.status", "can_bus.can_additional_rear_lights_status": "can.additional.rear.lights.status", "can_bus.can_check_engine_indicator_status": "can.check.engine.indicator.status", "can_bus.can_oil_pressure_indicator_status": "can.oil.pressure.indicator.status", "can_bus.can_tracker_counted_fuel_consumed": "can.tracker.counted.fuel.consumed", "can_bus.can_additional_front_lights_status": "can.additional.front.lights.status", "can_bus.can_electronic_power_control_status": "can.electronic.power.control.status", "can_bus.can_front_passenger_seatbelt_status": "can.front.passenger.seatbelt.status", "can_bus.can_fuel_level_low_indicator_status": "can.fuel.level.low.indicator.status", "can_bus.can_lights_failure_indicator_status": "can.lights.failure.indicator.status", "can_bus.can_ready_to_drive_indicator_status": "can.ready.to.drive.indicator.status", "can_bus.can_vehicle_battery_charging_status": "can.vehicle.battery.charging.status", "can_bus.can_central_differential_4_hi_status": "can.central.differential.4hi.status", "can_bus.can_central_differential_4_lo_status": "can.central.differential.4lo.status", "can_bus.can_driver_seatbelt_indicator_status": "can.driver.seatbelt.indicator.status", "can_bus.can_wear_brake_pads_indicator_status": "can.wear.brake.pads.indicator.status", "can_bus.can_coolant_level_low_indicator_status": "can.coolant.level.low.indicator.status", "can_bus.can_passenger_seatbelt_indicator_status": "can.passenger.seatbelt.indicator.status", "can_bus.can_rear_left_passenger_seatbelt_status": "can.rear.left.passenger.seatbelt.status", "can_bus.can_rear_right_passenger_seatbelt_status": "can.rear.right.passenger.seatbelt.status", "can_bus.can_rear_central_passenger_seatbelt_status": "can.rear.central.passenger.seatbelt.status"}'::jsonb, created_at='2024-08-29 09:39:25.828', updated_at='2024-08-29 09:39:25.828', deleted_at=NULL, updated_by=NULL, created_by=NULL
WHERE code='FLESPI_TELTONIKA';

INSERT INTO "uis_PERMISSIONS" (code, user_permission_category_code, label, description, display_sequence) VALUES
('TRIGGER_COMMAND_ASSET', 'ASSET_MANAGEMENT', 'Trigger command asset', 'User can trigger command asset', 18) ON CONFLICT (code) DO NOTHING;


UPDATE ins_integrations SET machine_status_code = 'UNLOCK';

COMMIT;