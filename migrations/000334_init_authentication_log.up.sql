CREATE TABLE
    IF NOT EXISTS uis_authentication_log (
        id VARCHAR(40) PRIMARY KEY,
        user_id VARCHAR(40) NOT NULL,
        device_type_ref VARCHAR(20) NULL DEFAULT NULL,
        log_type VARCHAR(20) NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
        deleted_at TIMESTAMPTZ,
        CONSTRAINT fk_device_type_ref FOREIGN KEY(device_type_ref) REFERENCES "uis_DEVICE_TYPES"(code)
    );

INSERT INTO "uis_DEVICE_TYPES" VALUES
    ('DIGISPECT_MOBILE','Digispect Mobile','Digispect Mobile'),
    ('AF_APP_MOBILE','AF App Mobile','AF App Mobile'),
    ('WEB_DESKTOP','Web Desktop','Web Desktop'),
    ('WEB_MOBILE','Web Mobile','Web Mobile') ON CONFLICT (code) DO NOTHING;
    