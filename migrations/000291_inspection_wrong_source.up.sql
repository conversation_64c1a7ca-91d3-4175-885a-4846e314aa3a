CREATE TABLE IF NOT EXISTS "ams_ASSET_INSPECTION_SOURCE_TYPES" (
    code VARCHAR(255) PRIMARY KEY,
    label VARCHAR(255) NOT NULL,
    description TEXT NOT NULL
);

INSERT INTO "ams_ASSET_INSPECTION_SOURCE_TYPES" VALUES 
('TRANSLOGIC_APP','Translogic App','-'),
('ASSETFINDR_APP','AssetFindr App','-'),
('ASSETFINDR_WEB','AssetFindr Web','-');

ALTER TABLE "ams_asset_inspection_vehicle"
    ADD COLUMN IF NOT EXISTS "device_id" CHARACTER VARYING(40),
    ADD COLUMN IF NOT EXISTS "source_type_code" CHARACTER VARYING(40),
    ADD CONSTRAINT fk_source_type_code foreign key (source_type_code) references "ams_ASSET_INSPECTION_SOURCE_TYPES"(code);