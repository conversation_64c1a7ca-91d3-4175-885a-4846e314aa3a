BEGIN;
CREATE TABLE
    IF NOT EXISTS "uis_PARTNER_TYPES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "uis_PARTNER_TYPES" (code, "label", description)
VALUES
    ('CUSTOMER', 'Customer', '-'),
    ('VENDOR', 'Vendor', '-'),
    ('CUSTOMER_VENDOR', 'Customer vendor', '-') ON CONFLICT (code) DO NOTHING;

ALTER TABLE uis_partners 
ADD COLUMN IF NOT EXISTS tax_identity varchar(255),
ADD COLUMN IF NOT EXISTS partner_type_code varchar(255) REFERENCES "uis_PARTNER_TYPES"(code) DEFAULT 'VENDOR';

COMMIT;