BEGIN;

DROP TRIGGER IF EXISTS create_update_trigger_ams_asset_tyres_treads ON ams_asset_tyres_treads;

CREATE TRIGGER create_update_trigger_ams_asset_tyres_treads
AFTER INSERT OR UPDATE ON ams_asset_tyres_treads
FOR EACH ROW
EXECUTE FUNCTION log_trigger_function();

CREATE OR REPLACE FUNCTION log_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
  _new jsonb;
  _old jsonb;
  _k text;
  _id varchar(40);
BEGIN
  _id := concat('log_', uuid_generate_v4());
  _new := to_jsonb(NEW);
  _old := to_jsonb(OLD);

  FOR _k IN SELECT * FROM jsonb_object_keys(_new)
  LOOP
    IF _new[_k] = _old[_k]
      THEN _new = _new - _k; _old = _old - _k;
    END IF;
  END LOOP;

IF _new IS NULL THEN
  _new := '{}'::jsonb;
<PERSON>ND IF;

  INSERT INTO log_logs(
  id, table_name, client_id, created_by, 
  created_at, previous_value, new_value) 
VALUES 
  (
    _id, 
    TG_TABLE_NAME, 
    NEW.client_id, 
    NEW.updated_by, 
    NEW.updated_at, 
    _old, 
    _new
  );

  
  IF TG_TABLE_NAME = 'ams_assets' 
    THEN  INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.id);
  ELSIF TG_TABLE_NAME = 'ams_asset_vehicles' 
    THEN  INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.asset_id);

    INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET_VEHICLE', NEW.asset_id);
  ELSIF TG_TABLE_NAME = 'ams_asset_tyres' 
    THEN  INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.asset_id);

    INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET_TYRE', NEW.asset_id);
  ELSIF TG_TABLE_NAME = 'ams_asset_components' 
    THEN  INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.asset_id);

    INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET_COMPONENT', NEW.id);
  ELSIF TG_TABLE_NAME = 'ams_asset_transactions' 
    THEN  INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.asset_id);

    INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET_TRANSACTION', NEW.id);
  ELSIF TG_TABLE_NAME = 'ams_asset_tyres_treads' 
    THEN  INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.asset_id);

    INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET_TYRES_TREAD', NEW.id);
  END IF;

  -- ADD FOR ANOTHER TABLE

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

INSERT INTO
    "log_LOG_CATEGORIES" (code, "label", description)
VALUES
    ('RETREAD', 'Retread', '-')
ON CONFLICT (code) DO NOTHING;

COMMIT;