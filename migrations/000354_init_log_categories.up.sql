BEGIN;

CREATE TABLE
    IF NOT EXISTS "log_LOG_CATEGORIES" (
        code VARCHAR(40) PRIMARY KEY,
        label VARCHAR(40) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "log_LOG_CATEGORIES" (code, "label", description)
VALUES
    ('ASSET_DETAIL', 'Asset Detail', '-'),
    ('PURCHASE_INFORMATION', 'Purchase Information', '-'),
    ('RENT_INFORMATION', 'Rent Information', '-'),
    ('WARRANTY_INFORMATION', 'Warranty Information', '-'),
    ('INSURANCE_INFORMATION', 'Insurance Information', '-'),
    ('EXPENSE_INFORMATION', 'Expense Information', '-'),
    ('ASSET_COMPONENTS', 'Asset Components', '-')
ON CONFLICT (code) DO NOTHING;

ALTER TABLE log_logs
    ADD COLUMN IF NOT EXISTS category_code VARCHAR(40) NULL;

COMMIT;
