BEGIN;

DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'ins_monitoring_display_configs'
          AND column_name = 'hidden_fields'
    ) THEN
        EXECUTE 'ALTER TABLE ins_monitoring_display_configs RENAME COLUMN hidden_fields TO data_mapping_keys';
    END IF;
END$$;

ALTER TABLE ins_monitoring_display_configs DROP COLUMN IF EXISTS asset_id;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.table_constraints
        WHERE constraint_type = 'UNIQUE'
          AND table_name = 'ins_monitoring_display_configs'
          AND constraint_name = 'unique_client_id'
    ) THEN
        ALTER TABLE ins_monitoring_display_configs
        ADD CONSTRAINT unique_client_id UNIQUE (client_id);
    END IF;
END
$$;

COMMIT;
