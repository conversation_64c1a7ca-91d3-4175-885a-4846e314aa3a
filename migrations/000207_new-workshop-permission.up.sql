BEGIN;

ALTER TABLE "uis_PERMISSION_CATEGORIES"
ALTER COLUMN "code" TYPE VARCHAR(255),
ALTER COLUMN "title" TYPE VARCHAR(255),
ALTER COLUMN "icon" TYPE VARCHAR(255),
ALTER COLUMN "description" TYPE TEXT,
ALTER COLUMN "label" TYPE VARCHAR(255);

INSERT INTO "uis_PERMISSION_CATEGORIES" 
(code, title, subtitle, icon, label, description, display_sequence) VALUES
('WORKSHOP_CUSTOMER', 'Customer Workshop', '', 'assets.svg', 'Customer Workshop', '', 11),
('WORKSHOP_VEHICLE', 'Vehicle Workshop', '', 'assets.svg', 'Vehicle Workshop', '', 12),
('WORKSHOP_MEMBERSHIP', 'Membership Workshop', '', '', 'Membership Workshop', '', 13),
('WORKSHOP_PART_SERVICE', 'Part & Service Workshop', '', 'assets.svg', 'Part & Service Workshop', '', 14),
('WORKSHOP_INVOICE_PAYMENT', 'Invoice & Payment Workshop', '', 'assets.svg', 'Invoice & Payment Workshop', '', 15),
('WORKSHOP_WORK_ORDER', 'Work Order Workshop', '', 'work-order.svg', 'Work Order Workshop', '', 16),
('WORKSHOP_WORK_ORDER_TODO', 'Work Order Workshop Todo', '', 'work-order.svg', 'Work Order Workshop Todo', '', 17) ON CONFLICT (code) DO NOTHING;

INSERT INTO "uis_PERMISSIONS" (code, user_permission_category_code, label, description, display_sequence) VALUES
('ADD_WORKSHOP_CUSTOMER', 'WORKSHOP_CUSTOMER', 'Add Workshop Customer', '', 1),
('EDIT_WORKSHOP_CUSTOMER', 'WORKSHOP_CUSTOMER', 'Edit Workshop Customer', '', 2),
('ADD_WORKSHOP_VEHICLES', 'WORKSHOP_VEHICLE', 'Add Workshop Vehicles', '', 1),
('EDIT_WORKSHOP_VEHICLES', 'WORKSHOP_VEHICLE', 'Edit Workshop Vehicles', '', 2),
('ADD_WORKSHOP_MEMBERSHIP', 'WORKSHOP_MEMBERSHIP', 'Add Workshop Membership', '', 1),
('ADD_WORKSHOP_PACKAGE', 'WORKSHOP_MEMBERSHIP', 'Add Workshop Package', '', 2),
('EDIT_WORKSHOP_PACKAGE', 'WORKSHOP_MEMBERSHIP', 'Edit Workshop Package', '', 3),
('CREATE_WORKSHOP_TICKET', 'WORKSHOP_WORK_ORDER', 'Create Workshop Ticket', '', 1),
('EDIT_WORKSHOP_TICKET', 'WORKSHOP_WORK_ORDER', 'Edit Workshop Ticket', '', 2),
('CLOSE_WORKSHOP_TICKET', 'WORKSHOP_WORK_ORDER', 'Close Workshop Ticket', '', 3),
('WORK_ORDER_WORKSHOP_ADMIN', 'WORKSHOP_WORK_ORDER', 'Work Order Workshop Admin', '', 4)  ON CONFLICT (code) DO NOTHING;

COMMIT;
