BEGIN;

ALTER TABLE "tks_TICKET_CATEGORIES" 
ADD COLUMN IF NOT EXISTS is_hidden BOOLEAN default FALSE,
ALTER COLUMN code TYPE VARCHAR(40),
ALTER COLUMN label TYPE VARCHAR(30),
ALTER COLUMN description TYPE VARCHAR(255),
ADD COLUMN IF NOT EXISTS client_id character varying(40) not null default 'GENERAL',
ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL default now(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL default now(),
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(40) NOT NULL default 'ADMIN',
ADD COLUMN IF NOT EXISTS created_by VARCHAR(40) NOT NULL default 'ADMIN';


ALTER TABLE tks_tickets  
ADD COLUMN IF NOT EXISTS schedule_datetime_end TIMESTAMPTZ,
ALTER COLUMN ticket_category_code TYPE VARCHAR(40),
ADD COLUMN IF NOT EXISTS location_id VARCHAR(40);

UPDATE tks_tickets SET schedule_datetime_end = schedule_datetime + INTERVAL '1 hour' WHERE schedule_datetime IS NOT NULL;

UPDATE "tks_TICKET_CATEGORIES" SET is_hidden = TRUE WHERE code IN (
    'WORKSHOP_SERVICE',
    'ASSET_ALERT',
    'TYRE_ALERT',
    'ASSET_WARRANTY'
);

COMMIT;