BEGIN;

ALTER TABLE ams_asset_tyres
ADD COLUMN IF NOT EXISTS prev_km_hm_data_unavailable BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS prev_total_km BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS prev_total_hm INT DEFAULT 0;

CREATE OR REPLACE FUNCTION update_tyre_km_hm_after_update_prev_km_hm_data() 
RETURNS TRIGGER AS $$
BEGIN
    -- Handle UPDATE operations
    IF TG_OP = 'UPDATE' THEN
        -- Handle KM updates
        IF OLD.prev_km_hm_data_unavailable = FALSE AND OLD.prev_total_km != NEW.prev_total_km THEN
            -- Update asset tyres
            UPDATE ams_asset_tyres
            SET total_km = total_km + (NEW.prev_total_km - OLD.prev_total_km)
            WHERE asset_id = NEW.asset_id;

            -- Update stats history
            UPDATE ams_asset_tyre_stats_histories
            SET total_km = total_km + (NEW.prev_total_km - OLD.prev_total_km)
            WHERE asset_id = NEW.asset_id;
        END IF;

        -- <PERSON><PERSON> HM updates
        IF OLD.prev_km_hm_data_unavailable = FALSE AND OLD.prev_total_hm != NEW.prev_total_hm THEN
            -- Update asset tyres
            UPDATE ams_asset_tyres
            SET total_hm = total_hm + (NEW.prev_total_hm - OLD.prev_total_hm)
            WHERE asset_id = NEW.asset_id;

            -- Update stats history
            UPDATE ams_asset_tyre_stats_histories
            SET total_hm = total_hm + (NEW.prev_total_hm - OLD.prev_total_hm)
            WHERE asset_id = NEW.asset_id;
        END IF;
    END IF;

    -- Handle INSERT operations
    IF TG_OP = 'INSERT' AND NEW.prev_km_hm_data_unavailable = FALSE THEN
        -- Update asset tyres
        UPDATE ams_asset_tyres
        SET total_km = total_km + NEW.prev_total_km,
            total_hm = total_hm + NEW.prev_total_hm
        WHERE asset_id = NEW.asset_id;

        -- Update stats history
        UPDATE ams_asset_tyre_stats_histories
        SET total_km = total_km + NEW.prev_total_km,
            total_hm = total_hm + NEW.prev_total_hm
        WHERE asset_id = NEW.asset_id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_tyre_km_hm_after_update_prev_km_hm_data ON ams_asset_tyres;

CREATE TRIGGER trigger_update_tyre_km_hm_after_update_prev_km_hm_data
AFTER INSERT OR UPDATE ON ams_asset_tyres
FOR EACH ROW
EXECUTE FUNCTION update_tyre_km_hm_after_update_prev_km_hm_data();


CREATE OR REPLACE FUNCTION update_tread_after_asset_tyre_update()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
    WITH
        cte AS (
            SELECT
                aatt.id, aa.updated_by
            FROM
                ams_asset_tyres_treads aatt JOIN ams_assets aa ON aa.id = aatt.asset_id
            WHERE
                asset_id = NEW.asset_id
            ORDER BY
                thread_sequence DESC
            LIMIT
                1
        )
    UPDATE ams_asset_tyres_treads
    SET
        average_rtd = NEW.average_rtd,
        total_km = NEW.total_km,
        total_hm = NEW.total_hm,
        total_lifetime = NEW.total_lifetime,
        start_thread_depth= NEW.start_thread_depth,
        updated_by = cte.updated_by,
        updated_at = NEW.updated_at
        FROM cte
    WHERE
        ams_asset_tyres_treads.id = cte.id;

    -- Handle KM updates
    IF OLD.prev_km_hm_data_unavailable = FALSE AND OLD.prev_total_km != NEW.prev_total_km THEN

        -- Update latest tread
        UPDATE ams_asset_tyres_treads
        SET total_km = total_km + (NEW.prev_total_km - OLD.prev_total_km),
            updated_at = NEW.updated_at
        WHERE id = (
            SELECT id 
            FROM ams_asset_tyres_treads 
            WHERE asset_id = NEW.asset_id 
            ORDER BY thread_sequence DESC 
            LIMIT 1
        );

        
    END IF;

    -- Handle HM updates
    IF OLD.prev_km_hm_data_unavailable = FALSE AND OLD.prev_total_hm != NEW.prev_total_hm THEN
        -- Update latest tread
        UPDATE ams_asset_tyres_treads
        SET total_hm = total_hm + (NEW.prev_total_hm - OLD.prev_total_hm),
            updated_at = NEW.updated_at
        WHERE id = (
            SELECT id 
            FROM ams_asset_tyres_treads 
            WHERE asset_id = NEW.asset_id 
            ORDER BY thread_sequence DESC 
            LIMIT 1
        );

    END IF;

  RETURN NEW;
END;
$$;

ALTER TABLE ams_asset_tyre_stats_histories
ALTER COLUMN created_at SET NOT NULL;

CREATE UNIQUE INDEX IF NOT EXISTS idx_ams_asset_tyre_stats_histories_created_at_asset_id ON ams_asset_tyre_stats_histories (asset_id, created_at);

alter table ams_asset_tyre_stats_histories REPLICA IDENTITY USING INDEX idx_ams_asset_tyre_stats_histories_created_at_asset_id;

COMMIT;