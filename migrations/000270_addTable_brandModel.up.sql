CREATE TABLE IF NOT EXISTS "ams_asset_models" (
    id VARCHAR(40) PRIMARY KEY,
    asset_model_name VARCHAR(25) NOT NULL,
    brand_id VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    deleted_at TIMESTAMPTZ,
    updated_by VA<PERSON><PERSON><PERSON>(40) NULL,
    created_by VARCHAR(40) NOT NULL,
    CONSTRAINT fk_brand_id FOREIGN KEY(brand_id) REFERENCES ams_brands(id)
);


ALTER TABLE IF EXISTS "ams_assets"
    ADD COLUMN IF NOT EXISTS "model_id" varchar(40),
    ADD CONSTRAINT fk_model_id FOREIGN KEY (model_id) REFERENCES "ams_asset_models"(id);