begin;

CREATE TABLE
    IF NOT EXISTS "sts_INVENTORY_STATUSES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "sts_INVENTORY_STATUSES" (code, "label", description)
VALUES
    ('ACTIVE', 'Active', '-'),
    ('DELETED', 'Deleted', '-'),
    ('INACTIVE', 'Inactive', '-') ON CONFLICT (code) DO NOTHING;

CREATE TABLE
    IF NOT EXISTS "sts_PRODUCT_STATUSES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "sts_PRODUCT_STATUSES" (code, "label", description)
VALUES
    ('ACTIVE', 'Active', '-'),
    ('DELETED', 'Deleted', '-'),
    ('INACTIVE', 'Inactive', '-') ON CONFLICT (code) DO NOTHING;

CREATE TABLE
    IF NOT EXISTS "sts_PRODUCT_UOMS" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "sts_PRODUCT_UOMS" (code, "label", description)
VALUES
    ('PIECES', 'Pieces', '-'),
    ('UNIT', 'Unit', '-') ON CONFLICT (code) DO NOTHING; 
  
CREATE TABLE
    IF NOT EXISTS inv_product_categories (
        id VARCHAR(40) PRIMARY KEY,
        category_name VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );

CREATE INDEX "idx_inv_product_categories_deleted_at" ON "inv_product_categories" ("deleted_at");
   
INSERT INTO
    "inv_product_categories" (id,category_name, description, client_id,created_at,updated_at,updated_by,created_by)
VALUES
    ('prc_2024-05-4-2024-1','Vehicle','Vehicle','GENERAL',now(),now(),'ADMIN','ADMIN'),
    ('prc_2024-05-4-2024-2','Electronic','Electronic','GENERAL',now(),now(),'ADMIN','ADMIN'),
    ('prc_2024-05-4-2024-3','Furniture','Furniture','GENERAL',now(),now(),'ADMIN','ADMIN'),
    ('prc_2024-05-4-2024-4','Tyre','Tyre','GENERAL',now(),now(),'ADMIN','ADMIN'),
    ('prc_2024-05-4-2024-5','Handphone & Tablet','Handphone & Tablet','GENERAL',now(),now(),'ADMIN','ADMIN'),
    ('prc_2024-05-4-2024-6','Computer & Laptop','Computer & Laptop','GENERAL',now(),now(),'ADMIN','ADMIN'); 
   
CREATE TABLE
    IF NOT EXISTS inv_products (
        id VARCHAR(40) PRIMARY KEY,
        sku VARCHAR(100) NOT NULL,
        name VARCHAR(20) NOT NULL,
        uom_code VARCHAR(20) REFERENCES "sts_PRODUCT_UOMS" (code) NOT NULL,
        category_id VARCHAR(40) REFERENCES "inv_product_categories" (id) NOT NULL,
        description VARCHAR(255),
        status_code VARCHAR(20) REFERENCES "sts_PRODUCT_STATUSES" (code) NOT NULL,
        photo VARCHAR(255),
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );
   
 CREATE INDEX "idx_inv_products_deleted_at" ON "inv_products" ("deleted_at");
   
 CREATE TABLE
    IF NOT EXISTS inv_inventories (
        id VARCHAR(40) PRIMARY KEY,
        product_id VARCHAR(40) REFERENCES "inv_products" (id) NOT NULL,
        location_id VARCHAR(40) NOT NULL,
        quantity BIGINT NOT NULL,
        available_quantity BIGINT NOT NULL,
        minimum_quantity BIGINT NOT NULL,
        status_code VARCHAR(20) REFERENCES "sts_INVENTORY_STATUSES" (code) NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );
 
CREATE INDEX "idx_inv_inventories_deleted_at" ON "inv_inventories" ("deleted_at");

CREATE TABLE
    IF NOT EXISTS "sts_INVENTORY_TRANSACTION_TYPES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );
   
INSERT INTO
    "sts_INVENTORY_TRANSACTION_TYPES" (code, "label", description)
VALUES
    ('STOCK_IN', 'Stock In', '-'),
    ('STOCK_OUT', 'Stock Out', '-'),
    ('STOCK_TRANSFER', 'Stock Transfer', '-'),
    ('STOCK_AUDIT', 'Stock Audit', '-') ON CONFLICT (code) DO NOTHING;
   
 CREATE TABLE
    IF NOT EXISTS inv_inventory_transactions (
        id VARCHAR(40) PRIMARY KEY,
        transaction_date TIMESTAMPTZ NOT NULL,
        transaction_type_code VARCHAR(20) REFERENCES "sts_INVENTORY_TRANSACTION_TYPES" (code) NOT NULL,
        source_location_id VARCHAR(40) NOT NULL,
        destination_location_id VARCHAR(40) NOT NULL,
        partner_id VARCHAR(40) NOT NULL,
        reference VARCHAR(100) NOT NULL,
        transaction_number VARCHAR(30) NOT NULL,
        grand_total BIGINT NOT NULL,
        notes TEXT NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );
 
CREATE INDEX "idx_inv_inventory_transactions_deleted_at" ON "inv_inventory_transactions" ("deleted_at");
    
 CREATE TABLE
    IF NOT EXISTS inv_inventory_transaction_items (
        id VARCHAR(40) PRIMARY KEY,
        transaction_id VARCHAR(40) REFERENCES "inv_inventory_transactions" (id) NOT NULL,
        product_id VARCHAR(40) REFERENCES "inv_products" (id) NOT NULL,
        quantity BIGINT NOT NULL,
        price BIGINT NOT NULL,
        uom_code VARCHAR(20) REFERENCES "sts_PRODUCT_UOMS" (code) NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );
 
CREATE INDEX "idx_inv_inventory_transaction_items_deleted_at" ON "inv_inventory_transaction_items" ("deleted_at"); 


CREATE OR REPLACE FUNCTION trigger_set_new_transaction_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    CASE
    WHEN it.transaction_type_code = 'STOCK_IN' THEN
    'INV-IN-' || 
    to_char(created_at, 'YYMMDD') || '-' ||
    lpad((row_number() over (partition BY NEW.client_id,  date_trunc('day',created_at) order by created_at))::text,4,'0')
    WHEN it.transaction_type_code = 'STOCK_OUT' THEN 
    'INV-OUT-' || 
    to_char(created_at, 'YYMMDD') || '-' ||
    lpad((row_number() over (partition BY NEW.client_id,  date_trunc('day',created_at) order by created_at))::text,4,'0')
    WHEN it.transaction_type_code = 'STOCK_TRANSFER' THEN 
    'INV-TR-' || 
    to_char(created_at, 'YYMMDD') || '-' ||
    lpad((row_number() over (partition BY NEW.client_id,  date_trunc('day',created_at) order by created_at))::text,4,'0')
    END
    AS new_transaction_number,
    transaction_number
  FROM
    inv_inventory_transactions it
  WHERE
   it.deleted_at IS NULL 
   AND client_id = NEW.client_id
   AND transaction_type_code = NEW.transaction_type_code
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE inv_inventory_transactions AS it SET transaction_number = cte.new_transaction_number FROM cte WHERE it.id = cte.id AND (cte.transaction_number IS NULL OR cte.transaction_number = '');
RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS set_number_after_insert_inventory_transaction ON inv_inventory_transactions;

CREATE TRIGGER set_number_after_insert_inventory_transaction
  AFTER INSERT
  ON inv_inventory_transactions
  FOR EACH ROW
  EXECUTE PROCEDURE trigger_set_new_transaction_number();

INSERT INTO
    "sts_ATTACHMENT_REFERENCE" (code, "label", description)
VALUES
    ('INV_TRANSACTION', 'Inv Transaction', 'Inventory Transaction') ON CONFLICT (code) DO NOTHING;

commit;