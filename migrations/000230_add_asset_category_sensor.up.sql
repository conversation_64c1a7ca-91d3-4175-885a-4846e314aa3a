BEGIN;

INSERT INTO
    "ams_ASSET_CATEGORIES" (code, "label", description)
VALUES
    ('GENERAL_SENSOR','Sensor', '-')
    ON CONFLICT (code) DO NOTHING;

INSERT INTO "ams_ASSET_SUB_CATEGORIES" (code, "label", description)
VALUES
    ('TYRE_SENSOR', 'Tyre Sensor', 'Tyre Sensor')
    ON CONFLICT (code) DO NOTHING;
    
INSERT INTO
    "ams_ASSET_CATEGORY_MAPPING" (category_code, sub_category_code)
VALUES
    ('GENERAL_SENSOR', 'TYRE_SENSOR') ON CONFLICT (category_code, sub_category_code) DO NOTHING;

COMMIT;