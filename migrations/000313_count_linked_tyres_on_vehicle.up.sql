BEGIN;

ALTER TABLE ams_asset_vehicles ADD COLUMN IF NOT EXISTS count_linked_tyre_by_trigger smallint DEFAULT 0;

WITH cte AS (
    SELECT count(1) AS c, parent_asset_id from ams_linked_assets
    WHERE linked_asset_type_code = 'VEHICLE_TYRE'
    AND unlinked_datetime IS NULL GROUP BY parent_asset_id
) UPDATE ams_asset_vehicles 
SET count_linked_tyre_by_trigger = cte.c 
FROM cte WHERE cte.parent_asset_id = ams_asset_vehicles.asset_id AND ams_asset_vehicles.deleted_at IS NULL;

CREATE OR REPLACE FUNCTION update_count_linked_tyre_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE ams_asset_vehicles
        SET count_linked_tyre_by_trigger = count_linked_tyre_by_trigger + 1
        WHERE asset_id = NEW.parent_asset_id
        AND NEW.linked_asset_type_code = 'VEHIC<PERSON>_TYRE'
        AND NEW.unlinked_datetime IS NULL;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.unlinked_datetime IS NULL AND NEW.unlinked_datetime IS NOT NULL THEN
            UPDATE ams_asset_vehicles
            SET count_linked_tyre_by_trigger = count_linked_tyre_by_trigger - 1
            WHERE asset_id = OLD.parent_asset_id
            AND OLD.linked_asset_type_code = 'VEHICLE_TYRE';
        ELSIF OLD.unlinked_datetime IS NOT NULL AND NEW.unlinked_datetime IS NULL THEN
            UPDATE ams_asset_vehicles
            SET count_linked_tyre_by_trigger = count_linked_tyre_by_trigger + 1
            WHERE asset_id = NEW.parent_asset_id
            AND NEW.linked_asset_type_code = 'VEHICLE_TYRE';
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_count_linked_tyre ON ams_linked_assets;

CREATE TRIGGER update_count_linked_tyre
AFTER INSERT OR UPDATE ON ams_linked_assets
FOR EACH ROW
EXECUTE FUNCTION update_count_linked_tyre_trigger();

COMMIT;