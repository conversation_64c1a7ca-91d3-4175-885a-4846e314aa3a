UPDATE "ins_ALERT_PARAMETERS" SET
label = 'Engine Motorhours',
description = 'Engine Operating Hours since telematics monitoring was activated'
WHERE label = 'Counted Engine Hours';

UPDATE "ins_ALERT_PARAMETERS" SET
label = 'Trip Engine Motorhours',
description = 'Total Engine Operating Hours Since the Trip was reset'
WHERE label = 'Total Engine Hours';

UPDATE "ins_ALERT_PARAMETERS" SET
label = 'Tracker Counted Mileage',
description = 'Total distance travelled in KM since telematics monitoring was activated'
WHERE label = 'Counted Mileage (KM)';

UPDATE "ins_ALERT_PARAMETERS" SET
description = 'Vehicle Starter Battery Voltage level'
WHERE label = 'General Battery Voltage';