BEGIN;

ALTER TABLE "ams_asset_inspection_vehicle" 
ADD COLUMN IF NOT EXISTS number_of_tyres int,
ADD COLUMN IF NOT EXISTS number_of_spare_tyres int;


WITH cte AS (
SELECT
    asset_vehicle_id,
    CASE
        WHEN axle_type IN (
            'TWO_TYRES_WITH_STEERING_WHEEL',
            'TWO_TYRES',
            'TWO_TYRES_WITH_SPINDLE'
        ) THEN 2
        WHEN axle_type IN ('FOUR_TYRES', 'FOUR_TYRES_WITH_SPINDLE') THEN 4
        ELSE 0
    END AS num_of_tyres,
    CASE
        WHEN axle_type = 'ONE_SPARE_TYRE' THEN 1
        ELSE 0
    END AS num_of_spare_tyres
FROM
    (
        SELECT
            asset_vehicle_id,
            jsonb_array_elements (axle_configuration) ->> 'axle' AS axle_type
        FROM
            ams_asset_inspection_vehicle
        ORDER BY
            asset_vehicle_id
    ) T)
UPDATE ams_asset_inspection_vehicle
SET
    number_of_tyres = cte.num_of_tyres,
    number_of_spare_tyres = cte.num_of_spare_tyres
FROM
    cte
WHERE
    ams_asset_inspection_vehicle.asset_vehicle_id = cte.asset_vehicle_id;

COMMIT;