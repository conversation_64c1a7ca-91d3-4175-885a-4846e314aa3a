CREATE TABLE IF NOT EXISTS "ins_integration_commands" (
    "id" character varying(40) NOT NULL,
    "created_at" timestamptz NULL DEFAULT NOW (),
    "updated_at" timestamptz NULL DEFAULT NOW (),
    "deleted_at" timestamptz NULL,
    "integration_id" character varying(40),
    "command_type_code" character varying(20),
    "status_code" character varying(20),
    "client_id" character varying(40) NOT NULL,
    "created_by" character varying(40) NULL,
    "updated_by" character varying(40) NULL,
    PRIMARY KEY("id")
);

INSERT INTO "ins_integration_commands" 
("id",  "command_type_code", "status_code", "client_id", "created_by", "updated_by", "integration_id") 
VALUES 
('cmd-0c64f7a8-4b64-4bcf-8c0f-d2f87c0f5c00',  'ASSET_LOCK', 'SUCCESS', 'cln_fa97045a-dc68-4717-9bfa-d02790a0dd48', 'usr_b1c5b738-2c60-48c7-b74b-5ff5f1423576', NULL, ''),
('cmd-1a32d0c4-94f5-4d21-b916-ef104a94e874',  'ASSET_LOCK', 'SUCCESS', 'cln_fa97045a-dc68-4717-9bfa-d02790a0dd48', 'usr_b1c5b738-2c60-48c7-b74b-5ff5f1423576', NULL, ''),
('cmd-2d3c5b12-e142-4b98-b674-fc2f5f0e6a3d',  'ASSET_LOCK', 'SUCCESS', 'cln_fa97045a-dc68-4717-9bfa-d02790a0dd48', 'usr_b1c5b738-2c60-48c7-b74b-5ff5f1423576', NULL, ''),
('cmd-3e18c7a2-1c9d-44e6-989d-9b4ed4e9166c', 'ASSET_LOCK', 'FAILED', 'cln_fa97045a-dc68-4717-9bfa-d02790a0dd48', 'usr_b1c5b738-2c60-48c7-b74b-5ff5f1423576', NULL, ''),
('cmd-4f43b0b8-f6f1-46a5-8a58-7c5f987d4a51',  'ASSET_LOCK', 'FAILED', 'cln_fa97045a-dc68-4717-9bfa-d02790a0dd48', 'usr_b1c5b738-2c60-48c7-b74b-5ff5f1423576', NULL, ''),
('cmd-5b18f3d9-bac2-4df4-961d-7b334aafce9a',  'ASSET_UNLOCK', 'FAILED', 'cln_fa97045a-dc68-4717-9bfa-d02790a0dd48', 'usr_b1c5b738-2c60-48c7-b74b-5ff5f1423576', NULL, ''),
('cmd-6d77e1f1-cd11-4d7a-925e-4a8e0a5e0fa1',  'ASSET_UNLOCK', 'FAILED', 'cln_fa97045a-dc68-4717-9bfa-d02790a0dd48', 'usr_b1c5b738-2c60-48c7-b74b-5ff5f1423576', NULL, ''),
('cmd-8b20d9e0-b8d4-4b6c-91e9-9f6f05a9c104',  'ASSET_UNLOCK', 'SUCCESS', 'cln_fa97045a-dc68-4717-9bfa-d02790a0dd48', 'usr_b1c5b738-2c60-48c7-b74b-5ff5f1423576', NULL, ''),
('cmd-9a15d5a8-e3d3-4c7b-b735-6a1e3d1b2b4d',  'ASSET_UNLOCK', 'SUCCESS', 'cln_fa97045a-dc68-4717-9bfa-d02790a0dd48', 'usr_b1c5b738-2c60-48c7-b74b-5ff5f1423576', NULL, '')
ON CONFLICT (id) DO NOTHING;


CREATE TABLE IF NOT EXISTS "ins_INTEGRATION_COMMANDS_TYPES" (
    "code" character varying(20) NOT NULL,
    "label" character varying(20) NOT NULL,
    "description" character varying(50) NOT NULL,
    PRIMARY KEY ("code")
);

INSERT INTO
    "ins_INTEGRATION_COMMANDS_TYPES" (code, label, description) VALUES 
    (
        'ASSET_LOCK',
        'Asset Lock',
        'Asset Lock'
    ),
    (
        'ASSET_UNLOCK',
        'Asset Unlock',
        'Asset Unlock'
    ) ON CONFLICT (code) DO NOTHING;


CREATE TABLE IF NOT EXISTS "ins_INTEGRATION_COMMANDS_STATUSES" (
    "code" character varying(20) NOT NULL,
    "label" character varying(20) NOT NULL,
    "description" character varying(50) NOT NULL,
    PRIMARY KEY ("code")
);

INSERT INTO
    "ins_INTEGRATION_COMMANDS_STATUSES" (code, label, description) VALUES 
    (
        'SUCCESS',
        'Success',
        'Success'
    ),
    (
        'FAILED',
        'Failed',
        'Failed'
    ) ON CONFLICT (code) DO NOTHING;