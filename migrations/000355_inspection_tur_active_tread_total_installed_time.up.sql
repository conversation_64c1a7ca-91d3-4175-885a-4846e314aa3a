BEGIN;

ALTER TABLE ams_asset_inspection_tyre
ADD COLUMN IF NOT EXISTS utilization_rate_percentage_status_code VARCHAR(20) REFERENCES "ams_UTILIZATION_RATE_PERCENTAGE_STATUSES" (code),
ADD COLUMN IF NOT EXISTS retread_number INT,
ADD COLUMN IF NOT EXISTS total_lifetime BIGINT;

WITH cte AS (
    SELECT
        id,
        CASE original_td WHEN NULL THEN NULL WHEN 0 THEN NULL ELSE ((original_td - LEAST(original_td, average_rtd))/ original_td) * 100 END AS utilization_rate_percentage
    FROM
        ams_asset_inspection_tyre
)
UPDATE ams_asset_inspection_tyre
SET
    utilization_rate_percentage = cte.utilization_rate_percentage,
    utilization_rate_percentage_status_code = CASE
        WHEN cte.utilization_rate_percentage <= 30 THEN 'GOOD'
        WHEN cte.utilization_rate_percentage <= 60 THEN 'MODERATE'
        WHEN cte.utilization_rate_percentage <= 80 THEN 'LOW'
        WHEN cte.utilization_rate_percentage > 80 THEN 'CRITICAL'
    END
FROM
    cte
WHERE
    cte.id = ams_asset_inspection_tyre.id;

CREATE OR REPLACE FUNCTION update_inspection_tyre_utilization_rate_percentage()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.original_td IS NULL THEN
        NEW.utilization_rate_percentage = NULL;
    ELSIF NEW.original_td = 0 THEN
        NEW.utilization_rate_percentage = NULL;
    ELSE
        NEW.utilization_rate_percentage = ((NEW.original_td - LEAST(NEW.average_rtd, NEW.original_td)) / NEW.original_td) * 100;
    END IF;

     IF NEW.utilization_rate_percentage <= 30 THEN
        NEW.utilization_rate_percentage_status_code = 'GOOD';
    ELSIF NEW.utilization_rate_percentage <= 60 THEN
        NEW.utilization_rate_percentage_status_code = 'MODERATE';
    ELSIF NEW.utilization_rate_percentage <= 80 THEN
        NEW.utilization_rate_percentage_status_code = 'LOW';
    ELSIF NEW.utilization_rate_percentage > 80 THEN
        NEW.utilization_rate_percentage_status_code = 'CRITICAL';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_inspection_tyre_utilization_rate_percentage ON ams_asset_inspection_tyre;

CREATE TRIGGER trigger_update_inspection_tyre_utilization_rate_percentage
BEFORE INSERT OR UPDATE ON ams_asset_inspection_tyre
FOR EACH ROW
EXECUTE FUNCTION update_inspection_tyre_utilization_rate_percentage();



-- blm hanlde case yg single tyre
WITH cte AS (SELECT
	aait.id,
	aait.created_at,
	CASE
		WHEN lifetime IS NOT NULL THEN lifetime + aait.created_at - linked_datetime
		ELSE aait.created_at - linked_datetime
	END,
	round(EXTRACT(EPOCH FROM
	CASE
		WHEN lifetime IS NOT NULL THEN lifetime + aait.created_at - linked_datetime
		ELSE aait.created_at - linked_datetime
	END)) AS total_lifetime,
	t2.*
FROM
	ams_asset_inspection_tyre aait
JOIN 
	(
	SELECT
		child_asset_id,
		linked_datetime,
		t_unlinked_datetime,
		unlinked_datetime,
		sum(prev) OVER (PARTITION BY child_asset_id
	ORDER BY
		linked_datetime) AS lifetime
	FROM
		(
		SELECT
			ala.child_asset_id,
			ala.linked_datetime,
			ala.unlinked_datetime AS t_unlinked_datetime,
			CASE
				WHEN ala.unlinked_datetime IS NULL THEN now ()
				ELSE ala.unlinked_datetime
			END AS unlinked_datetime,
			CASE
				WHEN ala.unlinked_datetime IS NULL THEN now ()
				ELSE ala.unlinked_datetime
			END - ala.linked_datetime AS gap,
			LAG (
				CASE
					WHEN ala.unlinked_datetime IS NULL THEN now ()
				ELSE ala.unlinked_datetime
			END - ala.linked_datetime
			) OVER (
				PARTITION BY
					ala.child_asset_id
		ORDER BY
					ala.child_asset_id,
					ala.linked_datetime
			) AS prev
		FROM
			ams_linked_assets ala
		JOIN ams_linked_asset_vehicle_tyres alavt ON
			alavt.asset_linked_id = ala.id
		ORDER BY
			ala.child_asset_id,
			ala.linked_datetime
	) AS t) AS t2 ON
	t2.child_asset_id = aait.asset_tyre_id
	AND aait.created_at >= linked_datetime
	AND aait.created_at <= unlinked_datetime
ORDER BY
	child_asset_id,
	linked_datetime,
	aait.created_at)
UPDATE ams_asset_inspection_tyre aait SET total_lifetime = cte.total_lifetime FROM cte WHERE cte.id = aait.id;


WITH cte AS (SELECT
	aait.id,
	ROW_NUMBER() OVER (PARTITION BY aait.id ORDER BY aait.created_at - linked_datetime) AS rn,
	aait.created_at - linked_datetime,
	round(EXTRACT(EPOCH FROM
	CASE
		WHEN lifetime IS NOT NULL THEN lifetime + aait.created_at - linked_datetime
		ELSE aait.created_at - linked_datetime
	END)) AS total_lifetime
FROM
	ams_asset_inspection_tyre aait
LEFT JOIN ams_asset_inspection_vehicle aaiv ON aaiv.asset_inspection_id = aait.asset_inspection_id
JOIN 
	(
	SELECT
		child_asset_id,
		linked_datetime,
		t_unlinked_datetime,
		unlinked_datetime,
		sum(prev) OVER (PARTITION BY child_asset_id
	ORDER BY
		linked_datetime) AS lifetime
	FROM
		(
		SELECT
			ala.child_asset_id,
			ala.linked_datetime,
			ala.unlinked_datetime AS t_unlinked_datetime,
			CASE
				WHEN ala.unlinked_datetime IS NULL THEN now ()
				ELSE ala.unlinked_datetime
			END AS unlinked_datetime,
			CASE
				WHEN ala.unlinked_datetime IS NULL THEN now ()
				ELSE ala.unlinked_datetime
			END - ala.linked_datetime AS gap,
			LAG (
				CASE
					WHEN ala.unlinked_datetime IS NULL THEN now ()
				ELSE ala.unlinked_datetime
			END - ala.linked_datetime
			) OVER (
				PARTITION BY
					ala.child_asset_id
		ORDER BY
					ala.child_asset_id,
					ala.linked_datetime
			) AS prev
		FROM
			ams_linked_assets ala
		JOIN ams_linked_asset_vehicle_tyres alavt ON
			alavt.asset_linked_id = ala.id
		ORDER BY
			ala.child_asset_id,
			ala.linked_datetime
	) AS t) AS t2 ON
	t2.child_asset_id = aait.asset_tyre_id
	AND aait.created_at >= linked_datetime WHERE aaiv.id IS NULL ORDER BY aait.created_at, linked_datetime)
	UPDATE ams_asset_inspection_tyre aait SET total_lifetime = cte.total_lifetime FROM cte WHERE cte.id = aait.id AND cte.rn = 1;

COMMIT;