begin;

CREATE TABLE
    IF NOT EXISTS "ins_ANALYTIC_LINK_STATUSES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "ins_ANALYTIC_LINK_STATUSES" (code, "label", description)
VALUES
    ('ACTIVE', 'Active', '-'),
    ('INACTIVE', 'Inactive', '-') ON CONFLICT (code) DO NOTHING;
   
CREATE TABLE
    IF NOT EXISTS "ins_ANALYTIC_LINK_TYPES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "ins_ANALYTIC_LINK_TYPES" (code, "label", description)
VALUES
    ('DASHBOARD_VEHICLE', 'Vehicle Summary', '-'),
    ('DASHBOARD_TYRE', 'Tyre Summary', '-'),
    ('DASHBOARD_INSPECTION', 'Inspection Summary', '-'),
    ('DASHBOARD_WO', 'Work Order Summary', '-') ON CONFLICT (code) DO NOTHING;
   
CREATE TABLE
    IF NOT EXISTS ins_links (
        id VARCHAR(40) PRIMARY KEY,
        link TEXT NOT NULL,
        title VARCHAR(255) NOT NULL,
        size BIGINT,
        admin_permission_code VARCHAR(255) NOT NULL,
        permission_codes VARCHAR(255)[],
        sequence BIGINT NOT NULL,
        link_type_code VARCHAR(20) REFERENCES "ins_ANALYTIC_LINK_TYPES" (code) NOT NULL,
        status_code VARCHAR(20) REFERENCES "ins_ANALYTIC_LINK_STATUSES" (code) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );
 
CREATE INDEX "idx_ins_links_deleted_at" ON "ins_links" ("deleted_at"); 

commit;