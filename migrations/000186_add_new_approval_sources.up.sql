BEGIN;
INSERT INTO
    "app_APPROVAL_SOURCES" (code, "label", description, "user_permission_codes")
SELECT 'ASSET_MEMBERSHIP', 'Asset Membership', '-', '{ASSET_MEMBERSHIP}'
WHERE
NOT EXISTS (
    SELECT code FROM "app_APPROVAL_SOURCES" WHERE code = 'ASSET_MEMBERSHIP'
);


ALTER TABLE "inv_memberships_benefits"
ADD CONSTRAINT memberships_benefits_membership_id_fk_constraint
FOREIGN KEY (membership_id)
REFERENCES "inv_memberships"(id);

COMMIT;