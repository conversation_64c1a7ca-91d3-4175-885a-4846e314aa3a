BEGIN;

-- Create the "ams_ASSET_HANDOVER_REQUEST_STATUSES" table
CREATE TABLE IF NOT EXISTS "ams_ASSET_HANDOVER_REQUEST_STATUSES" (
    code VARCHAR(50) PRIMARY KEY,
    label VARCHAR(50) NOT NULL,
    description TEXT
);

INSERT INTO
    "ams_ASSET_HANDOVER_REQUEST_STATUSES" (code, "label", description)
VALUES
    ('PENDING','Pending', '-'),
    ('ACCEPTED','Accepted', '-'),
    ('REJECTED','Rejected', '-')
    ON CONFLICT (code) DO NOTHING;

-- Create the ams_asset_handover_requests table
CREATE TABLE IF NOT EXISTS ams_asset_handover_requests (
    id VARCHAR(40) PRIMARY KEY,
    asset_id VARCHAR(40),
    before_assigned_user_id VARCHAR(40),
    target_assigned_user_id VARCHAR(40),
    form_template_id VARCHAR(40) NOT NULL,
    need_inspection BOOLEAN,
    reject_reason TEXT,
    status_code VARCHAR(50) REFERENCES "ams_ASSET_HANDOVER_REQUEST_STATUSES"(code),
    confirm_date TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);

-- Add columns to ams_assets table
ALTER TABLE ams_assets
ADD COLUMN IF NOT EXISTS handover_form_template_id VARCHAR(40),
ADD COLUMN IF NOT EXISTS handover_need_inspection BOOLEAN;

ALTER TABLE ams_asset_assignments
ADD COLUMN IF NOT EXISTS unassigned_by_user_id VARCHAR(40),
ADD COLUMN IF NOT EXISTS assigned_by_user_id VARCHAR(40);

INSERT INTO
    "sts_ATTACHMENT_REFERENCE" (code, "label", description)
VALUES
    ('ASSET_HANDOVER', 'Asset Handover', 'Asset Handover') ON CONFLICT (code) DO NOTHING;


COMMIT;