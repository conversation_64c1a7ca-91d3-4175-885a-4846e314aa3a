CREATE TABLE 
    "ins_asset_thresholds" (
        "asset_id" varchar(48) NOT NULL,
        "category_code" VARCHAR(100) NOT NULL,
        "min_value" DECIMAL(18, 3) NOT NULL,
        "max_value" DECIMAL(18, 3) NOT NULL,
        "client_id" varchar(40) not null,
        "created_at" timestamptz NOT NULL DEFAULT NOW (),
        "updated_at" timestamptz NOT NULL DEFAULT NOW (),
        "deleted_at" timestamptz NULL,
        "created_by" character varying(40) NULL,
        "updated_by" character varying(40) NULL,
        PRIMARY KEY (asset_id, category_code)
    );

INSERT INTO "ins_asset_thresholds" (asset_id, category_code, min_value, max_value, created_at, updated_at,created_by,updated_by,client_id) 
VALUES 
    ('ass_4c4da419-4e01-4f13-88da-4351ff207f9d', 'BATTERY_VOLTAGE', 11, 12.7, NOW(), NOW(),'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','cln_2e7400cf-332d-485d-9bc8-6308aa406697'),
    ('ass_4c4da419-4e01-4f13-88da-4351ff207f9d', 'VEHICLE_SPEED', 0, 120, NOW(), NOW(),'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','cln_2e7400cf-332d-485d-9bc8-6308aa406697'),
    ('ass_4c4da419-4e01-4f13-88da-4351ff207f9d', 'ENGINE_TEMPERATURE', 30, 90, NOW(), NOW(),'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','cln_2e7400cf-332d-485d-9bc8-6308aa406697'),
    ('ass_4c4da419-4e01-4f13-88da-4351ff207f9d', 'ENGINE_RPM', 0, 3500, NOW(), NOW(),'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','cln_2e7400cf-332d-485d-9bc8-6308aa406697'),
    ('ass_fef1baf7-833c-42ed-b7cd-45983f10d70c', 'BATTERY_VOLTAGE', 25, 30, NOW(), NOW(),'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','cln_2e7400cf-332d-485d-9bc8-6308aa406697'),
    ('ass_fef1baf7-833c-42ed-b7cd-45983f10d70c', 'VEHICLE_SPEED', 30, 70, NOW(), NOW(),'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','cln_2e7400cf-332d-485d-9bc8-6308aa406697'),
    ('ass_fef1baf7-833c-42ed-b7cd-45983f10d70c', 'ENGINE_TEMPERATURE', 30, 90, NOW(), NOW(),'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','cln_2e7400cf-332d-485d-9bc8-6308aa406697'),
    ('ass_fef1baf7-833c-42ed-b7cd-45983f10d70c', 'ENGINE_RPM', 0, 3000, NOW(), NOW(),'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','cln_2e7400cf-332d-485d-9bc8-6308aa406697'),
    ('ass_fef1baf7-833c-42ed-b7cd-45983f10d70c', 'ENGINE_LOADLEVEL', 0, 100, NOW(), NOW(),'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','cln_2e7400cf-332d-485d-9bc8-6308aa406697'),
    ('ass_67903e71-a671-41b8-9322-4cc1e1b61fa6', 'CURRENT_IMBALANCE', 0, 15, NOW(), NOW(),'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','cln_2e7400cf-332d-485d-9bc8-6308aa406697'),
    ('ass_67903e71-a671-41b8-9322-4cc1e1b61fa6', 'EXHAUST_TEMPERATURE', 0, 105, NOW(), NOW(),'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','cln_2e7400cf-332d-485d-9bc8-6308aa406697'),
    ('ass_67903e71-a671-41b8-9322-4cc1e1b61fa6', 'FEED_PRESSURE', 0, 1, NOW(), NOW(),'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3','cln_2e7400cf-332d-485d-9bc8-6308aa406697');

