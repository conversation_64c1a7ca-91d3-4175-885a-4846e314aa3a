BEGIN;

CREATE TABLE
    IF NOT EXISTS "tks_TICKET_CONTACT_TYPES" (
        code VARCHAR(255) PRIMARY KEY,
        label VARCHAR(255) NOT NULL,
        description TEXT NOT NULL
    );

INSERT INTO
    "tks_TICKET_CONTACT_TYPES" (code, "label", description)
VALUES
    ('PIC_REQUESTER', 'Pic Requester', 'Pic Requester'),
    ('PIC_CHECK_IN', 'Pic Check In', 'Pic Check In'),
    ('PIC_SPK_VEHICLE', 'Pic Spk Vehicle', 'Pic Spk Vehicle'),
    ('PIC_SPK_TYRE', 'Pic Spk Tyre', 'Pic Spk Tyre')
ON CONFLICT (code) DO NOTHING;

CREATE TABLE
    IF NOT EXISTS "tks_TICKET_CONTACT_REFERENCES" (
        code VARCHAR(255) PRIMARY KEY,
        label VARCHAR(255) NOT NULL,
        description TEXT NOT NULL
    );

INSERT INTO
    "tks_TICKET_CONTACT_REFERENCES" (code, "label", description)
VALUES
    ('CUSTOM', 'Custom', 'Custom')
ON CONFLICT (code) DO NOTHING;

CREATE TABLE
    IF NOT EXISTS tks_ticket_contacts (
        id VARCHAR(40) PRIMARY KEY,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL,
        name VARCHAR(255) NOT NULL,
        phone_number VARCHAR(20) NOT NULL,
        email VARCHAR(255) NOT NULL,
        role VARCHAR(255) NOT NULL,
        ticket_id VARCHAR(40) NOT NULL REFERENCES "tks_tickets" (id),
        type_code VARCHAR(255) REFERENCES "tks_TICKET_CONTACT_TYPES" (code),
        reference_source_id VARCHAR(40),
        reference_source_code VARCHAR(255) REFERENCES "tks_TICKET_CONTACT_REFERENCES" (code),
        UNIQUE (ticket_id, type_code)
    );


COMMIT;