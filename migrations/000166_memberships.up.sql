BEGIN;

-- Create table for membership statuses
CREATE TABLE IF NOT EXISTS "inv_MEMBERSHIP_STATUSES" (
    code VARCHAR(255) PRIMARY KEY,
    label VARCHAR(255) NOT NULL,
    description TEXT
);


INSERT INTO
    "inv_MEMBERSHIP_STATUSES" (code, "label", description)
VALUES
    ('ACTIVE', 'Active', '-'),
    ('EXPIRED', 'Expired', '-') ON CONFLICT (code) DO NOTHING;


-- Create table for memberships
CREATE TABLE IF NOT EXISTS inv_memberships (
    id VARCHAR(40) PRIMARY KEY,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    partner_id VARCHAR(40) NOT NULL,
    package_id VARCHAR(40) NOT NULL REFERENCES inv_packages(id),
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    asset_id VARCHAR(40) NOT NULL,
    status_code VARCHAR(255) REFERENCES "inv_MEMBERSHIP_STATUSES"(code)
);

-- Create table for membership benefits
CREATE TABLE IF NOT EXISTS inv_memberships_benefits (
    id VARCHAR(40) PRIMARY KEY,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    membership_id VARCHAR(40) NOT NULL REFERENCES inv_memberships(id),
    package_id VARCHAR(40) NOT NULL,
    package_benefit_id VARCHAR(40) NOT NULL,
    limitation TEXT,
    requirement TEXT,
    quantity INT,
    product_id VARCHAR(40) NOT NULL,
    claimed_quantity INT
);

COMMIT;
