BEGIN;

ALTER TABLE "uis_PERMISSIONS" 
ALTER COLUMN code TYPE VARCHAR(255);

ALTER TABLE "uis_permission_group_rights" 
ALTER COLUMN permission_codes TYPE VARCHAR(255)[];

UPDATE "uis_PERMISSIONS" SET description = 'Users can access the complete list of work orders' WHERE code = 'WORK_ORDER_ADMIN';

INSERT INTO "uis_PERMISSIONS" (user_permission_category_code, code, label, description, display_sequence) VALUES
('DASHBOARD', 'VEHICLE_NO_OF_VEHICLE', 'Vehicle No Of Vehicle', 'User can view Vehicle No Of Vehicle in Dashboard', 1),
('DASHBOARD', 'VEHICLE_STATUS', 'Vehicle Status', 'User can view Vehicle Status in Dashboard', 2),
('DASHBOARD', 'VEHICLE_TYPE', 'Vehicle Type', 'User can view Vehicle Type in Dashboard', 3),
('DASHBOARD', 'VEHICLE_TOTAL_KM', 'Vehicle Total Km', 'User can view Vehicle Total Km in Dashboard', 4),
('DASHBOARD', 'TYRE_NO_OF_TYRES', 'Tyre No Of Tyres', 'User can view Tyre No Of Tyres in Dashboard', 5),
('DASHBOARD', 'TYRE_NEW_STOCK', 'Tyre New Stock', 'User can view Tyre New Stock in Dashboard', 6),
('DASHBOARD', 'TYRE_SIZE', 'Tyre Size', 'User can view Tyre Size in Dashboard', 7),
('DASHBOARD', 'TYRE_BRAND', 'Tyre Brand', 'User can view Tyre Brand in Dashboard', 8),
('DASHBOARD', 'TYRE_RETREAD', 'Tyre Retread', 'User can view Tyre Retread in Dashboard', 9),
('DASHBOARD', 'TYRE_STATUS', 'Tyre Status', 'User can view Tyre Status in Dashboard', 10),
('DASHBOARD', 'TYRE_HIGHEST_KM_PER_TYRE_SIZE', 'Tyre Highest Km Per Tyre Size', 'User can view Tyre Highest Km Per Tyre Size in Dashboard', 11),
('DASHBOARD', 'TYRE_HIGHEST_TREAD_PER_TYRE_SIZE', 'Tyre Highest Tread per Tyre Size', 'User can view Tyre Highest Tread per Tyre Size in Dashboard', 12),
('DASHBOARD', 'TYRE_AVERAGE_KM_PER_MM_ORIGINAL', 'Tyre Average Km Per Mm Original', 'User can view Tyre Average Km Per Mm Original in Dashboard', 13),
('DASHBOARD', 'TYRE_AVERAGE_KM_PER_MM_RETREAD', 'Tyre Average Km Per Mm Retread', 'User can view Tyre Average Km Per Mm Retread in Dashboard', 14),
('DASHBOARD', 'TYRE_SCRAPED_TYRE', 'Tyre Scraped Tyre', 'User can view Tyre Scraped Tyre in Dashboard', 15),
('DASHBOARD', 'TYRE_DISPOSED_TYRE', 'Tyre Disposed Tyre', 'User can view Tyre Disposed Tyre in Dashboard', 16),
('DASHBOARD', 'TYRE_SCRAP_REASON', 'Tyre Scrap Reason', 'User can view Tyre Scrap Reason in Dashboard', 17),
('DASHBOARD', 'WORK_ORDER_SUBJECT', 'Work Order Subject', 'User can view Work Order Subject in Dashboard', 18),
('DASHBOARD', 'WORK_ORDER_STATUS', 'Work Order Status', 'User can view Work Order Status in Dashboard', 19),
('DASHBOARD', 'WORK_ORDER_TOP_10_SUBJECT_STATUS', 'Work Order Top 10 Subject Status', 'User can view Work Order Top 10 Subject Status in Dashboard', 20);

INSERT INTO "uis_PERMISSION_CATEGORIES" VALUES ('UPLOADS', 'Uploads', '', 'uploads.svg', 'Uploads', '', 9);

WITH cte AS (
SELECT
  upgr.id
FROM
  uis_permission_groups upg
JOIN uis_permission_group_rights upgr ON
  upg.id = upgr.permission_group_id
  AND permission_group_type_code = 'ADMIN'
  AND upgr.permission_category_code = 'DASHBOARD')
  UPDATE
  uis_permission_group_rights AS upgr SET
  permission_codes = '{VEHICLE_NO_OF_VEHICLE, VEHICLE_STATUS, VEHICLE_TYPE, VEHICLE_TOTAL_KM, TYRE_NO_OF_TYRES, TYRE_NEW_STOCK, TYRE_SIZE, TYRE_BRAND, TYRE_RETREAD, TYRE_STATUS, TYRE_HIGHEST_KM_PER_TYRE_SIZE, TYRE_HIGHEST_TREAD_PER_TYRE_SIZE, TYRE_AVERAGE_KM_PER_MM_ORIGINAL, TYRE_AVERAGE_KM_PER_MM_RETREAD, TYRE_SCRAPED_TYRE, TYRE_DISPOSED_TYRE, TYRE_SCRAP_REASON, WORK_ORDER_SUBJECT, WORK_ORDER_STATUS, WORK_ORDER_TOP_10_SUBJECT_STATUS}'
FROM
  cte
WHERE
  upgr.id = cte.id;

INSERT INTO "uis_PERMISSIONS" (user_permission_category_code, code, label, description, display_sequence) VALUES
('UPLOADS', 'UPLOAD_TYRES', 'Upload Tyres', 'User can bulk Upload Tyres', 1),
('UPLOADS', 'UPLOAD_VEHICLES', 'Upload Vehicles', 'User can bulk Upload Vehicles', 2),
('UPLOADS', 'UPLOAD_LINKED_TYRES', 'Upload Linked Tyres', 'User can bulk Upload Linked Tyres', 3);

INSERT INTO uis_permission_group_rights SELECT
  concat('pgr_', uuid_generate_v4()),
  id,
  '{UPLOAD_TYRES, UPLOAD_VEHICLES, UPLOAD_LINKED_TYRES}',
  TRUE,
  client_id,
  NOW(),
  NOW(),
  NULL,
  'UPLOADS'
FROM
  uis_permission_groups upg where permission_group_type_code = 'ADMIN';

COMMIT;