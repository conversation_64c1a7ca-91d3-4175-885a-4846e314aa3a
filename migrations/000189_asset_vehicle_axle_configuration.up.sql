BEGIN;

ALTER TABLE ams_asset_vehicles 
ADD COLUMN IF NOT EXISTS axle_configuration VARCHAR(255)[];

WITH cte AS (
    SELECT
    aav.asset_id,
    av.axle_configuration
    FROM
    ams_asset_vehicles aav
    JOIN ams_vehicles av ON
    av.id = aav.vehicle_id
    WHERE
    aav.deleted_at IS NULL
)
UPDATE
  ams_asset_vehicles
SET
  axle_configuration = cte.axle_configuration
FROM
  cte
WHERE
  ams_asset_vehicles.asset_id = cte.asset_id;

COMMIT;