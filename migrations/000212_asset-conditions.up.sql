BEGIN;

CREATE TABLE
    IF NOT EXISTS "ams_ASSET_INITIAL_CONDITIONS" (
        "code" VARCHAR(255) NOT NULL,
        "label" VARCHAR(255) NOT NULL,
        "description" TEXT NOT NULL,
        PRIMARY KEY ("code")
    );

INSERT INTO
    "ams_ASSET_INITIAL_CONDITIONS" (code, "label", description)
VALUES
    ('NEW', 'New', 'New'),
    ('USED', 'Used', 'Used') ON CONFLICT (code) DO NOTHING;

ALTER TABLE "ams_assets"
ADD COLUMN IF NOT EXISTS initial_condition_code varchar(255) REFERENCES "ams_ASSET_INITIAL_CONDITIONS" (code) DEFAULT 'NEW',
ADD COLUMN IF NOT EXISTS partner_owner_name varchar(255);

UPDATE "ams_assets" AS tt SET partner_owner_name = "name" WHERE partner_owner_id IS NOT NULL;

COMMIT;
