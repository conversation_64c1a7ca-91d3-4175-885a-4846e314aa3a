BEGIN;

ALTER TABLE ctn_forms DROP CONSTRAINT ctn_forms_reference_id_form_category_code_key;

CREATE UNIQUE INDEX ctn_forms_reference_id_form_category_code ON ctn_forms (reference_id, form_category_code)
WHERE deleted_at IS NULL;

INSERT INTO
    "ctn_FORM_CATEGORIES" (code, "label", description)
VALUES
    ('FLOW', 'Flow', 'Flow')
    ON CONFLICT (code) DO NOTHING;

INSERT INTO
    "ams_ASSET_INSPECTION_REFERENCE" (code, "label", description)
VALUES
    ('FLOW', 'Flow', 'Flow') ON CONFLICT (code) DO NOTHING;

CREATE TABLE IF NOT EXISTS "ctn_FLOW_REFERENCES" (
    code VARCHAR(255) PRIMARY KEY,
    label VARCHAR(255),
    description VARCHAR(255)
);

INSERT INTO
    "ctn_FLOW_REFERENCES" (code, "label", description)
VALUES
    ('WORK_ORDER', 'Work Order', 'Work Order'),
    ('ASSET_HANDOVER', 'Asset Handover', 'Asset Handover')
     ON CONFLICT (code) DO NOTHING;


CREATE TABLE IF NOT EXISTS "ctn_FLOW_TYPES" (
    code VARCHAR(255) PRIMARY KEY,
    label VARCHAR(255),
    description VARCHAR(255)
);

INSERT INTO
    "ctn_FLOW_TYPES" (code, "label", description)
VALUES
    ('CUSTOM_FORM', 'Custom Form', 'Custom FormD'),
    ('TYRE_INSPECTION', 'Tyre Inspection', 'Tyre Inspection')
     ON CONFLICT (code) DO NOTHING;

CREATE TABLE IF NOT EXISTS "ctn_flows" (
    id VARCHAR(40) PRIMARY KEY,
    reference_id VARCHAR(40) NOT NULL,
    reference_code VARCHAR(40) REFERENCES "ctn_FLOW_REFERENCES" (code) NOT NULL,
    flow_type_id VARCHAR(40),
    flow_type_code VARCHAR(40) REFERENCES "ctn_FLOW_TYPES" (code),    
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);


COMMIT;