BEGIN;

ALTER TABLE ams_asset_tyres
ADD COLUMN IF NOT EXISTS last_inspection_id VARCHAR(40) NULL;

ALTER TABLE ams_asset_tyres_treads
ADD COLUMN IF NOT EXISTS rtd1 numeric(14, 2) NULL,
ADD COLUMN IF NOT EXISTS rtd2 numeric(14, 2) NULL,
ADD COLUMN IF NOT EXISTS rtd3 numeric(14, 2) NULL,
ADD COLUMN IF NOT EXISTS rtd4 numeric(14, 2) NULL,
ADD COLUMN IF NOT EXISTS last_inspection_id VARCHAR(40) NULL,
ADD COLUMN IF NOT EXISTS last_inspected_at TIMESTAMPTZ;

CREATE OR REPLACE FUNCTION update_tread_after_asset_tyre_update()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
    WITH
        cte AS (
            SELECT
                aatt.id, aa.updated_by
            FROM
                ams_asset_tyres_treads aatt JOIN ams_assets aa ON aa.id = aatt.asset_id
            WHERE
                asset_id = NEW.asset_id
            ORDER BY
                thread_sequence DESC
            LIMIT
                1
        )
    UPDATE ams_asset_tyres_treads
    SET
        average_rtd = NEW.average_rtd,
        rtd1 = NEW.rtd1,
        rtd2 = NEW.rtd2,
        rtd3 = NEW.rtd3,
        rtd4 = NEW.rtd4,
        last_inspection_id = NEW.last_inspection_id,
        last_inspected_at = NEW.last_inspected_at,
        total_km = NEW.total_km,
        total_hm = NEW.total_hm,
        total_lifetime = NEW.total_lifetime,
        start_thread_depth= NEW.start_thread_depth,
        updated_by = cte.updated_by,
        updated_at = NEW.updated_at
        FROM cte
    WHERE
        ams_asset_tyres_treads.id = cte.id;

    -- Handle KM updates
    IF OLD.prev_km_hm_data_unavailable = FALSE AND OLD.prev_total_km != NEW.prev_total_km THEN

        -- Update latest tread
        UPDATE ams_asset_tyres_treads
        SET total_km = total_km + (NEW.prev_total_km - OLD.prev_total_km),
            updated_at = NEW.updated_at
        WHERE id = (
            SELECT id 
            FROM ams_asset_tyres_treads 
            WHERE asset_id = NEW.asset_id 
            ORDER BY thread_sequence DESC 
            LIMIT 1
        );

        
    END IF;

    -- Handle HM updates
    IF OLD.prev_km_hm_data_unavailable = FALSE AND OLD.prev_total_hm != NEW.prev_total_hm THEN
        -- Update latest tread
        UPDATE ams_asset_tyres_treads
        SET total_hm = total_hm + (NEW.prev_total_hm - OLD.prev_total_hm),
            updated_at = NEW.updated_at
        WHERE id = (
            SELECT id 
            FROM ams_asset_tyres_treads 
            WHERE asset_id = NEW.asset_id 
            ORDER BY thread_sequence DESC 
            LIMIT 1
        );

    END IF;

  RETURN NEW;
END;
$$;

COMMIT;