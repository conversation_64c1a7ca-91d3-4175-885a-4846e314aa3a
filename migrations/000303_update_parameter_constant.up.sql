UPDATE "ins_ALERT_PARAMETERS" SET label='ABS Failure Indicator Status',description='Status indikator kegagalan sistem rem anti-lock (ABS).',unit='' WHERE code='can_bus.can_abs_failure_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Airbag Indicator Status',description='Status indikator sistem airbag kendaraan.',unit='' WHERE code='can_bus.can_airbag_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Battery Indicator Status',description='Status indikator baterai kendaraan.',unit='' WHERE code='can_bus.can_battery_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Coolant Level Low Indicator Status',description='Status indikator level cairan pendingin rendah.',unit='' WHERE code='can_bus.can_coolant_level_low_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Electric Engine Status',description='Status mesin listrik kendaraan.',unit='' WHERE code='can_bus.can_electric_engine_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Engine Rpm',description='Kecepatan putaran mesin dalam satuan RPM (putaran per menit).',unit='RPM' WHERE code='can_bus.can_engine_rpm';
UPDATE "ins_ALERT_PARAMETERS" SET label='Engine Temperature',description='Suhu mesin kendaraan.',unit='°C' WHERE code='can_bus.can_engine_temperature';
UPDATE "ins_ALERT_PARAMETERS" SET label='Fuel Consumed',description='Total bahan bakar yang telah digunakan kendaraan.',unit='L' WHERE code='can_bus.can_fuel_consumed';
UPDATE "ins_ALERT_PARAMETERS" SET label='Engine Ignition Status',description='Status sistem pengapian mesin.',unit='' WHERE code='can_bus.can_engine_ignition_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Engine Load Level',description='Tingkat beban kerja mesin.',unit='%' WHERE code='can_bus.can_engine_load_level';
UPDATE "ins_ALERT_PARAMETERS" SET label='Engine Working Status',description='Status operasional mesin kendaraan.',unit='' WHERE code='can_bus.can_engine_working_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Tracker Counted Fuel Consumed',description='Bahan bakar yang dihitung oleh pelacak kendaraan.',unit='L' WHERE code='can_bus.can_tracker_counted_fuel_consumed';
UPDATE "ins_ALERT_PARAMETERS" SET label='Odometer',description='Jarak tempuh total kendaraan.',unit='km' WHERE code='can_bus.can_vehicle_mileage';
UPDATE "ins_ALERT_PARAMETERS" SET label='Vehicle Speed',description='Kecepatan kendaraan saat ini.',unit='km/h' WHERE code='can_bus.can_vehicle_speed';
UPDATE "ins_ALERT_PARAMETERS" SET label='Pedal Brake Status',description='Status pengoperasian pedal rem.',unit='' WHERE code='can_bus.can_pedal_brake_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Pedal Clutch Status',description='Status pengoperasian pedal kopling.',unit='' WHERE code='can_bus.can_pedal_clutch_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Throttle Pedal Level',description='Tingkat pengoperasian pedal gas.',unit='%' WHERE code='can_bus.can_throttle_pedal_level';
UPDATE "ins_ALERT_PARAMETERS" SET label='Fuel Level',description='Level bahan bakar dalam tangki kendaraan.',unit='%' WHERE code='can_bus.can_fuel_level';
UPDATE "ins_ALERT_PARAMETERS" SET label='Engine Oil Level',description='Level oli mesin kendaraan.',unit='%' WHERE code='can_bus.can_engine_oil_level';
UPDATE "ins_ALERT_PARAMETERS" SET label='Odometer',description='Jarak tempuh kendaraan berdasarkan data GPS.',unit='km' WHERE code='gps.vehicle_mileage';
UPDATE "ins_ALERT_PARAMETERS" SET label='Internal Device Battery Current',description='Arus baterai kendaraan.',unit='A' WHERE code='general.battery_current';
UPDATE "ins_ALERT_PARAMETERS" SET label='Internal Device Battery Voltage',description='Tegangan baterai kendaraan.',unit='V' WHERE code='general.battery_voltage';
UPDATE "ins_ALERT_PARAMETERS" SET label='Gsm Signal Level',description='Kekuatan sinyal GSM kendaraan.',unit='dBm' WHERE code='general.gsm_signal_level';
UPDATE "ins_ALERT_PARAMETERS" SET label='External Powersource Voltage',description='Tegangan dari sumber daya eksternal.',unit='V' WHERE code='general.external_powersource_voltage';
UPDATE "ins_ALERT_PARAMETERS" SET label='Electronic Power Control Status',description='Status kontrol daya elektronik.',unit='' WHERE code='can_bus.can_electronic_power_control_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Hourmeter',description='Total jam operasi mesin.',unit='h' WHERE code='can_bus.can_trip_engine_motorhours';
UPDATE "ins_ALERT_PARAMETERS" SET label='Fuel Level Low Indicator Status',description='Status indikator level bahan bakar rendah.',unit='' WHERE code='can_bus.can_fuel_level_low_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Handbrake Indicator Status',description='Status indikator rem tangan.',unit='' WHERE code='can_bus.can_handbrake_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Handbrake Status',description='Status pengoperasian rem tangan.',unit='' WHERE code='can_bus.can_handbrake_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Ignition Key Status',description='Status kunci pengapian kendaraan.',unit='' WHERE code='can_bus.can_ignition_key_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Manual Retarder Status',description='Status sistem retarder manual.',unit='' WHERE code='can_bus.can_manual_retarder_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Neutral Gear Status',description='Status posisi gigi netral.',unit='' WHERE code='can_bus.can_neutral_gear_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Oil Pressure Indicator Status',description='Status indikator tekanan oli.',unit='' WHERE code='can_bus.can_oil_pressure_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Power Take-off Status',description='Status pengoperasian power take-off.',unit='' WHERE code='can_bus.can_pto_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Stop Indicator Status',description='Status indikator berhenti kendaraan.',unit='' WHERE code='can_bus.can_stop_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Trailer Axle Lift Status 1',description='Status pengangkatan gandar trailer 1.',unit='' WHERE code='can_bus.can_trailer_axle_lift_status_1';
UPDATE "ins_ALERT_PARAMETERS" SET label='Trailer Axle Lift Status 2',description='Status pengangkatan gandar trailer 2.',unit='' WHERE code='can_bus.can_trailer_axle_lift_status_2';
UPDATE "ins_ALERT_PARAMETERS" SET label='Warning Indicator Status',description='Status indikator peringatan.',unit='' WHERE code='can_bus.can_warning_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Movement Status',description='Status pergerakan umum kendaraan.',unit='' WHERE code='general.movement_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='General Digital Input',description='Status input digital sistem.',unit='' WHERE code='general.digital_input';
UPDATE "ins_ALERT_PARAMETERS" SET label='Hourmeter (DIN)',description='Status input digital dengan mode HM.',unit='' WHERE code='general.digital_input_hm';
UPDATE "ins_ALERT_PARAMETERS" SET label='General Digital Output',description='Status output digital sistem.',unit='' WHERE code='general.digital_output';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Air Feed Pressure',description='Tekanan udara yang dihasilkan oleh kompresor.',unit='psi' WHERE code='compressor.press_air_feed_pressure';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Air Exhaust Temperature',description='Suhu udara buangan dari kompresor.',unit='°C' WHERE code='compressor.press_air_exhaust_temperature';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Phase A Current',description='Arus fase A pada kompresor.',unit='A' WHERE code='compressor.press_phase_a_current';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Phase B Current',description='Arus fase B pada kompresor.',unit='A' WHERE code='compressor.press_phase_b_current';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Phase C Current',description='Arus fase C pada kompresor.',unit='A' WHERE code='compressor.press_phase_c_current';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Load Time',description='Waktu beban kerja kompresor.',unit='h' WHERE code='compressor.press_load_time';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Oil Filter Used Time',description='Waktu penggunaan filter oli kompresor.',unit='h' WHERE code='compressor.press_oil_filter_used_time';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Oil Separator Used Time',description='Waktu penggunaan pemisah oli kompresor.',unit='h' WHERE code='compressor.press_oil_separator_used_time';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Air Filter Used Time',description='Waktu penggunaan filter udara kompresor.',unit='h' WHERE code='compressor.press_air_filter_used_time';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Lube Oil Used Time',description='Waktu penggunaan oli pelumas kompresor.',unit='h' WHERE code='compressor.press_lube_oil_used_time';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Lube Grease Used Time',description='Waktu penggunaan gemuk pelumas kompresor.',unit='h' WHERE code='compressor.press_lube_grease_used_time';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Machine Status',description='Status keseluruhan mesin kompresor.',unit='' WHERE code='compressor.press_machine_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Current Imbalance',description='Ketidakseimbangan arus kompresor.',unit='%' WHERE code='compressor.press_current_imbalance';
UPDATE "ins_ALERT_PARAMETERS" SET label='Hourmeter',description='Total waktu operasional kompresor.',unit='h' WHERE code='compressor.press_run_time';
UPDATE "ins_ALERT_PARAMETERS" SET label='Tyre Battery Voltage',description='Tegangan baterai pada sistem ban.',unit='V' WHERE code='tyre.battery_voltage';
UPDATE "ins_ALERT_PARAMETERS" SET label='Tyre Pressure',description='Tekanan ban kendaraan.',unit='psi' WHERE code='tyre.pressure';
UPDATE "ins_ALERT_PARAMETERS" SET label='Tyre Temperature',description='Suhu ban kendaraan.',unit='°C' WHERE code='tyre.temperature';
UPDATE "ins_ALERT_PARAMETERS" SET label='Cruise Control Status',description='Status sistem kontrol kecepatan kendaraan.',unit='' WHERE code='can_bus.can_cruise_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Drive Gear Status',description='Status gigi penggerak kendaraan.',unit='' WHERE code='can_bus.can_drive_gear_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Driver Seatbelt Indicator Status',description='Status indikator sabuk pengaman pengemudi.',unit='' WHERE code='can_bus.can_driver_seatbelt_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Driver Seatbelt Status',description='Status pemakaian sabuk pengaman pengemudi.',unit='' WHERE code='can_bus.can_driver_seatbelt_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Dynamic Ignition Status',description='Status sistem pengapian dinamis kendaraan.',unit='' WHERE code='can_bus.can_dynamic_ignition_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Engine Lock Status',description='Status penguncian mesin kendaraan.',unit='' WHERE code='can_bus.can_engine_lock_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Electric Power Steering Indicator Status',description='Status indikator sistem kemudi listrik.',unit='' WHERE code='can_bus.can_eps_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Electronic Stability Program Indicator Status',description='Status indikator program stabilitas elektronik.',unit='' WHERE code='can_bus.can_esp_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Program Id',description='Identifikasi program yang sedang berjalan.',unit='' WHERE code='can_bus.can_program_id';
UPDATE "ins_ALERT_PARAMETERS" SET label='Tracker Counted Mileage',description='Jarak tempuh yang dihitung oleh pelacak kendaraan.',unit='km' WHERE code='can_bus.can_tracker_counted_mileage';
UPDATE "ins_ALERT_PARAMETERS" SET label='Gps Position Hdop',description='Horizontal dilution of precision pada data GPS.',unit='' WHERE code='gps.position_hdop';
UPDATE "ins_ALERT_PARAMETERS" SET label='Gps Position Pdop',description='Positional dilution of precision pada data GPS.',unit='' WHERE code='gps.position_pdop';
UPDATE "ins_ALERT_PARAMETERS" SET label='Gps Position Satellites',description='Jumlah satelit GPS yang terdeteksi.',unit='' WHERE code='gps.position_satellites';
UPDATE "ins_ALERT_PARAMETERS" SET label='Gps Position Direction',description='Arah pergerakan kendaraan berdasarkan GPS.',unit='°' WHERE code='gps.position_direction';
UPDATE "ins_ALERT_PARAMETERS" SET label='Gps Position Altitude',description='Ketinggian kendaraan berdasarkan GPS.',unit='m' WHERE code='gps.position_altitude';
UPDATE "ins_ALERT_PARAMETERS" SET label='Gps Position Speed',description='Kecepatan kendaraan berdasarkan data GPS.',unit='km/h' WHERE code='gps.position_speed';
UPDATE "ins_ALERT_PARAMETERS" SET label='Gps Position Latitude',description='Lokasi lintang kendaraan berdasarkan GPS.',unit='°' WHERE code='gps.position_latitude';
UPDATE "ins_ALERT_PARAMETERS" SET label='Gps Position Longitude',description='Lokasi bujur kendaraan berdasarkan GPS.',unit='°' WHERE code='gps.position_longitude';
UPDATE "ins_ALERT_PARAMETERS" SET label='Electronic Stability Program Status',description='Status program stabilitas elektronik.',unit='' WHERE code='can_bus.can_esp_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Factory Armed Status',description='Status sistem keamanan bawaan pabrik.',unit='' WHERE code='can_bus.can_factory_armed_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Front Differential Status',description='Status diferensial depan kendaraan.',unit='' WHERE code='can_bus.can_front_differential_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Front Fog Lights Status',description='Status lampu kabut depan.',unit='' WHERE code='can_bus.can_front_fog_lights_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Front Left Door Status',description='Status pintu depan kiri kendaraan.',unit='' WHERE code='can_bus.can_front_left_door_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Front Passenger Seatbelt Status',description='Status sabuk pengaman penumpang depan.',unit='' WHERE code='can_bus.can_front_passenger_seatbelt_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Front Passenger Status',description='Status keberadaan penumpang depan.',unit='' WHERE code='can_bus.can_front_passenger_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Front Right Door Status',description='Status pintu depan kanan kendaraan.',unit='' WHERE code='can_bus.can_front_right_door_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Glow Plug Indicator Status',description='Status indikator glow plug mesin diesel.',unit='' WHERE code='can_bus.can_glow_plug_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='High Beam Status',description='Status lampu jauh kendaraan.',unit='' WHERE code='can_bus.can_high_beam_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Hood Status',description='Status kap mesin kendaraan.',unit='' WHERE code='can_bus.can_hood_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Interlock Active',description='Status aktif interlock kendaraan.',unit='' WHERE code='can_bus.can_interlock_active';
UPDATE "ins_ALERT_PARAMETERS" SET label='Light Signal Status',description='Status sinyal lampu kendaraan.',unit='' WHERE code='can_bus.can_light_signal_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Lights Failure Indicator Status',description='Status indikator kegagalan lampu.',unit='' WHERE code='can_bus.can_lights_failure_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Lights Hazard Lights Status',description='Status lampu hazard kendaraan.',unit='' WHERE code='can_bus.can_lights_hazard_lights_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Low Beam Status',description='Status lampu dekat kendaraan.',unit='' WHERE code='can_bus.can_low_beam_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Maintenance Required Status',description='Status kebutuhan perawatan kendaraan.',unit='' WHERE code='can_bus.can_maintenance_required_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Module Sleep Mode',description='Status mode tidur modul sistem.',unit='' WHERE code='can_bus.can_module_sleep_mode';
UPDATE "ins_ALERT_PARAMETERS" SET label='Operator Present Status',description='Status keberadaan operator kendaraan.',unit='' WHERE code='can_bus.can_operator_present_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Parking Lights Status',description='Status lampu parkir.',unit='' WHERE code='can_bus.can_parking_lights_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Parking Status',description='Status kendaraan dalam mode parkir.',unit='' WHERE code='can_bus.can_parking_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Passenger Seatbelt Indicator Status',description='Status indikator sabuk pengaman penumpang.',unit='' WHERE code='can_bus.can_passenger_seatbelt_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Ready To Drive Indicator Status',description='Status indikator siap berkendara.',unit='' WHERE code='can_bus.can_ready_to_drive_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Rear Central Passenger Seatbelt Status',description='Status sabuk pengaman penumpang tengah belakang.',unit='' WHERE code='can_bus.can_rear_central_passenger_seatbelt_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Rear Differential Status',description='Status diferensial belakang kendaraan.',unit='' WHERE code='can_bus.can_rear_differential_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Rear Fog Lights Status',description='Status lampu kabut belakang.',unit='' WHERE code='can_bus.can_rear_fog_lights_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Rear Left Door Status',description='Status pintu belakang kiri kendaraan.',unit='' WHERE code='can_bus.can_rear_left_door_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Rear Left Passenger Seatbelt Status',description='Status sabuk pengaman penumpang belakang kiri.',unit='' WHERE code='can_bus.can_rear_left_passenger_seatbelt_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Rear Right Door Status',description='Status pintu belakang kanan kendaraan.',unit='' WHERE code='can_bus.can_rear_right_door_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Rear Right Passenger Seatbelt Status',description='Status sabuk pengaman penumpang belakang kanan.',unit='' WHERE code='can_bus.can_rear_right_passenger_seatbelt_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Reverse Gear Status',description='Status gigi mundur kendaraan.',unit='' WHERE code='can_bus.can_reverse_gear_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Roof Opened Status',description='Status atap kendaraan terbuka.',unit='' WHERE code='can_bus.can_roof_opened_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Soot Filter Indicator Status',description='Status indikator filter partikel mesin diesel.',unit='' WHERE code='can_bus.can_soot_filter_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Standalone Engine',description='Status mesin independen.',unit='' WHERE code='can_bus.can_standalone_engine';
UPDATE "ins_ALERT_PARAMETERS" SET label='Tire Pressure Low Status',description='Status tekanan ban rendah.',unit='' WHERE code='can_bus.can_tire_pressure_low_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Trunk Status',description='Status bagasi kendaraan.',unit='' WHERE code='can_bus.can_trunk_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Vehicle Battery Charging Status',description='Status pengisian baterai kendaraan.',unit='' WHERE code='can_bus.can_vehicle_battery_charging_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Wear Brake Pads Indicator Status',description='Status indikator keausan kampas rem.',unit='' WHERE code='can_bus.can_wear_brake_pads_indicator_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Webasto Status',description='Status pengoperasian sistem pemanas Webasto.',unit='' WHERE code='can_bus.can_webasto_status';
UPDATE "ins_ALERT_PARAMETERS" SET label='Gps Position Valid',description='Validitas data posisi GPS.',unit='' WHERE code='gps.position_valid';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Run State 1',description='Status 1 operasional kompresor.',unit='' WHERE code='compressor.press_run_state_1';
UPDATE "ins_ALERT_PARAMETERS" SET label='Compressor Run State 2',description='Status 2 operasional kompresor.',unit='' WHERE code='compressor.press_run_state_2';
UPDATE "ins_ALERT_PARAMETERS" SET label='Hourmeter',description='Total jam operasi mesin sejak instalasi gateway.',unit='h' WHERE code='can_bus.can_engine_motorhours';

UPDATE "ins_ALERT_PARAMETERS" set label='Engine Working Status',description='Status operasional mesin (Running/Not).',unit='' where code='general.digital_input';