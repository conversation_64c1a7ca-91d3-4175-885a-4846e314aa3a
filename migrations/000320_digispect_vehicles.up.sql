CREATE TABLE
    IF NOT EXISTS ams_digispect_vehicles (
        id VARCHAR(40) PRIMARY KEY,
        reference_number VARCHAR(40),
        partner_owner_name VARCHA<PERSON>(255),
        digispect_brand_id VARCHAR(40) REFERENCES ams_digispect_brand_configs (id),
        digispect_brand_name VA<PERSON>HA<PERSON>(255),
        digispect_config_id VARCHAR(40) REFERENCES ams_digispect_configs (id),
        digispect_config_model VARCHAR(255),
        photo VARCHAR(255),
        last_inspected_datetime TIMESTAMPTZ,
        axle_configuration JSONB DEFAULT '[]'::jsonb,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        updated_by VA<PERSON><PERSON><PERSON>(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL,
        deleted_at TIMESTAMPTZ
    );

CREATE UNIQUE INDEX
    IF NOT EXISTS unique_reference_number_client_id_ams_digispect_vehicles
    ON ams_digispect_vehicles (LOWER(reference_number), client_id)
    WHERE deleted_at IS NULL;

ALTER TABLE ams_asset_inspection_vehicle
ADD COLUMN IF NOT EXISTS digispect_vehicle_id VARCHAR(40) REFERENCES ams_digispect_vehicles (id),
ADD COLUMN IF NOT EXISTS partner_owner_name VARCHAR(255);

