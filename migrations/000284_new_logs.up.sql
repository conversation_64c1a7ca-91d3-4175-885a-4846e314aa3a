BEGIN;


CREATE OR <PERSON><PERSON>LACE FUNCTION log_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
  _new jsonb;
  _old jsonb;
  _k text;
  _id varchar(40);
BEGIN
  _id := concat('log_', uuid_generate_v4());
  _new := to_jsonb(NEW);
  _old := to_jsonb(OLD);

  FOR _k IN SELECT * FROM jsonb_object_keys(_new)
  LOOP
    IF _new[_k] = _old[_k]
      THEN _new = _new - _k; _old = _old - _k;
    END IF;
  END LOOP;

IF _new IS NULL THEN
  _new := '{}'::jsonb;
END IF;

  INSERT INTO log_logs(
  id, table_name, client_id, created_by, 
  created_at, previous_value, new_value) 
VALUES 
  (
    _id, 
    TG_TABLE_NAME, 
    NEW.client_id, 
    NEW.updated_by, 
    NEW.updated_at, 
    _old, 
    _new
  );

  
  IF TG_TABLE_NAME = 'ams_assets' 
    THEN  INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.id);
  ELSIF TG_TABLE_NAME = 'ams_asset_vehicles' 
    THEN  INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.asset_id);

    INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET_VEHICLE', NEW.asset_id);
  ELSIF TG_TABLE_NAME = 'ams_asset_tyres' 
    THEN  INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.asset_id);

    INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET_TYRE', NEW.asset_id);
  ELSIF TG_TABLE_NAME = 'ams_asset_components' 
    THEN  INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.asset_id);

    INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET_COMPONENT', NEW.id);
  ELSIF TG_TABLE_NAME = 'ams_asset_transactions' 
    THEN  INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET', NEW.asset_id);

    INSERT INTO log_log_references(log_id, client_id, created_by, created_at, reference_code, reference_id)
    VALUES(_id, NEW.client_id , NEW.updated_by, NEW.updated_at, 'ASSET_TRANSACTION', NEW.id);
  END IF;

  -- ADD FOR ANOTHER TABLE

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;


CREATE TABLE
    IF NOT EXISTS log_logs (
        id VARCHAR(40) PRIMARY KEY,
        table_name VARCHAR(255) NOT NULL,
        previous_value JSONB,
        new_value JSONB,
        parsed_previous_value JSONB,
        parsed_new_value JSONB,
        is_parsed BOOLEAN DEFAULT FALSE,
        parsed_at TIMESTAMPTZ,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        created_by VARCHAR(40)
    );
  
CREATE TABLE
    IF NOT EXISTS log_log_references (
        id VARCHAR(44) PRIMARY KEY DEFAULT concat('log_ref_', uuid_generate_v4()),
        log_id VARCHAR(40),
        reference_code VARCHAR(20) NOT NULL,
        reference_id VARCHAR(40) NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        created_by VARCHAR(40)
    );
  
  
DROP TRIGGER IF EXISTS create_update_trigger_ams_assets ON ams_assets;

CREATE TRIGGER create_update_trigger_ams_assets
AFTER INSERT OR UPDATE ON ams_assets
FOR EACH ROW
EXECUTE FUNCTION log_trigger_function();

DROP TRIGGER IF EXISTS create_update_trigger_ams_asset_vehicles ON ams_asset_vehicles;

CREATE TRIGGER create_update_trigger_ams_asset_vehicles
AFTER INSERT OR UPDATE ON ams_asset_vehicles
FOR EACH ROW
EXECUTE FUNCTION log_trigger_function();

DROP TRIGGER IF EXISTS create_update_trigger_ams_asset_tyres ON ams_asset_tyres;

CREATE TRIGGER create_update_trigger_ams_asset_tyres
AFTER INSERT OR UPDATE ON ams_asset_tyres
FOR EACH ROW
EXECUTE FUNCTION log_trigger_function();

DROP TRIGGER IF EXISTS create_update_ams_asset_components ON ams_asset_components;

CREATE TRIGGER create_update_ams_asset_components
AFTER INSERT OR UPDATE ON ams_asset_components
FOR EACH ROW
EXECUTE FUNCTION log_trigger_function();

DROP TRIGGER IF EXISTS create_update_ams_asset_transactions ON ams_asset_transactions;

CREATE TRIGGER create_update_ams_asset_transactions
AFTER INSERT OR UPDATE ON ams_asset_transactions
FOR EACH ROW
EXECUTE FUNCTION log_trigger_function();


COMMIT;