BEGIN;

CREATE TABLE IF NOT EXISTS "ams_ASSET_TYRE_TREAD_TYPES" (
    code VARCHAR(255) PRIMARY KEY,
    label VARCHAR(255) NOT NULL,
    description TEXT NOT NULL
);

INSERT INTO
    "ams_ASSET_TYRE_TREAD_TYPES" (code, "label", description)
VALUES
    ('ORIGINAL', 'Original', '-'),
    ('RETREAD', 'Retread', '-'),
    ('REGROOVE', 'Regroove', '-')
ON CONFLICT (code) DO NOTHING;

ALTER TABLE "ams_asset_tyres_treads"
ADD COLUMN IF NOT EXISTS type_code varchar(255) REFERENCES "ams_ASSET_TYRE_TREAD_TYPES" (code);

UPDATE "ams_asset_tyres_treads" SET type_code = 'RETREAD';
UPDATE "ams_asset_tyres_treads" SET type_code = 'ORIGINAL' WHERE "thread_sequence" = 0;


COMMIT;
