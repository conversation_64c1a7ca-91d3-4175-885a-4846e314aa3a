BEGIN;

DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'ins_monitoring_display_configs'
          AND column_name = 'data_mapping_keys'
    ) THEN
        EXECUTE 'ALTER TABLE ins_monitoring_display_configs RENAME COLUMN data_mapping_keys TO hidden_fields';
    END IF;
END$$;

ALTER TABLE ins_monitoring_display_configs
    ADD COLUMN IF NOT EXISTS asset_id VARCHAR(40) NOT NULL;

    BEGIN;

DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.table_constraints
        WHERE constraint_type = 'UNIQUE'
          AND table_name = 'ins_monitoring_display_configs'
          AND constraint_name = 'unique_client_id'
    ) THEN
        ALTER TABLE ins_monitoring_display_configs
        DROP CONSTRAINT unique_client_id;
    END IF;
END
$$;


COMMIT;
