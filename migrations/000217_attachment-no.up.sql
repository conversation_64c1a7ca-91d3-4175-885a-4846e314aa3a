BEGIN;


ALTER TABLE "sts_attachments"
ADD COLUMN IF NOT EXISTS "created_by" VARCHAR(40),
ADD COLUMN IF NOT EXISTS "updated_by" VARCHAR(40),
ADD COLUMN IF NOT EXISTS "number" VARCHAR(63);


-- Populate Past Data
WITH cte AS (
  SELECT
    id,
    'ATT-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    row_number() over (partition by client_id order by created_at) AS new_number,
    "number"
  FROM
    sts_attachments tt
  WHERE
   tt.deleted_at IS NULL
)
UPDATE sts_attachments AS tt SET "number" = cte.new_number FROM cte WHERE tt.id = cte.id;

-- Create Trigger To Set New Number
CREATE OR REPLACE FUNCTION trigger_set_new_sts_attachments_number()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
   WITH cte AS (
  SELECT
    id,
    'ATT-' || 
    to_char(timezone('-7', created_at), 'YYMMDD') || '-' ||
    row_number() over (partition by client_id order by created_at) AS new_number,
    number
  FROM
    sts_attachments tt
  WHERE
   client_id = NEW.client_id
   AND created_at >= date_trunc('day',NOW()) AND created_at < date_trunc('day',NOW()+ INTERVAL '1 day') 
)
UPDATE sts_attachments AS tt SET number = cte.new_number FROM cte WHERE tt.id = cte.id AND (cte.number IS NULL OR cte.number = '');
RETURN NEW;
END;
$$;

-- Define Trigger
CREATE TRIGGER set_number_after_insert_sts_attachments
  AFTER INSERT
  ON sts_attachments
  FOR EACH ROW
  EXECUTE PROCEDURE trigger_set_new_sts_attachments_number();

INSERT INTO
    "sts_ATTACHMENT_REFERENCE" (code, "label", description)
VALUES
    ('ASSET_ATTACHMENT', 'Asset Attachment', 'Asset Attachment') ON CONFLICT (code) DO NOTHING;


COMMIT;