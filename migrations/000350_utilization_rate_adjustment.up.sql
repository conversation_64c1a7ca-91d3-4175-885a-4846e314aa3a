BEGIN;

WITH cte AS (
    SELECT
        asset_id,
        CASE original_td WHEN NULL THEN NULL WHEN 0 THEN NULL ELSE ((original_td - LEAST(average_rtd, original_td))/ original_td) * 100 END AS utilization_rate_percentage
    FROM
        ams_asset_tyres
)
UPDATE ams_asset_tyres
SET
    utilization_rate_percentage = cte.utilization_rate_percentage,
    utilization_rate_percentage_status_code = CASE
        WHEN cte.utilization_rate_percentage <= 30 THEN 'GOOD'
        WHEN cte.utilization_rate_percentage <= 60 THEN 'MODERATE'
        WHEN cte.utilization_rate_percentage <= 80 THEN 'LOW'
        WHEN cte.utilization_rate_percentage > 80 THEN 'CRITICAL'
    END
FROM
    cte
WHERE
    cte.asset_id = ams_asset_tyres.asset_id;

CREATE OR REPLACE FUNCTION update_tyre_utilization_rate_percentage()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.original_td IS NULL THEN
        NEW.utilization_rate_percentage = NULL;
    ELSIF NEW.original_td = 0 THEN
        NEW.utilization_rate_percentage = NULL;
    ELSE
        NEW.utilization_rate_percentage = ((NEW.original_td - LEAST(NEW.average_rtd, NEW.original_td)) / NEW.original_td) * 100;
    END IF;
    IF NEW.utilization_rate_percentage <= 30 THEN
        NEW.utilization_rate_percentage_status_code = 'GOOD';
    ELSIF NEW.utilization_rate_percentage <= 60 THEN
        NEW.utilization_rate_percentage_status_code = 'MODERATE';
    ELSIF NEW.utilization_rate_percentage <= 80 THEN
        NEW.utilization_rate_percentage_status_code = 'LOW';
    ELSIF NEW.utilization_rate_percentage > 80 THEN
        NEW.utilization_rate_percentage_status_code = 'CRITICAL';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

ALTER TABLE ams_asset_inspection_tyre 
ADD COLUMN IF NOT EXISTS original_td NUMERIC(10, 2),
ADD COLUMN IF NOT EXISTS utilization_rate_percentage numeric(14, 2);

UPDATE ams_asset_inspection_tyre 
SET original_td = start_thread_depth
WHERE original_td IS NULL;

UPDATE ams_asset_inspection_tyre 
SET original_td = ams_asset_tyres.original_td
FROM ams_asset_tyres
WHERE ams_asset_inspection_tyre.asset_tyre_id = ams_asset_tyres.asset_id;

WITH cte AS (
    SELECT
        id,
        CASE original_td WHEN NULL THEN NULL WHEN 0 THEN NULL ELSE ((original_td - LEAST(original_td, average_rtd))/ original_td) * 100 END AS utilization_rate_percentage
    FROM
        ams_asset_inspection_tyre
)
UPDATE ams_asset_inspection_tyre
SET
    utilization_rate_percentage = cte.utilization_rate_percentage
FROM
    cte
WHERE
    cte.id = ams_asset_inspection_tyre.id;


CREATE OR REPLACE FUNCTION update_inspection_tyre_utilization_rate_percentage()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.original_td IS NULL THEN
        NEW.utilization_rate_percentage = NULL;
    ELSIF NEW.original_td = 0 THEN
        NEW.utilization_rate_percentage = NULL;
    ELSE
        NEW.utilization_rate_percentage = ((NEW.original_td - LEAST(NEW.average_rtd, NEW.original_td)) / NEW.original_td) * 100;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_inspection_tyre_utilization_rate_percentage ON ams_asset_inspection_tyre;

CREATE TRIGGER trigger_update_inspection_tyre_utilization_rate_percentage
AFTER INSERT OR UPDATE ON ams_asset_inspection_tyre
FOR EACH ROW
EXECUTE FUNCTION update_inspection_tyre_utilization_rate_percentage();

DROP TRIGGER IF EXISTS update_tyre_utilization_rate_percentage ON ams_asset_tyres;

CREATE TRIGGER update_tyre_utilization_rate_percentage
AFTER INSERT OR UPDATE ON ams_asset_tyres
FOR EACH ROW
EXECUTE FUNCTION update_tyre_utilization_rate_percentage();

COMMIT;