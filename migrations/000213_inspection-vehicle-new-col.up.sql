DO $$
BEGIN
IF EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='ams_asset_inspection_vehicle' and column_name='spooring_is_required')
  THEN
      ALTER TABLE "ams_asset_inspection_vehicle" RENAME COLUMN "spooring_is_required" TO "require_spooring_vehicle";
  END IF;
END $$;

ALTER TABLE "ams_asset_inspection_vehicle" 
ADD COLUMN IF NOT EXISTS failed_visual_checking BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS require_rotation_tyre BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS tire_tread_and_rim_damage BOOLEAN DEFAULT false;

ALTER TABLE "ams_asset_inspection_tyre" 
ADD COLUMN IF NOT EXISTS tire_tread_and_rim_damage BOOLEAN DEFAULT false;