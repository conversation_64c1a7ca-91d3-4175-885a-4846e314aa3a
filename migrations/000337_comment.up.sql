BEGIN;

-- Create "ctn_COMMENT_REFERENCES" table
CREATE TABLE
    IF NOT EXISTS "ctn_COMMENT_REFERENCES" (
        "code" character varying(20) NOT NULL,
        "label" character varying(20) NOT NULL,
        "description" character varying(50) NOT NULL,
        PRIMARY KEY ("code")
    );

INSERT INTO
    "ctn_COMMENT_REFERENCES" (code, description, "label")
VALUES
    ('ASSET', '-', 'Asset') ON CONFLICT (code) DO NOTHING;

-- Create "ctn_comments" table
CREATE TABLE
    IF NOT EXISTS "ctn_comments" (
        "id" VARCHAR(40) PRIMARY KEY,
        "reference_id" VARCHAR(40) NULL,
        "reference_code" VARCHAR(20) REFERENCES "ctn_COMMENT_REFERENCES" (code),
        "user_id" VARCHAR(40) NULL,
        "comment" text NULL,
        "client_id" VARCHAR(40) NOT NULL,
        "title" VARCHAR(100) NULL,
        "has_attachment" BOOLEAN DEFAULT FALSE,
        "created_at" TIMESTAMPTZ NULL DEFAULT NOW(),
        "updated_at" TIMESTAMPTZ NULL DEFAULT NOW(),
        "deleted_at" TIMESTAMPTZ NULL
    );

-- Create index "idx_ctn_comments_deleted_at" to table: "ctn_comments"
CREATE INDEX IF NOT EXISTS "idx_ctn_comments_deleted_at" ON "ctn_comments" ("deleted_at");

INSERT INTO
    "sts_ATTACHMENT_REFERENCE" (code, "label", description)
VALUES
    ('COMMENT', 'Comment', 'Comment') ON CONFLICT (code) DO NOTHING;

COMMIT;
