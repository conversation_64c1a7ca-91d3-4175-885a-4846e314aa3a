BEGIN;
ALTER TABLE public."uis_ANALYTICS" ALTER COLUMN code TYPE varchar(100) USING code::varchar(100);
ALTER TABLE public."uis_ANALYTICS" ALTER COLUMN "label" TYPE varchar(100) USING "label"::varchar(100);
ALTER TABLE public."uis_ANALYTICS" ALTER COLUMN description TYPE varchar(100) USING description::varchar(100);
INSERT INTO
    "uis_ANALYTIC_TYPES" (code, "label", description)
VALUES
    ('DASHBOARD_DIGISPECT_SUMMARY_CHART', 'Dashboard Digispect Summary Chart', '-'),
    ('DASHBOARD_DIGISPECT_SUMMARY_CARD', 'Dashboard Digispect Summary Card', '-')
ON CONFLICT (code) DO NOTHING;
INSERT INTO
    "uis_ANALYTICS" (code, "label", description, sequence, analytic_type_code)
VALUES
    ('TOTAL_INSPECTIONS', 'Total Inspections', '-', 1, 'DASHBOARD_DIGISPECT_SUMMARY_CARD'),
    ('TOTAL_CUSTOMERS_INSPECTED', 'Total Customers Inspected', '-', 2, 'DASHBOARD_DIGISPECT_SUMMARY_CARD'),
    ('TOTAL_VEHICLES_INSPECTED', 'Total Vehicles Inspected', '-', 3, 'DASHBOARD_DIGISPECT_SUMMARY_CARD'),
    ('TOTAL_TYRES_INSPECTED', 'Total Tyres Inspected', '-', 4, 'DASHBOARD_DIGISPECT_SUMMARY_CARD'),
    ('TOTAL_INSPECTORS', 'Total Inspectors', '-', 5, 'DASHBOARD_DIGISPECT_SUMMARY_CARD'),
    ('TOTAL_INSPECTIONS_BY_TYPE', 'Total Inspections by Type', '-', 6, 'DASHBOARD_DIGISPECT_SUMMARY_CHART'),
    ('VEHICLES_BASED_ON_INSPECTION_FREQUENCY', 'Number of Vehicles based on Inspection Frequency', '-', 7, 'DASHBOARD_DIGISPECT_SUMMARY_CHART'),
    ('TOTAL_TYRES_INSPECTED_VS_VEHICLES_INSPECTED', 'Total Tyres Inspected vs Vehicles Inspected', '-', 8, 'DASHBOARD_DIGISPECT_SUMMARY_CHART'),
    ('NO_OF_CUSTOMER_INSPECTED_BY_NO_OF_INSPECTIONS', 'Number of Customers Inspected by Number of Inspections', '-', 9, 'DASHBOARD_DIGISPECT_SUMMARY_CHART'),
    ('TOP_5_MOST_INSPECTED_CUSTOMER', 'Top 5 Most Inspected Customer', '-', 10, 'DASHBOARD_DIGISPECT_SUMMARY_CHART'),
    ('TOTAL_INSPECTIONS_BY_INSPECTORS_OVER_TIME', 'Total Inspections by Inspectors Over Time', '-', 11, 'DASHBOARD_DIGISPECT_SUMMARY_CHART'),
    ('TOP_5_TYRE_BRANDS_BY_SIZE_DISTRIBUTION', 'Top 5 Tyre Brands by Size Distribution', '-', 12, 'DASHBOARD_DIGISPECT_SUMMARY_CHART'),
    ('TOP_5_VEHICLE_BRANDS', 'Top 5 Vehicle Brands', '-', 13, 'DASHBOARD_DIGISPECT_SUMMARY_CHART'),
    ('INSPECTIONS_LOCATION', 'Inspections Location', '-', 14, 'DASHBOARD_DIGISPECT_SUMMARY_CHART')
ON CONFLICT (code) DO NOTHING;
COMMIT;
