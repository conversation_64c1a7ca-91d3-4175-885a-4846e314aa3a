CREATE TABLE IF NOT EXISTS "ams_custom_asset_categories" (
    id VARCHAR(40) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(50) NOT NULL,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL
);

CREATE TABLE IF NOT EXISTS "ams_custom_asset_sub_categories" (
    id VARCHAR(40) PRIMARY KEY,
    custom_asset_category_id VARCHAR(40) REFERENCES ams_custom_asset_categories(id),
    name VARCHAR(100) NOT NULL,
    description VARCHAR(50) NOT NULL,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by <PERSON><PERSON><PERSON><PERSON>(40) NOT NULL
);

ALTER TABLE "ams_assets" 
ADD COLUMN IF NOT EXISTS custom_asset_category_id VARCHAR(40),
ADD COLUMN IF NOT EXISTS custom_asset_sub_category_id VARCHAR(40);

ALTER TABLE ams_custom_asset_categories
ADD COLUMN asset_category_code VARCHAR(40),
ADD CONSTRAINT fk_asset_category_code
FOREIGN KEY (asset_category_code) REFERENCES "ams_ASSET_CATEGORIES"(code);

INSERT INTO "ams_ASSET_CATEGORIES"
(code, "label", "description", is_general)
VALUES('OTHERS', 'Others', '-', true);

INSERT INTO "ams_ASSET_CATEGORIES"
(code, "label", "description", is_general)
VALUES ('OTHERS', 'Others', '-', true), ('EQUIPMENT', 'EQUIPMENT', '-', true);