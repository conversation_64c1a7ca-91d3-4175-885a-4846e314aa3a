-- ams_custom_asset_categories
CREATE TABLE IF NOT EXISTS "ams_CUSTOM_ASSET_CATEGORY_STATUSES" (
        "code" character varying(20) NOT NULL,
        "label" character varying(20) NOT NULL,
        "description" character varying(50) NOT NULL,
        PRIMARY KEY ("code")
);

INSERT INTO "ams_CUSTOM_ASSET_CATEGORY_STATUSES" VALUES 
    ('ACTIVE', 'Active', '-'),
    ('INACTIVE', 'Inactive', '-')
    ON CONFLICT ("code") DO NOTHING;

ALTER TABLE "ams_custom_asset_categories"
    ADD COLUMN IF NOT EXISTS "status_code" character varying(20),
    ADD CONSTRAINT fk_ams_custom_asset_categories_status 
    FOREIGN KEY ("status_code") 
    REFERENCES "ams_CUSTOM_ASSET_CATEGORY_STATUSES"("code");


	
-- ams_custom_asset_sub_categories

CREATE TABLE IF NOT EXISTS "ams_CUSTOM_ASSET_SUB_CATEGORY_STATUSES" (
        "code" character varying(20) NOT NULL,
        "label" character varying(20) NOT NULL,
        "description" character varying(50) NOT NULL,
        PRIMARY KEY ("code")
);

INSERT INTO "ams_CUSTOM_ASSET_SUB_CATEGORY_STATUSES" VALUES 
    ('ACTIVE', 'Active', '-'),
    ('INACTIVE', 'Inactive', '-')
    ON CONFLICT ("code") DO NOTHING;

ALTER TABLE "ams_custom_asset_sub_categories"
    ADD COLUMN IF NOT EXISTS "status_code" character varying(20),
    ADD CONSTRAINT fk_ams_custom_asset_sub_categories_status 
    FOREIGN KEY ("status_code") 
    REFERENCES "ams_CUSTOM_ASSET_SUB_CATEGORY_STATUSES"("code");

-- ams_ASSET_MODELS_STATUSES

CREATE TABLE IF NOT EXISTS "ams_ASSET_MODELS_STATUSES" (
        "code" character varying(20) NOT NULL,
        "label" character varying(20) NOT NULL,
        "description" character varying(50) NOT NULL,
        PRIMARY KEY ("code")
);

INSERT INTO "ams_ASSET_MODELS_STATUSES" VALUES 
    ('ACTIVE', 'Active', '-'),
    ('INACTIVE', 'Inactive', '-')
    ON CONFLICT ("code") DO NOTHING;

ALTER TABLE "ams_asset_models"
    ADD COLUMN IF NOT EXISTS "status_code" character varying(20),
    ADD CONSTRAINT fk_ams_asset_models_status 
    FOREIGN KEY ("status_code") 
    REFERENCES "ams_ASSET_MODELS_STATUSES"("code");