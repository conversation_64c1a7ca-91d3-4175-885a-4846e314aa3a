-- Create the "nts_NOTIFICATION_TYPES" table
CREATE TABLE
    IF NOT EXISTS "nts_NOTIFICATION_REFERENCES" (
        code VARCHAR(50) PRIMARY KEY,
        label VARCHAR(50) NOT NULL,
        description TEXT
    );

INSERT INTO
    "nts_NOTIFICATION_REFERENCES" (code, "label", description)
VALUES
    ('ASSET', 'Asset', '-'),
    ('WORK_ORDER', 'Work Order', '-'),
    ('INSPECTION', 'Inspection', '-'),
    ('APPROVAL', 'Approval', '-')
    ON CONFLICT (code) DO NOTHING;

ALTER TABLE nts_notifications
ADD COLUMN IF NOT EXISTS push_notif_message_header VARCHAR(255),
ADD COLUMN IF NOT EXISTS push_notif_message_body VARCHAR(255),
ADD COLUMN IF NOT EXISTS reference_code VARCHAR(50) REFERENCES "nts_NOTIFICATION_REFERENCES" (code),
ADD COLUMN IF NOT EXISTS reference_value VARCHAR(40);


UPDATE "nts_notifications"
SET
    push_notif_message_header = message_header
WHERE
    message_header != '' AND message_header IS NOT NULL;