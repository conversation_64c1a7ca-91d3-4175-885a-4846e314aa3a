BEGIN;

ALTER TABLE ams_asset_tyres
ADD COLUMN IF NOT EXISTS average_rtd_last_updated_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS pressure NUMERIC(14, 2),
ADD COLUMN IF NOT EXISTS pressure_last_updated_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS temperature DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS temperature_last_updated_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS pressure_last_updated_sensor_ref VARCHAR(255),
ADD COLUMN IF NOT EXISTS temperature_last_updated_sensor_ref VARCHAR(255);


WITH
    cte AS (
        SELECT
            *
        FROM
            (
                SELECT
                    ROW_NUMBER() OVER (
                        PARTITION BY
                            asset_tyre_id
                        ORDER BY
                            created_at
                    ) AS rn,
                    *
                FROM
                    ams_asset_inspection_tyre aait
                WHERE
                    pressure > 0 AND pressure IS NOT NULL
            ) T
        WHERE
            rn = 1
    )
UPDATE ams_asset_tyres aat
SET
    pressure_last_updated_at = NOW (),
    pressure = cte.pressure,
    pressure_last_updated_sensor_ref = cte.pressure_sensor_ref
FROM
    cte
WHERE
    cte.asset_tyre_id = aat.asset_id;

WITH
    cte AS (
        SELECT
            *
        FROM
            (
                SELECT
                    ROW_NUMBER() OVER (
                        PARTITION BY
                            asset_tyre_id
                        ORDER BY
                            created_at
                    ) AS rn,
                    *
                FROM
                    ams_asset_inspection_tyre aait
                WHERE
                    temperature IS NOT NULL
            ) T
        WHERE
            rn = 1
    )
UPDATE ams_asset_tyres aat
SET
    temperature_last_updated_at = NOW (),
    temperature = cte.temperature,
    temperature_last_updated_sensor_ref = cte.temperature_sensor_ref
FROM
    cte
WHERE
    cte.asset_tyre_id = aat.asset_id;

COMMIT;