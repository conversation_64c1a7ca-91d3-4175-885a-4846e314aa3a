DROP VIEW "app_approval_requests_views";

ALTER TABLE "app_approval_requests"
	ALTER COLUMN reference_ids TYPE VARCHAR(255) USING COALESCE(reference_ids[1],'');

CREATE VIEW "app_approval_requests_views" AS 
    SELECT ((('AP-'::text || to_char(created_at, 'YYMMDD'::text)) || '-'::text) || lpad((row_number() OVER (PARTITION BY client_id, (date_trunc('day'::text, created_at)) ORDER BY created_at))::text, 4, '0'::text)) AS approval_number,
        id,
        source_code,
        reference_ids,
        description,
        request_notes,
        status_code,
        client_id,
        created_at,
        updated_at,
        deleted_at,
        created_by,
        updated_by
    FROM app_approval_requests aar;

ALTER TABLE "app_approval_requests"
    RENAME COLUMN reference_ids TO reference_id;
