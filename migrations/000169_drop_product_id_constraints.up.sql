BEGIN;
ALTER TABLE inv_order_items DROP CONSTRAINT IF EXISTS inv_order_items_product_id_fkey; 
ALTER TABLE inv_memberships_benefits DROP CONSTRAINT IF EXISTS inv_memberships_benefits_membership_id_fkey; 

ALTER TABLE inv_orders 
ADD COLUMN IF NOT EXISTS partner_no VARCHAR(255),
ADD COLUMN IF NOT EXISTS external_id VARCHAR(40),
ADD COLUMN IF NOT EXISTS external_no VARCHAR(255),
ADD COLUMN IF NOT EXISTS external_source_code VARCHAR(255);

ALTER TABLE inv_order_items 
ADD COLUMN IF NOT EXISTS product_no VARCHAR(100),
ADD COLUMN IF NOT EXISTS external_id VARCHAR(40),
ADD COLUMN IF NOT EXISTS external_no VARCHAR(255),
ADD COLUMN IF NOT EXISTS external_source_code VARCHAR(255);

INSERT INTO
    "ctn_FORM_FIELD_TYPES" (code, "label", description)
VALUES
    ('INSPECTION_CHECK', 'Inspection Check', 'Inspection Check'),
    ('SECTION_HEADER', 'Section Header', 'Section Header')
ON CONFLICT (code) DO NOTHING;

COMMIT;