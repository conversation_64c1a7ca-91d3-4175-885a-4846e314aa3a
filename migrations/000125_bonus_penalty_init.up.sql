begin;

CREATE TABLE
    IF NOT EXISTS "ams_BONUS_PENALTY_STATUSES" (
        code VARCHAR(20) PRIMARY KEY,
        label VARCHAR(20) NOT NULL,
        description VARCHAR(50) NOT NULL
    );

INSERT INTO
    "ams_BONUS_PENALTY_STATUSES" (code, "label", description)
VALUES
    ('PENDING', 'Pending', '-'),
    ('REJECTED', 'Rejected', '-'),
    ('APPROVED', 'Approved', '-') ON CONFLICT (code) DO NOTHING;
   
CREATE TABLE
    IF NOT EXISTS ams_bonus_penalty_target_formations (
        id VARCHAR(40) PRIMARY KEY,
        name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
        description TEXT NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by <PERSON><PERSON><PERSON><PERSON>(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );
 
CREATE INDEX "idx_ams_bonus_penalty_target_formations_deleted_at" ON "ams_bonus_penalty_target_formations" ("deleted_at"); 

CREATE TABLE
    IF NOT EXISTS ams_bonus_penalty_parameters (
        id VARCHAR(40) PRIMARY KEY,
        tyre_id VARCHAR(40) REFERENCES "ams_tyres" (id) NOT NULL,
        target_formation_id VARCHAR(40) REFERENCES "ams_bonus_penalty_target_formations" (id) NOT NULL,
        target_km BIGINT NOT NULL,
        rate_per_km BIGINT NOT NULL,
        max_achievment_km BIGINT NOT NULL,
        client_id VARCHAR(40) NOT NULL,
        created_at TIMESTAMPTZ NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL,
        deleted_at TIMESTAMPTZ,
        updated_by VARCHAR(40) NOT NULL,
        created_by VARCHAR(40) NOT NULL
    );
 
CREATE INDEX "idx_ams_bonus_penalty_parameters_deleted_at" ON "ams_bonus_penalty_parameters" ("deleted_at"); 

INSERT INTO
    "sts_PRODUCT_UOMS" (code, "label", description)
VALUES
    ('BOX', 'Box', '-'),
    ('TON', 'Ton', '-') ON CONFLICT (code) DO NOTHING; 

commit;