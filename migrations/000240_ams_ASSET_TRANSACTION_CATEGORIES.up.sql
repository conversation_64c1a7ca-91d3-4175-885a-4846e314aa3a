BEGIN;

CREATE TABLE IF NOT EXISTS
    "ams_ASSET_TRANSACTION_CATEGORIES" (
        "code" varchar(20) NOT NULL PRIMARY KEY ,
        "label" VARCHAR(20) NOT NULL,
        "description" varchar(40)
    );

INSERT INTO "ams_ASSET_TRANSACTION_CATEGORIES" (code,label) 
VALUES 
    ('FUEL','Fuel'),
    ('MAINTENANCE','Maintenance') ON CONFLICT (code) DO NOTHING;

ALTER TABLE "ams_asset_transactions"
ADD COLUMN IF NOT EXISTS "category_code" VARCHAR(20),
ADD COLUMN IF NOT EXISTS "location" VARCHAR(50),
ADD COLUMN IF NOT EXISTS "odometer" INTEGER,
ADD COLUMN IF NOT EXISTS "payment_method_code" VARCHAR(20);

ALTER TABLE "ams_asset_transactions"
ADD CONSTRAINT "fk_ams_asset_transactions_category" FOREIGN KEY ("category_code") 
    REFERENCES "ams_ASSET_TRANSACTION_CATEGORIES" ("code") ON UPDATE NO ACTION ON DELETE NO ACTION,
ADD CONSTRAINT "fk_ams_asset_transactions_payment_method" FOREIGN KEY ("payment_method_code") 
    REFERENCES "ams_ASSET_TRANSACTION_PAYMENT_METHODS" ("code") ON UPDATE NO ACTION ON DELETE NO ACTION;

COMMIT;
