BEGIN;

-- Revert the insertion into "ins_ALERT_ACTION_TYPES"
DELETE FROM "ins_ALERT_ACTION_TYPES" WHERE code = 'WEB_MOBILE_APP_NOTIFICATION';

-- Drop the tables in reverse order of their dependencies
DROP TABLE IF EXISTS "ins_alerts_v2";
DROP TABLE IF EXISTS "ins_alert_config_conditions";
DROP TABLE IF EXISTS "ins_alert_configs_v2";
DROP TABLE IF EXISTS "ins_ALERT_PARAMETERS";
DROP TABLE IF EXISTS "ins_ALERT_CONDITION_OPERATORS";
DROP TABLE IF EXISTS "ins_ALERT_CONDITION_TYPES";
DROP TABLE IF EXISTS "ins_ALERT_PARAMETER_TYPES";
DROP TABLE IF EXISTS "ins_ALERT_PARAMETER_DATA_TYPES";

COMMIT;