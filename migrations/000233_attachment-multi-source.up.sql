BEGIN;

CREATE TABLE
    IF NOT EXISTS "sts_attachment_sources" (
        id VARCHAR(40) PRIMARY KEY,
        "attachment_id" VARCHAR(40) REFERENCES sts_attachments (id),
        "reference_code" VARCHAR(255),
        "source_reference_id" VARCHAR(40),
        "target_reference_id" VARCHAR(40)
    );

ALTER TABLE "sts_attachments"
ALTER COLUMN reference_code
DROP NOT NULL,
ALTER COLUMN source_reference_id
DROP NOT NULL;

INSERT INTO
    sts_attachment_sources (
        id,
        attachment_id,
        reference_code,
        source_reference_id,
        target_reference_id
    )
SELECT
    'sas_' || uuid_generate_v4 (),
    id,
    reference_code,
    source_reference_id,
    target_reference_id
FROM
    sts_attachments;

COMMIT;