ALTER TABLE "tks_TICKET_CATEGORIES"
    ADD COLUMN IF NOT EXISTS status_code varchar(10);

CREATE TABLE IF NOT EXISTS "tks_TICKET_CATEGORY_asset_category_mapping" (
    category_code character varying(40) NOT NULL REFERENCES "tks_TICKET_CATEGORIES" (code),
    asset_category_code character varying(40) NOT NULL REFERENCES "ams_ASSET_CATEGORIES" (code)
);

ALTER TABLE "tks_TICKET_CATEGORY_asset_category_mapping" REPLICA IDENTITY FULL;


CREATE TABLE IF NOT EXISTS "tks_TICKET_CATEGORY_custom_asset_category_mapping" (
    category_code character varying(40) NOT NULL REFERENCES "tks_TICKET_CATEGORIES" (code),
    custom_asset_category_id character varying(40) NOT NULL REFERENCES "ams_custom_asset_categories" (id)
);

ALTER TABLE "tks_TICKET_CATEGORY_custom_asset_category_mapping" REPLICA IDENTITY FULL;

CREATE TABLE IF NOT EXISTS "tks_TICKET_CATEGORY_STATUS"(
    "code" character varying(20) NOT NULL,
    "label" character varying(20) NOT NULL,
    "description" character varying(50) NOT NULL,
    PRIMARY KEY ("code")
);

INSERT INTO
    "tks_TICKET_CATEGORY_STATUS" (code, label, description)
VALUES
    (
        'ACTIVE',
        'Active',
        'Work Order Subject is Active'
    ),
    (
        'INACTIVE',
        'Inactive',
        'Work Order Subject is Inactive'
    ) ON CONFLICT (code) DO NOTHING;

UPDATE "tks_TICKET_CATEGORIES" SET "status_code" = 'ACTIVE';