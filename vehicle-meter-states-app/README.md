# Vehicle Meter States Manager

A Vue.js application for managing vehicle meter states with list, detail, and delete functionality.

## Features

- 📋 **Vehicle List**: Browse all available vehicles
- 📊 **Meter States List**: View meter states history for each vehicle
- 🔍 **Meter State Detail**: View detailed information about specific meter states
- 🗑️ **Delete Functionality**: Delete meter states with confirmation
- 📱 **Responsive Design**: Works on desktop and mobile devices
- 🎨 **Modern UI**: Bootstrap-based interface with FontAwesome icons

## API Endpoints

The application consumes the following API endpoints:

- `GET /v1/asset-vehicles` - Get list of vehicles
- `GET /v1/asset-vehicles/:id/meter-states` - Get meter states for a specific vehicle
- `GET /v1/asset-vehicle-meter-states/:id` - Get specific meter state detail
- `DELETE /v1/asset-vehicle-meter-states/:id` - Delete a meter state

## Prerequisites

- Node.js (version 16 or higher)
- npm or yarn package manager
- Backend API server running on port 8080 (configurable)

## Installation

1. **Clone or download the project**
   ```bash
   cd vehicle-meter-states-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure API endpoint** (if needed)
   Edit `vite.config.js` to change the backend URL:
   ```javascript
   proxy: {
     '/v1': {
       target: 'http://your-backend-url:8000', // Change this
       changeOrigin: true,
       secure: false
     }
   }
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Open in browser**
   Navigate to `http://localhost:3000`

## Build for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## Project Structure

```
vehicle-meter-states-app/
├── src/
│   ├── components/
│   │   ├── VehicleList.vue          # Vehicle listing page
│   │   ├── MeterStatesList.vue      # Meter states list for a vehicle
│   │   └── MeterStateDetail.vue     # Meter state detail with delete
│   ├── services/
│   │   └── api.js                   # API service layer
│   ├── App.vue                      # Main app component
│   └── main.js                      # App entry point
├── package.json
├── vite.config.js                   # Vite configuration
└── README.md
```

## Usage

### 1. Vehicle List
- View all available vehicles
- Click "View Meter States" to see meter states for a specific vehicle

### 2. Meter States List
- View all meter states for a selected vehicle
- Sorted by date (newest first)
- Click the eye icon to view detailed information

### 3. Meter State Detail
- View comprehensive information about a meter state
- See meter readings (KM and HM)
- View metadata (created/updated dates and users)
- Delete the meter state with confirmation

### 4. Delete Functionality
- Click the "Delete Meter State" button in the detail view
- Confirm deletion in the modal dialog
- Automatically redirects back to the meter states list

## API Response Format

The application expects the following response formats:

### Vehicles List Response
```json
{
  "data": [
    {
      "id": "vehicle_id",
      "reference_number": "VH001",
      "registration_number": "ABC123",
      "serial_number": "SN123456",
      "brand_name": "Toyota",
      "model_name": "Hilux",
      "current_km": 50000,
      "current_hm": 2500
    }
  ]
}
```

### Meter States List Response
```json
{
  "data": [
    {
      "id": "meter_state_id",
      "asset_id": "vehicle_id",
      "vehicle_km": 50000,
      "vehicle_hm": 2500,
      "date_time": "2024-01-15T10:30:00Z",
      "created_by": "user_name",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### Meter State Detail Response
```json
{
  "data": {
    "id": "meter_state_id",
    "asset_id": "vehicle_id",
    "vehicle_km": 50000,
    "vehicle_hm": 2500,
    "date_time": "2024-01-15T10:30:00Z",
    "axle_configuration": {},
    "created_by": "user_name",
    "updated_by": "user_name",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

## Authentication

The application supports token-based authentication. If you have an auth token, store it in localStorage:

```javascript
localStorage.setItem('authToken', 'your-jwt-token')
```

The token will be automatically included in API requests.

## Customization

### Styling
The application uses Bootstrap 5 for styling. You can customize the appearance by:
- Modifying the CSS in component `<style>` sections
- Adding custom CSS classes
- Changing Bootstrap variables

### API Configuration
- Modify `src/services/api.js` to change API endpoints or add new methods
- Update the proxy configuration in `vite.config.js` for different backend URLs

## Troubleshooting

### Common Issues

1. **API Connection Issues**
   - Check that your backend server is running
   - Verify the proxy configuration in `vite.config.js`
   - Check browser console for CORS errors

2. **Authentication Issues**
   - Ensure your auth token is valid and stored in localStorage
   - Check that the backend accepts the token format

3. **Build Issues**
   - Clear node_modules and reinstall: `rm -rf node_modules && npm install`
   - Check Node.js version compatibility

### Development Tips

- Use browser developer tools to inspect API calls
- Check the Network tab for API response details
- Use Vue DevTools browser extension for component debugging

## License

This project is for demonstration purposes. Adjust licensing as needed for your use case.
