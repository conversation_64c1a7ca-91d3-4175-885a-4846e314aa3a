<template>
  <div id="app">
    <!-- Token Input Overlay -->
    <TokenInput
      @authenticated="handleAuthenticated"
      @logout="handleLogout"
      v-if="!isAuthenticated"
    />

    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary" v-if="isAuthenticated">
      <div class="container">
        <router-link class="navbar-brand" to="/">
          <i class="fas fa-truck me-2"></i>
          Vehicle Meter States Manager
        </router-link>

        <div class="navbar-nav ms-auto">
          <router-link class="nav-link" to="/vehicles">
            <i class="fas fa-list me-1"></i>
            Vehicles
          </router-link>
          <button class="btn btn-outline-light btn-sm ms-2" @click="logout">
            <i class="fas fa-sign-out-alt me-1"></i>
            Logout
          </button>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4" v-if="isAuthenticated">
      <!-- Breadcrumb Navigation -->
      <nav aria-label="breadcrumb" v-if="showBreadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item">
            <router-link to="/vehicles">
              <i class="fas fa-home me-1"></i>
              Vehicles
            </router-link>
          </li>
          <li class="breadcrumb-item" v-if="$route.name === 'MeterStatesList'">
            <span>Vehicle {{ $route.params.vehicleId }} Meter States</span>
          </li>
          <li class="breadcrumb-item" v-if="$route.name === 'MeterStateDetail'">
            <span>Meter State Detail</span>
          </li>
        </ol>
      </nav>

      <!-- Router View -->
      <router-view />
    </div>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
      <div class="container">
        <small>&copy; 2024 Vehicle Meter States Manager. Built with Vue.js</small>
      </div>
    </footer>
  </div>
</template>

<script>
import TokenInput from './components/TokenInput.vue'

export default {
  name: 'App',
  components: {
    TokenInput
  },
  data() {
    return {
      isAuthenticated: false
    }
  },
  computed: {
    showBreadcrumb() {
      return this.$route.name !== 'VehicleList'
    }
  },
  mounted() {
    // Check if user is already authenticated
    this.checkAuthentication()
  },
  methods: {
    checkAuthentication() {
      const token = localStorage.getItem('authToken')
      this.isAuthenticated = !!token
    },

    handleAuthenticated() {
      this.isAuthenticated = true
    },

    handleLogout() {
      this.isAuthenticated = false
    },

    logout() {
      localStorage.removeItem('authToken')
      this.isAuthenticated = false
      // Redirect to vehicles page
      this.$router.push('/vehicles')
    }
  }
}
</script>

<style>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  flex: 1;
}

.breadcrumb {
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  padding: 0.75rem 1rem;
}

.breadcrumb-item a {
  text-decoration: none;
  color: #0d6efd;
}

.breadcrumb-item a:hover {
  text-decoration: underline;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.btn {
  border-radius: 0.375rem;
}

.table {
  margin-bottom: 0;
}

.navbar-brand {
  font-weight: 600;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.alert {
  border-radius: 0.375rem;
}
</style>
