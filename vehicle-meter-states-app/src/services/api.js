import axios from 'axios'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: '/v1', // This will be proxied to your backend
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor to add auth token if available
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken')
      // Optionally redirect to login
    }
    return Promise.reject(error)
  }
)

// API service methods
export const apiService = {
  // Get list of vehicles (for vehicle selection)
  async getVehicles() {
    try {
      const response = await api.get('/asset-vehicles')
      return response.data
    } catch (error) {
      console.error('Error fetching vehicles:', error)
      throw error
    }
  },

  // Get meter states for a specific vehicle
  async getVehicleMeterStates(vehicleId) {
    try {
      const response = await api.get(`/asset-vehicles/${vehicleId}/meter-states`)
      return response.data
    } catch (error) {
      console.error('Error fetching vehicle meter states:', error)
      throw error
    }
  },

  // Get specific meter state detail
  async getMeterStateDetail(meterStateId) {
    try {
      const response = await api.get(`/asset-vehicle-meter-states/${meterStateId}`)
      return response.data
    } catch (error) {
      console.error('Error fetching meter state detail:', error)
      throw error
    }
  },

  // Delete meter state
  async deleteMeterState(meterStateId) {
    try {
      const response = await api.delete(`/asset-vehicle-meter-states/${meterStateId}`)
      return response.data
    } catch (error) {
      console.error('Error deleting meter state:', error)
      throw error
    }
  },

  // Create new meter state
  async createMeterState(vehicleId, meterStateData) {
    try {
      const response = await api.post(`/asset-vehicles/${vehicleId}/meter-states`, meterStateData)
      return response.data
    } catch (error) {
      console.error('Error creating meter state:', error)
      throw error
    }
  },

  // Update meter state
  async updateMeterState(meterStateId, meterStateData) {
    try {
      const response = await api.put(`/asset-vehicle-meter-states/${meterStateId}`, meterStateData)
      return response.data
    } catch (error) {
      console.error('Error updating meter state:', error)
      throw error
    }
  }
}

export default api
