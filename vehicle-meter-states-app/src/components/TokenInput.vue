<template>
  <div class="token-input-overlay" v-if="!isAuthenticated">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
          <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
              <h4 class="mb-0">
                <i class="fas fa-key me-2"></i>
                Authentication Required
              </h4>
            </div>
            <div class="card-body">
              <form @submit.prevent="setToken">
                <div class="mb-3">
                  <label for="bearerToken" class="form-label">
                    <i class="fas fa-shield-alt me-1"></i>
                    Bearer <PERSON>ken
                  </label>
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-lock"></i>
                    </span>
                    <input
                      type="password"
                      class="form-control"
                      id="bearerToken"
                      v-model="token"
                      placeholder="Enter your bearer token"
                      required
                      :class="{ 'is-invalid': error }"
                    />
                  </div>
                  <div v-if="error" class="invalid-feedback d-block">
                    {{ error }}
                  </div>
                  <small class="form-text text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    This token will be used for all API requests
                  </small>
                </div>
                
                <div class="d-grid">
                  <button 
                    type="submit" 
                    class="btn btn-primary"
                    :disabled="!token.trim() || loading"
                  >
                    <span v-if="loading">
                      <i class="fas fa-spinner fa-spin me-2"></i>
                      Validating...
                    </span>
                    <span v-else>
                      <i class="fas fa-sign-in-alt me-2"></i>
                      Continue
                    </span>
                  </button>
                </div>
              </form>
              
              <!-- Sample token for testing -->
              <div class="mt-3 p-3 bg-light rounded">
                <small class="text-muted">
                  <strong>For testing:</strong> You can use any token format like:
                  <br>
                  <code>eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...</code>
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { apiService } from '../services/api.js'

export default {
  name: 'TokenInput',
  data() {
    return {
      token: '',
      error: null,
      loading: false
    }
  },
  computed: {
    isAuthenticated() {
      return !!localStorage.getItem('authToken')
    }
  },
  mounted() {
    // Check if token already exists
    const existingToken = localStorage.getItem('authToken')
    if (existingToken) {
      this.$emit('authenticated')
    }
  },
  methods: {
    async setToken() {
      if (!this.token.trim()) {
        this.error = 'Please enter a bearer token'
        return
      }

      try {
        this.loading = true
        this.error = null

        // Store the token
        localStorage.setItem('authToken', this.token.trim())
        
        // Test the token by making a simple API call
        await apiService.getVehicles()
        
        // If successful, emit authenticated event
        this.$emit('authenticated')
        
      } catch (error) {
        this.error = 'Invalid token or unable to connect to server'
        localStorage.removeItem('authToken')
        console.error('Authentication error:', error)
      } finally {
        this.loading = false
      }
    },
    
    logout() {
      localStorage.removeItem('authToken')
      this.token = ''
      this.error = null
      this.$emit('logout')
    }
  }
}
</script>

<style scoped>
.token-input-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 1rem;
}

.card-header {
  border-radius: 1rem 1rem 0 0 !important;
  padding: 1.5rem;
}

.card-body {
  padding: 2rem;
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn-primary {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
}

.bg-light {
  background-color: #f8f9fa !important;
}

code {
  background-color: #e9ecef;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  word-break: break-all;
}

.invalid-feedback {
  font-size: 0.875rem;
}

.fa-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
