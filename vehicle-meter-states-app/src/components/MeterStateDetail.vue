<template>
  <div class="meter-state-detail">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2>
        <i class="fas fa-info-circle me-2"></i>
        Meter State Detail
      </h2>
      <div class="btn-group">
        <button @click="goBack" class="btn btn-outline-secondary">
          <i class="fas fa-arrow-left me-2"></i>
          Back
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-spinner">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="alert alert-danger" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{ error }}
      <button @click="fetchMeterStateDetail" class="btn btn-sm btn-outline-danger ms-2">
        <i class="fas fa-redo me-1"></i>
        Retry
      </button>
    </div>

    <!-- Meter State Detail -->
    <div v-else-if="meterState" class="row">
      <div class="col-lg-8">
        <!-- Main Information Card -->
        <div class="card mb-4">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
              <i class="fas fa-chart-line me-2"></i>
              Meter State Information
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label text-muted">
                  <i class="fas fa-id-card me-1"></i>
                  Meter State ID
                </label>
                <div class="fw-semibold">{{ meterState.id }}</div>
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label text-muted">
                  <i class="fas fa-truck me-1"></i>
                  Vehicle ID
                </label>
                <div class="fw-semibold">{{ meterState.asset_id }}</div>
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label text-muted">
                  <i class="fas fa-calendar me-1"></i>
                  Date & Time
                </label>
                <div class="fw-semibold">
                  {{ formatDateTime(meterState.date_time) }}
                </div>
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label text-muted">
                  <i class="fas fa-user me-1"></i>
                  Created By
                </label>
                <div class="fw-semibold">{{ meterState.created_by || 'System' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Meter Readings Card -->
        <div class="card mb-4">
          <div class="card-header bg-info text-white">
            <h5 class="mb-0">
              <i class="fas fa-tachometer-alt me-2"></i>
              Meter Readings
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="text-center p-4 bg-light rounded">
                  <i class="fas fa-road fa-2x text-primary mb-2"></i>
                  <h4 class="text-primary mb-1">{{ formatNumber(meterState.vehicle_km) }}</h4>
                  <small class="text-muted">Kilometers</small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="text-center p-4 bg-light rounded">
                  <i class="fas fa-clock fa-2x text-success mb-2"></i>
                  <h4 class="text-success mb-1">{{ formatNumber(meterState.vehicle_hm) }}</h4>
                  <small class="text-muted">Hours</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Axle Configuration Card (if available) -->
        <div v-if="meterState.axle_configuration" class="card mb-4">
          <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">
              <i class="fas fa-cogs me-2"></i>
              Axle Configuration
            </h5>
          </div>
          <div class="card-body">
            <pre class="bg-light p-3 rounded">{{ formatJSON(meterState.axle_configuration) }}</pre>
          </div>
        </div>
      </div>

      <!-- Actions Sidebar -->
      <div class="col-lg-4">
        <div class="card">
          <div class="card-header bg-warning text-dark">
            <h5 class="mb-0">
              <i class="fas fa-tools me-2"></i>
              Actions
            </h5>
          </div>
          <div class="card-body">
            <!-- Delete Button -->
            <button 
              @click="showDeleteConfirmation = true" 
              class="btn btn-danger w-100 mb-3"
              :disabled="deleting"
            >
              <i class="fas fa-trash me-2"></i>
              <span v-if="deleting">Deleting...</span>
              <span v-else>Delete Meter State</span>
            </button>

            <!-- Metadata -->
            <div class="border-top pt-3">
              <h6 class="text-muted mb-3">Metadata</h6>
              
              <div class="mb-2">
                <small class="text-muted d-block">Created At</small>
                <small class="fw-semibold">{{ formatDateTime(meterState.created_at) }}</small>
              </div>
              
              <div class="mb-2">
                <small class="text-muted d-block">Updated At</small>
                <small class="fw-semibold">{{ formatDateTime(meterState.updated_at) }}</small>
              </div>
              
              <div v-if="meterState.updated_by" class="mb-2">
                <small class="text-muted d-block">Updated By</small>
                <small class="fw-semibold">{{ meterState.updated_by }}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div 
      v-if="showDeleteConfirmation" 
      class="modal fade show d-block" 
      tabindex="-1" 
      style="background-color: rgba(0,0,0,0.5);"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-exclamation-triangle text-warning me-2"></i>
              Confirm Deletion
            </h5>
            <button 
              type="button" 
              class="btn-close" 
              @click="showDeleteConfirmation = false"
            ></button>
          </div>
          <div class="modal-body">
            <p>Are you sure you want to delete this meter state record?</p>
            <div class="alert alert-warning">
              <i class="fas fa-info-circle me-2"></i>
              <strong>This action cannot be undone.</strong>
            </div>
            <div class="bg-light p-3 rounded">
              <strong>Meter State ID:</strong> {{ meterState.id }}<br>
              <strong>Date:</strong> {{ formatDateTime(meterState.date_time) }}<br>
              <strong>Vehicle KM:</strong> {{ formatNumber(meterState.vehicle_km) }}<br>
              <strong>Vehicle HM:</strong> {{ formatNumber(meterState.vehicle_hm) }}
            </div>
          </div>
          <div class="modal-footer">
            <button 
              type="button" 
              class="btn btn-secondary" 
              @click="showDeleteConfirmation = false"
            >
              Cancel
            </button>
            <button 
              type="button" 
              class="btn btn-danger" 
              @click="deleteMeterState"
              :disabled="deleting"
            >
              <i class="fas fa-trash me-2"></i>
              <span v-if="deleting">Deleting...</span>
              <span v-else>Delete</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { apiService } from '../services/api.js'

export default {
  name: 'MeterStateDetail',
  props: {
    id: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      meterState: null,
      loading: true,
      error: null,
      showDeleteConfirmation: false,
      deleting: false
    }
  },
  async mounted() {
    await this.fetchMeterStateDetail()
  },
  watch: {
    id: {
      immediate: true,
      async handler() {
        await this.fetchMeterStateDetail()
      }
    }
  },
  methods: {
    async fetchMeterStateDetail() {
      try {
        this.loading = true
        this.error = null
        
        const response = await apiService.getMeterStateDetail(this.id)
        this.meterState = response.data || response
      } catch (error) {
        this.error = 'Failed to load meter state details. Please try again later.'
        console.error('Error fetching meter state detail:', error)
      } finally {
        this.loading = false
      }
    },
    
    async deleteMeterState() {
      try {
        this.deleting = true
        
        await apiService.deleteMeterState(this.id)
        
        // Show success message and navigate back
        this.$router.push({ 
          name: 'MeterStatesList', 
          params: { vehicleId: this.meterState.asset_id } 
        })
        
        // You could also show a toast notification here
        alert('Meter state deleted successfully!')
        
      } catch (error) {
        console.error('Error deleting meter state:', error)
        alert('Failed to delete meter state. Please try again.')
      } finally {
        this.deleting = false
        this.showDeleteConfirmation = false
      }
    },
    
    goBack() {
      if (this.meterState && this.meterState.asset_id) {
        this.$router.push({ 
          name: 'MeterStatesList', 
          params: { vehicleId: this.meterState.asset_id } 
        })
      } else {
        this.$router.go(-1)
      }
    },
    
    formatDateTime(dateString) {
      if (!dateString) return 'N/A'
      const date = new Date(dateString)
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    
    formatNumber(value) {
      if (value === null || value === undefined) return 'N/A'
      return new Intl.NumberFormat().format(value)
    },
    
    formatJSON(obj) {
      if (!obj) return 'N/A'
      try {
        return JSON.stringify(obj, null, 2)
      } catch (e) {
        return String(obj)
      }
    }
  }
}
</script>

<style scoped>
.meter-state-detail {
  min-height: 400px;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.form-label {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.fw-semibold {
  font-weight: 600;
}

.loading-spinner {
  min-height: 300px;
}

.modal.show {
  display: block !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

pre {
  font-size: 0.875rem;
  max-height: 200px;
  overflow-y: auto;
}

.btn-danger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.text-center h4 {
  font-weight: 700;
}
</style>
