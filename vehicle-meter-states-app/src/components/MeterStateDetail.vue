<template>
  <div class="meter-state-detail">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2>
        <i class="fas fa-info-circle me-2"></i>
        Meter State Detail
      </h2>
      <div class="btn-group">
        <button @click="goBack" class="btn btn-outline-secondary">
          <i class="fas fa-arrow-left me-2"></i>
          Back
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-spinner">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="alert alert-danger" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{ error }}
      <button @click="fetchMeterStateDetail" class="btn btn-sm btn-outline-danger ms-2">
        <i class="fas fa-redo me-1"></i>
        Retry
      </button>
    </div>

    <!-- Meter State Detail -->
    <div v-else-if="meterState" class="row">
      <div class="col-lg-6">
        <!-- Axle Configuration Card -->
        <div class="card mb-4">
          <div class="card-header bg-light">
            <h5 class="mb-0 text-dark">
              <i class="fas fa-cogs me-2"></i>
              Axle Configuration
            </h5>
          </div>
          <div class="card-body">
            <div class="axle-configuration">
              <div v-for="(axle, index) in meterState.axle_configuration" :key="index" class="axle-row mb-3">
                <!-- TWO_TYRES_WITH_STEERING_WHEEL: []-o-[] -->
                <div v-if="axle.axle === 'TWO_TYRES_WITH_STEERING_WHEEL'" class="axle-display">
                  <div class="axle-visual">
                    <div class="tyre-box">
                      <i class="fas fa-square"></i>
                    </div>
                    <div class="connector-line"></div>
                    <div class="steering-wheel">
                      <i class="fas fa-circle"></i>
                    </div>
                    <div class="connector-line"></div>
                    <div class="tyre-box">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="axle-label">Steering Axle</div>
                </div>

                <!-- TWO_TYRES: []--[] -->
                <div v-else-if="axle.axle === 'TWO_TYRES'" class="axle-display">
                  <div class="axle-visual">
                    <div class="tyre-box">
                      <i class="fas fa-square"></i>
                    </div>
                    <div class="connector-double-line"></div>
                    <div class="tyre-box">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="axle-label">Drive Axle</div>
                </div>

                <!-- ONE_SPARE_TYRE: i=i -->
                <div v-else-if="axle.axle === 'ONE_SPARE_TYRE'" class="axle-display">
                  <div class="axle-visual spare-visual">
                    <div class="spare-tyre">
                      <span class="spare-symbol">i</span>
                    </div>
                    <div class="spare-connector">=</div>
                    <div class="spare-tyre">
                      <span class="spare-symbol">i</span>
                    </div>
                  </div>
                  <div class="axle-label">Spare Tyre</div>
                </div>

                <!-- Unknown axle type -->
                <div v-else class="axle-display">
                  <div class="axle-visual">
                    <div class="unknown-axle">{{ axle.axle }}</div>
                  </div>
                  <div class="axle-label">Unknown Type</div>
                </div>
              </div>

              <!-- Empty state -->
              <div v-if="!meterState.axle_configuration || meterState.axle_configuration.length === 0"
                   class="text-center text-muted py-4">
                <i class="fas fa-info-circle me-2"></i>
                No axle configuration available
              </div>
            </div>
          </div>
        </div>

        <!-- Main Information Card -->
        <div class="card mb-4">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
              <i class="fas fa-chart-line me-2"></i>
              Meter State Information
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label text-muted">
                  <i class="fas fa-truck me-1"></i>
                  Asset Name
                </label>
                <div class="fw-semibold">{{ meterState.asset_name || 'N/A' }}</div>
              </div>

              <div class="col-md-6 mb-3">
                <label class="form-label text-muted">
                  <i class="fas fa-id-card me-1"></i>
                  Asset ID
                </label>
                <div class="fw-semibold">{{ meterState.asset_id }}</div>
              </div>

              <div class="col-md-6 mb-3">
                <label class="form-label text-muted">
                  <i class="fas fa-calendar me-1"></i>
                  Date & Time
                </label>
                <div class="fw-semibold">
                  {{ formatDateTime(meterState.date_time) }}
                </div>
              </div>

              <div class="col-md-6 mb-3">
                <label class="form-label text-muted">
                  <i class="fas fa-user me-1"></i>
                  Updated By
                </label>
                <div class="fw-semibold">{{ meterState.updated_by_user_name || 'System' }}</div>
              </div>
            </div>
          </div>
        </div>

      </div>

      <div class="col-lg-6">
        <!-- Meter Readings Card -->
        <div class="card mb-4">
          <div class="card-header bg-info text-white">
            <h5 class="mb-0">
              <i class="fas fa-tachometer-alt me-2"></i>
              Meter Readings
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="text-center p-4 bg-light rounded">
                  <i class="fas fa-road fa-2x text-primary mb-2"></i>
                  <h4 class="text-primary mb-1">{{ formatNumber(meterState.vehicle_km) }}</h4>
                  <small class="text-muted">Kilometers</small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="text-center p-4 bg-light rounded">
                  <i class="fas fa-clock fa-2x text-success mb-2"></i>
                  <h4 class="text-success mb-1">{{ formatNumber(meterState.vehicle_hm) }}</h4>
                  <small class="text-muted">Hours</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Asset Linkeds Card -->
        <div v-if="meterState.asset_linkeds && meterState.asset_linkeds.length > 0" class="card mb-4">
          <div class="card-header bg-success text-white">
            <h5 class="mb-0">
              <i class="fas fa-link me-2"></i>
              Linked Assets ({{ meterState.asset_linkeds.length }})
            </h5>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-sm mb-0">
                <thead class="table-light">
                  <tr>
                    <th>Serial Number</th>
                    <th>Position</th>
                    <th>Asset ID</th>
                    <th>Linked At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="linked in meterState.asset_linkeds" :key="linked.child_asset_id">
                    <td class="font-monospace fw-semibold">{{ linked.serial_number }}</td>
                    <td>
                      <span class="badge bg-primary">Position {{ linked.tyre_position }}</span>
                    </td>
                    <td class="font-monospace small">{{ linked.child_asset_id }}</td>
                    <td class="small">{{ formatDateTime(linked.created_at) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Asset Unlinkeds Card -->
        <div v-if="meterState.asset_unlinkeds && meterState.asset_unlinkeds.length > 0" class="card mb-4">
          <div class="card-header bg-warning text-dark">
            <h5 class="mb-0">
              <i class="fas fa-unlink me-2"></i>
              Unlinked Assets ({{ meterState.asset_unlinkeds.length }})
            </h5>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-sm mb-0">
                <thead class="table-light">
                  <tr>
                    <th>Serial Number</th>
                    <th>Position</th>
                    <th>Asset ID</th>
                    <th>Unlinked At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="unlinked in meterState.asset_unlinkeds" :key="unlinked.child_asset_id + unlinked.created_at">
                    <td class="font-monospace fw-semibold">{{ unlinked.serial_number }}</td>
                    <td>
                      <span class="badge bg-secondary">Position {{ unlinked.tyre_position }}</span>
                    </td>
                    <td class="font-monospace small">{{ unlinked.child_asset_id }}</td>
                    <td class="small">{{ formatDateTime(unlinked.created_at) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="(!meterState.asset_linkeds || meterState.asset_linkeds.length === 0) &&
                   (!meterState.asset_unlinkeds || meterState.asset_unlinkeds.length === 0)"
             class="card mb-4">
          <div class="card-body text-center py-5">
            <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No Asset Links Found</h5>
            <p class="text-muted">There are no linked or unlinked assets for this meter state.</p>
          </div>
        </div>
      </div>

      <!-- Actions Sidebar -->
      <div class="col-lg-4">
        <div class="card">
          <div class="card-header bg-warning text-dark">
            <h5 class="mb-0">
              <i class="fas fa-tools me-2"></i>
              Actions
            </h5>
          </div>
          <div class="card-body">
            <!-- Delete Button -->
            <button 
              @click="showDeleteConfirmation = true" 
              class="btn btn-danger w-100 mb-3"
              :disabled="deleting"
            >
              <i class="fas fa-trash me-2"></i>
              <span v-if="deleting">Deleting...</span>
              <span v-else>Delete Meter State</span>
            </button>

            <!-- Metadata -->
            <div class="border-top pt-3">
              <h6 class="text-muted mb-3">Metadata</h6>
              
              <div class="mb-2">
                <small class="text-muted d-block">Created At</small>
                <small class="fw-semibold">{{ formatDateTime(meterState.created_at) }}</small>
              </div>
              
              <div class="mb-2">
                <small class="text-muted d-block">Updated At</small>
                <small class="fw-semibold">{{ formatDateTime(meterState.updated_at) }}</small>
              </div>
              
              <div v-if="meterState.updated_by" class="mb-2">
                <small class="text-muted d-block">Updated By</small>
                <small class="fw-semibold">{{ meterState.updated_by }}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div 
      v-if="showDeleteConfirmation" 
      class="modal fade show d-block" 
      tabindex="-1" 
      style="background-color: rgba(0,0,0,0.5);"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-exclamation-triangle text-warning me-2"></i>
              Confirm Deletion
            </h5>
            <button 
              type="button" 
              class="btn-close" 
              @click="showDeleteConfirmation = false"
            ></button>
          </div>
          <div class="modal-body">
            <p>Are you sure you want to delete this meter state record?</p>
            <div class="alert alert-warning">
              <i class="fas fa-info-circle me-2"></i>
              <strong>This action cannot be undone.</strong>
            </div>
            <div class="bg-light p-3 rounded">
              <strong>Meter State ID:</strong> {{ meterState.id }}<br>
              <strong>Date:</strong> {{ formatDateTime(meterState.date_time) }}<br>
              <strong>Vehicle KM:</strong> {{ formatNumber(meterState.vehicle_km) }}<br>
              <strong>Vehicle HM:</strong> {{ formatNumber(meterState.vehicle_hm) }}
            </div>
          </div>
          <div class="modal-footer">
            <button 
              type="button" 
              class="btn btn-secondary" 
              @click="showDeleteConfirmation = false"
            >
              Cancel
            </button>
            <button 
              type="button" 
              class="btn btn-danger" 
              @click="deleteMeterState"
              :disabled="deleting"
            >
              <i class="fas fa-trash me-2"></i>
              <span v-if="deleting">Deleting...</span>
              <span v-else>Delete</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { apiService } from '../services/api.js'

export default {
  name: 'MeterStateDetail',
  props: {
    id: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      meterState: null,
      loading: true,
      error: null,
      showDeleteConfirmation: false,
      deleting: false
    }
  },
  async mounted() {
    await this.fetchMeterStateDetail()
  },
  watch: {
    id: {
      immediate: true,
      async handler() {
        await this.fetchMeterStateDetail()
      }
    }
  },
  methods: {
    async fetchMeterStateDetail() {
      try {
        this.loading = true
        this.error = null

        const response = await apiService.getMeterStateDetail(this.id)
        // Handle the nested response structure
        this.meterState = response.data?.data || response.data || response

        console.log('Meter state data:', this.meterState)
      } catch (error) {
        this.error = 'Failed to load meter state details. Please try again later.'
        console.error('Error fetching meter state detail:', error)
      } finally {
        this.loading = false
      }
    },
    
    async deleteMeterState() {
      try {
        this.deleting = true
        
        await apiService.deleteMeterState(this.id)
        
        // Show success message and navigate back
        this.$router.push({ 
          name: 'MeterStatesList', 
          params: { vehicleId: this.meterState.asset_id } 
        })
        
        // You could also show a toast notification here
        alert('Meter state deleted successfully!')
        
      } catch (error) {
        console.error('Error deleting meter state:', error)
        alert('Failed to delete meter state. Please try again.')
      } finally {
        this.deleting = false
        this.showDeleteConfirmation = false
      }
    },
    
    goBack() {
      if (this.meterState && this.meterState.asset_id) {
        this.$router.push({ 
          name: 'MeterStatesList', 
          params: { vehicleId: this.meterState.asset_id } 
        })
      } else {
        this.$router.go(-1)
      }
    },
    
    formatDateTime(dateString) {
      if (!dateString) return 'N/A'
      const date = new Date(dateString)
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    
    formatNumber(value) {
      if (value === null || value === undefined) return 'N/A'
      return new Intl.NumberFormat().format(value)
    },
    
    formatJSON(obj) {
      if (!obj) return 'N/A'
      try {
        return JSON.stringify(obj, null, 2)
      } catch (e) {
        return String(obj)
      }
    }
  }
}
</script>

<style scoped>
.meter-state-detail {
  min-height: 400px;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.form-label {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.fw-semibold {
  font-weight: 600;
}

.loading-spinner {
  min-height: 300px;
}

.modal.show {
  display: block !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

pre {
  font-size: 0.875rem;
  max-height: 200px;
  overflow-y: auto;
}

.btn-danger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.text-center h4 {
  font-weight: 700;
}

/* Axle Configuration Styles */
.axle-configuration {
  padding: 20px;
}

.axle-row {
  margin-bottom: 20px;
}

.axle-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.axle-visual {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  color: #495057;
}

/* TWO_TYRES_WITH_STEERING_WHEEL: []-o-[] */
.tyre-box {
  width: 40px;
  height: 30px;
  border: 2px solid #495057;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.tyre-box i {
  font-size: 20px;
  color: #495057;
}

.connector-line {
  width: 20px;
  height: 2px;
  background-color: #495057;
}

.steering-wheel {
  width: 30px;
  height: 30px;
  border: 2px solid #495057;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e9ecef;
}

.steering-wheel i {
  font-size: 16px;
  color: #495057;
}

/* TWO_TYRES: []--[] */
.connector-double-line {
  width: 30px;
  height: 4px;
  background-color: #495057;
  position: relative;
}

.connector-double-line::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #495057;
}

.connector-double-line::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #495057;
}

/* ONE_SPARE_TYRE: i=i */
.spare-visual {
  font-family: 'Courier New', monospace;
  font-size: 28px;
  font-weight: bold;
}

.spare-tyre {
  width: 30px;
  height: 30px;
  border: 2px solid #6c757d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.spare-symbol {
  font-size: 18px;
  color: #6c757d;
  font-style: italic;
}

.spare-connector {
  font-size: 20px;
  color: #6c757d;
  margin: 0 5px;
}

.axle-label {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
  text-align: center;
}

.unknown-axle {
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 0.875rem;
  color: #6c757d;
}

/* Table styles */
.table th {
  font-weight: 600;
  font-size: 0.875rem;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.table td {
  font-size: 0.875rem;
  vertical-align: middle;
}

.badge {
  font-size: 0.75rem;
  padding: 0.35em 0.65em;
}

.font-monospace {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .axle-visual {
    font-size: 20px;
    gap: 6px;
  }

  .tyre-box {
    width: 35px;
    height: 25px;
  }

  .tyre-box i {
    font-size: 16px;
  }

  .connector-line {
    width: 15px;
  }

  .connector-double-line {
    width: 20px;
  }

  .steering-wheel {
    width: 25px;
    height: 25px;
  }

  .steering-wheel i {
    font-size: 14px;
  }

  .spare-visual {
    font-size: 24px;
  }

  .spare-tyre {
    width: 25px;
    height: 25px;
  }

  .spare-symbol {
    font-size: 16px;
  }
}
</style>
