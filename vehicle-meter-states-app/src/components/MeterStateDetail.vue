<template>
  <div class="meter-state-detail">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2>
        <i class="fas fa-info-circle me-2"></i>
        Meter State Detail
      </h2>
      <div class="btn-group">
        <button @click="goBack" class="btn btn-outline-secondary">
          <i class="fas fa-arrow-left me-2"></i>
          Back
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-spinner">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="alert alert-danger" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{ error }}
      <button @click="fetchMeterStateDetail" class="btn btn-sm btn-outline-danger ms-2">
        <i class="fas fa-redo me-1"></i>
        Retry
      </button>
    </div>

    <!-- Meter State Detail -->
    <div v-else-if="meterState" class="row">
      <div class="col-lg-6">
        <!-- Axle Configuration Card -->
        <div class="card mb-4">
          <div class="card-header bg-light">
            <h5 class="mb-0 text-dark">
              <i class="fas fa-cogs me-2"></i>
              Axle Configuration
            </h5>
          </div>
          <div class="card-body">
            <div class="axle-configuration">
              <!-- Axle visualization grid -->
              <div class="axle-grid">
                <!-- Row 1 -->
                <div class="axle-row">
                  <div class="tyre-position" :class="getTyreClass(1)">
                    <i class="fas fa-circle tyre-icon"></i>
                    <span class="position-number">1</span>
                  </div>
                  <div class="axle-connector"></div>
                  <div class="tyre-position" :class="getTyreClass(2)">
                    <i class="fas fa-circle tyre-icon"></i>
                    <span class="position-number">2</span>
                  </div>
                </div>

                <!-- Row 2 -->
                <div class="axle-row">
                  <div class="tyre-position" :class="getTyreClass(3)">
                    <i class="fas fa-circle tyre-icon"></i>
                    <span class="position-number">3</span>
                  </div>
                  <div class="axle-connector"></div>
                  <div class="tyre-position" :class="getTyreClass(4)">
                    <i class="fas fa-circle tyre-icon"></i>
                    <span class="position-number">4</span>
                  </div>
                </div>

                <!-- Row 3 -->
                <div class="axle-row">
                  <div class="tyre-position" :class="getTyreClass(5)">
                    <i class="fas fa-circle tyre-icon"></i>
                    <span class="position-number">5</span>
                  </div>
                  <div class="axle-connector"></div>
                  <div class="tyre-position" :class="getTyreClass(6)">
                    <i class="fas fa-circle tyre-icon"></i>
                    <span class="position-number">6</span>
                  </div>
                </div>

                <!-- Spare tyre -->
                <div class="spare-tyre">
                  <div class="tyre-position" :class="getTyreClass(0)">
                    <i class="fas fa-circle tyre-icon"></i>
                    <span class="position-number">0</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Meter Readings Card -->
        <div class="card mb-4">
          <div class="card-header bg-info text-white">
            <h5 class="mb-0">
              <i class="fas fa-tachometer-alt me-2"></i>
              Meter Readings
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="text-center p-4 bg-light rounded">
                  <i class="fas fa-road fa-2x text-primary mb-2"></i>
                  <h4 class="text-primary mb-1">{{ formatNumber(meterState.vehicle_km) }}</h4>
                  <small class="text-muted">Kilometers</small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="text-center p-4 bg-light rounded">
                  <i class="fas fa-clock fa-2x text-success mb-2"></i>
                  <h4 class="text-success mb-1">{{ formatNumber(meterState.vehicle_hm) }}</h4>
                  <small class="text-muted">Hours</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-6">
        <!-- Tyre Linkage Log Card -->
        <div class="card mb-4">
          <div class="card-header bg-light">
            <h5 class="mb-0 text-dark">
              <i class="fas fa-list me-2"></i>
              Tyre Linkage Log
            </h5>
            <small class="text-muted">Actions are listed chronologically, from oldest to newest</small>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-sm mb-0">
                <thead class="table-light">
                  <tr>
                    <th style="width: 60px;">No.</th>
                    <th style="width: 100px;">Action</th>
                    <th>Serial No.</th>
                    <th style="width: 80px;">Position</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(log, index) in tyreLinkageLog" :key="index">
                    <td class="text-center">{{ index + 1 }}</td>
                    <td>
                      <span class="badge" :class="log.action === 'Linked' ? 'bg-success' : 'bg-danger'">
                        <i class="fas" :class="log.action === 'Linked' ? 'fa-link' : 'fa-unlink'"></i>
                        {{ log.action }}
                      </span>
                    </td>
                    <td class="font-monospace">{{ log.serialNo }}</td>
                    <td class="text-center">{{ log.position }}</td>
                  </tr>
                  <tr v-if="tyreLinkageLog.length === 0">
                    <td colspan="4" class="text-center text-muted py-3">
                      <i class="fas fa-info-circle me-1"></i>
                      No tyre linkage history available
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions Sidebar -->
      <div class="col-lg-4">
        <div class="card">
          <div class="card-header bg-warning text-dark">
            <h5 class="mb-0">
              <i class="fas fa-tools me-2"></i>
              Actions
            </h5>
          </div>
          <div class="card-body">
            <!-- Delete Button -->
            <button 
              @click="showDeleteConfirmation = true" 
              class="btn btn-danger w-100 mb-3"
              :disabled="deleting"
            >
              <i class="fas fa-trash me-2"></i>
              <span v-if="deleting">Deleting...</span>
              <span v-else>Delete Meter State</span>
            </button>

            <!-- Metadata -->
            <div class="border-top pt-3">
              <h6 class="text-muted mb-3">Metadata</h6>
              
              <div class="mb-2">
                <small class="text-muted d-block">Created At</small>
                <small class="fw-semibold">{{ formatDateTime(meterState.created_at) }}</small>
              </div>
              
              <div class="mb-2">
                <small class="text-muted d-block">Updated At</small>
                <small class="fw-semibold">{{ formatDateTime(meterState.updated_at) }}</small>
              </div>
              
              <div v-if="meterState.updated_by" class="mb-2">
                <small class="text-muted d-block">Updated By</small>
                <small class="fw-semibold">{{ meterState.updated_by }}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div 
      v-if="showDeleteConfirmation" 
      class="modal fade show d-block" 
      tabindex="-1" 
      style="background-color: rgba(0,0,0,0.5);"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-exclamation-triangle text-warning me-2"></i>
              Confirm Deletion
            </h5>
            <button 
              type="button" 
              class="btn-close" 
              @click="showDeleteConfirmation = false"
            ></button>
          </div>
          <div class="modal-body">
            <p>Are you sure you want to delete this meter state record?</p>
            <div class="alert alert-warning">
              <i class="fas fa-info-circle me-2"></i>
              <strong>This action cannot be undone.</strong>
            </div>
            <div class="bg-light p-3 rounded">
              <strong>Meter State ID:</strong> {{ meterState.id }}<br>
              <strong>Date:</strong> {{ formatDateTime(meterState.date_time) }}<br>
              <strong>Vehicle KM:</strong> {{ formatNumber(meterState.vehicle_km) }}<br>
              <strong>Vehicle HM:</strong> {{ formatNumber(meterState.vehicle_hm) }}
            </div>
          </div>
          <div class="modal-footer">
            <button 
              type="button" 
              class="btn btn-secondary" 
              @click="showDeleteConfirmation = false"
            >
              Cancel
            </button>
            <button 
              type="button" 
              class="btn btn-danger" 
              @click="deleteMeterState"
              :disabled="deleting"
            >
              <i class="fas fa-trash me-2"></i>
              <span v-if="deleting">Deleting...</span>
              <span v-else>Delete</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { apiService } from '../services/api.js'

export default {
  name: 'MeterStateDetail',
  props: {
    id: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      meterState: null,
      loading: true,
      error: null,
      showDeleteConfirmation: false,
      deleting: false,
      tyreLinkageLog: [
        { action: 'Linked', serialNo: 'TYR7538', position: 6 },
        { action: 'Linked', serialNo: 'TYR2378', position: 1 },
        { action: 'Unlinked', serialNo: 'TYR7539', position: 1 },
        { action: 'Linked', serialNo: 'TYR1412', position: 0 },
        { action: 'Unlinked', serialNo: 'TYR2378', position: 0 },
        { action: 'Unlinked', serialNo: 'TYR1412', position: 6 }
      ]
    }
  },
  async mounted() {
    await this.fetchMeterStateDetail()
  },
  watch: {
    id: {
      immediate: true,
      async handler() {
        await this.fetchMeterStateDetail()
      }
    }
  },
  methods: {
    async fetchMeterStateDetail() {
      try {
        this.loading = true
        this.error = null
        
        const response = await apiService.getMeterStateDetail(this.id)
        this.meterState = response.data || response
      } catch (error) {
        this.error = 'Failed to load meter state details. Please try again later.'
        console.error('Error fetching meter state detail:', error)
      } finally {
        this.loading = false
      }
    },
    
    async deleteMeterState() {
      try {
        this.deleting = true
        
        await apiService.deleteMeterState(this.id)
        
        // Show success message and navigate back
        this.$router.push({ 
          name: 'MeterStatesList', 
          params: { vehicleId: this.meterState.asset_id } 
        })
        
        // You could also show a toast notification here
        alert('Meter state deleted successfully!')
        
      } catch (error) {
        console.error('Error deleting meter state:', error)
        alert('Failed to delete meter state. Please try again.')
      } finally {
        this.deleting = false
        this.showDeleteConfirmation = false
      }
    },
    
    goBack() {
      if (this.meterState && this.meterState.asset_id) {
        this.$router.push({ 
          name: 'MeterStatesList', 
          params: { vehicleId: this.meterState.asset_id } 
        })
      } else {
        this.$router.go(-1)
      }
    },
    
    formatDateTime(dateString) {
      if (!dateString) return 'N/A'
      const date = new Date(dateString)
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    
    formatNumber(value) {
      if (value === null || value === undefined) return 'N/A'
      return new Intl.NumberFormat().format(value)
    },
    
    formatJSON(obj) {
      if (!obj) return 'N/A'
      try {
        return JSON.stringify(obj, null, 2)
      } catch (e) {
        return String(obj)
      }
    },

    getTyreClass(position) {
      // Determine tyre status based on linkage log
      const linkedTyres = this.tyreLinkageLog.filter(log =>
        log.action === 'Linked' && log.position === position
      )
      const unlinkedTyres = this.tyreLinkageLog.filter(log =>
        log.action === 'Unlinked' && log.position === position
      )

      // If more linked than unlinked, position is occupied
      const isLinked = linkedTyres.length > unlinkedTyres.length

      return {
        'tyre-linked': isLinked,
        'tyre-empty': !isLinked
      }
    }
  }
}
</script>

<style scoped>
.meter-state-detail {
  min-height: 400px;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.form-label {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.fw-semibold {
  font-weight: 600;
}

.loading-spinner {
  min-height: 300px;
}

.modal.show {
  display: block !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

pre {
  font-size: 0.875rem;
  max-height: 200px;
  overflow-y: auto;
}

.btn-danger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.text-center h4 {
  font-weight: 700;
}

/* Axle Configuration Styles */
.axle-configuration {
  padding: 20px;
}

.axle-grid {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.axle-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.tyre-position {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 2px solid #dee2e6;
  transition: all 0.3s ease;
}

.tyre-position.tyre-linked {
  background-color: #6f42c1;
  border-color: #6f42c1;
  color: white;
}

.tyre-position.tyre-empty {
  background-color: #e9ecef;
  border-color: #dee2e6;
  color: #6c757d;
}

.tyre-icon {
  font-size: 20px;
  margin-bottom: 2px;
}

.position-number {
  font-size: 10px;
  font-weight: bold;
  position: absolute;
  bottom: 2px;
  right: 4px;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.tyre-linked .position-number {
  background-color: rgba(255, 255, 255, 0.9);
  color: #6f42c1;
}

.axle-connector {
  width: 40px;
  height: 4px;
  background-color: #333;
  border-radius: 2px;
}

.spare-tyre {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* Table styles */
.table th {
  font-weight: 600;
  font-size: 0.875rem;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.table td {
  font-size: 0.875rem;
  vertical-align: middle;
}

.badge {
  font-size: 0.75rem;
  padding: 0.35em 0.65em;
}

.font-monospace {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tyre-position {
    width: 50px;
    height: 50px;
  }

  .tyre-icon {
    font-size: 16px;
  }

  .axle-connector {
    width: 30px;
  }

  .position-number {
    width: 14px;
    height: 14px;
    font-size: 9px;
  }
}
</style>
