<template>
  <div class="vehicle-list">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2>
        <i class="fas fa-truck me-2"></i>
        Vehicles
      </h2>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-spinner">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="alert alert-danger" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{ error }}
    </div>

    <!-- Vehicles List -->
    <div v-else-if="vehicles.length > 0" class="row">
      <div v-for="vehicle in vehicles" :key="vehicle.id" class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
          <div class="card-body">
            <h5 class="card-title">
              <i class="fas fa-truck me-2 text-primary"></i>
              {{ vehicle.reference_number || vehicle.registration_number || 'Unknown Vehicle' }}
            </h5>
            
            <div class="card-text">
              <small class="text-muted d-block mb-2">
                <i class="fas fa-id-card me-1"></i>
                ID: {{ vehicle.id }}
              </small>
              
              <div v-if="vehicle.registration_number" class="mb-2">
                <i class="fas fa-car me-1"></i>
                <strong>Registration:</strong> {{ vehicle.registration_number }}
              </div>
              
              <div v-if="vehicle.serial_number" class="mb-2">
                <i class="fas fa-barcode me-1"></i>
                <strong>Serial:</strong> {{ vehicle.serial_number }}
              </div>
              
              <div v-if="vehicle.brand_name" class="mb-2">
                <i class="fas fa-tag me-1"></i>
                <strong>Brand:</strong> {{ vehicle.brand_name }}
              </div>
              
              <div v-if="vehicle.model_name" class="mb-2">
                <i class="fas fa-cog me-1"></i>
                <strong>Model:</strong> {{ vehicle.model_name }}
              </div>
              
              <div v-if="vehicle.current_km !== undefined" class="mb-2">
                <i class="fas fa-tachometer-alt me-1"></i>
                <strong>Current KM:</strong> {{ formatNumber(vehicle.current_km) }}
              </div>
              
              <div v-if="vehicle.current_hm !== undefined" class="mb-2">
                <i class="fas fa-clock me-1"></i>
                <strong>Current HM:</strong> {{ formatNumber(vehicle.current_hm) }}
              </div>
            </div>
          </div>
          
          <div class="card-footer bg-transparent">
            <router-link 
              :to="{ name: 'MeterStatesList', params: { vehicleId: vehicle.id } }"
              class="btn btn-primary w-100"
            >
              <i class="fas fa-chart-line me-2"></i>
              View Meter States
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-5">
      <i class="fas fa-truck fa-3x text-muted mb-3"></i>
      <h4 class="text-muted">No Vehicles Found</h4>
      <p class="text-muted">There are no vehicles available at the moment.</p>
    </div>
  </div>
</template>

<script>
import { apiService } from '../services/api.js'

export default {
  name: 'VehicleList',
  data() {
    return {
      vehicles: [],
      loading: true,
      error: null
    }
  },
  async mounted() {
    await this.fetchVehicles()
  },
  methods: {
    async fetchVehicles() {
      try {
        this.loading = true
        this.error = null
        
        const response = await apiService.getVehicles()
        this.vehicles = response.data || response || []
      } catch (error) {
        this.error = 'Failed to load vehicles. Please try again later.'
        console.error('Error fetching vehicles:', error)
      } finally {
        this.loading = false
      }
    },
    formatNumber(value) {
      if (value === null || value === undefined) return 'N/A'
      return new Intl.NumberFormat().format(value)
    }
  }
}
</script>

<style scoped>
.vehicle-list {
  min-height: 400px;
}

.card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-title {
  color: #495057;
  font-weight: 600;
}

.card-text small {
  font-size: 0.875rem;
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

.loading-spinner {
  min-height: 300px;
}

.text-muted {
  color: #6c757d !important;
}
</style>
