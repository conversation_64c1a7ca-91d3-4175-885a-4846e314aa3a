<template>
  <div class="meter-states-list">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2>
        <i class="fas fa-chart-line me-2"></i>
        Meter States for Vehicle {{ vehicleId }}
      </h2>
      <router-link :to="{ name: 'VehicleList' }" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        Back to Vehicles
      </router-link>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-spinner">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="alert alert-danger" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{ error }}
      <button @click="fetchMeterStates" class="btn btn-sm btn-outline-danger ms-2">
        <i class="fas fa-redo me-1"></i>
        Retry
      </button>
    </div>

    <!-- Meter States Table -->
    <div v-else-if="meterStates.length > 0" class="card">
      <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
          <i class="fas fa-list me-2"></i>
          Meter States History ({{ meterStates.length }} records)
        </h5>
      </div>
      
      <div class="table-responsive">
        <table class="table table-hover mb-0">
          <thead class="table-light">
            <tr>
              <th scope="col">
                <i class="fas fa-calendar me-1"></i>
                Date & Time
              </th>
              <th scope="col">
                <i class="fas fa-tachometer-alt me-1"></i>
                Vehicle KM
              </th>
              <th scope="col">
                <i class="fas fa-clock me-1"></i>
                Vehicle HM
              </th>
              <th scope="col">
                <i class="fas fa-user me-1"></i>
                Created By
              </th>
              <th scope="col" class="text-center">
                <i class="fas fa-cogs me-1"></i>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="meterState in sortedMeterStates" :key="meterState.id" class="align-middle">
              <td>
                <div class="fw-semibold">{{ formatDate(meterState.date_time) }}</div>
                <small class="text-muted">{{ formatTime(meterState.date_time) }}</small>
              </td>
              <td>
                <span class="badge bg-info">
                  {{ formatNumber(meterState.vehicle_km) }} km
                </span>
              </td>
              <td>
                <span class="badge bg-success">
                  {{ formatNumber(meterState.vehicle_hm) }} hrs
                </span>
              </td>
              <td>
                <div class="d-flex align-items-center">
                  <i class="fas fa-user-circle me-2 text-muted"></i>
                  {{ meterState.created_by || 'System' }}
                </div>
              </td>
              <td class="text-center">
                <div class="btn-group" role="group">
                  <router-link 
                    :to="{ name: 'MeterStateDetail', params: { id: meterState.id } }"
                    class="btn btn-sm btn-outline-primary"
                    title="View Details"
                  >
                    <i class="fas fa-eye"></i>
                  </router-link>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-5">
      <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
      <h4 class="text-muted">No Meter States Found</h4>
      <p class="text-muted">There are no meter state records for this vehicle yet.</p>
      <button @click="fetchMeterStates" class="btn btn-primary">
        <i class="fas fa-redo me-2"></i>
        Refresh
      </button>
    </div>
  </div>
</template>

<script>
import { apiService } from '../services/api.js'

export default {
  name: 'MeterStatesList',
  props: {
    vehicleId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      meterStates: [],
      loading: true,
      error: null
    }
  },
  computed: {
    sortedMeterStates() {
      return [...this.meterStates].sort((a, b) => {
        return new Date(b.date_time) - new Date(a.date_time)
      })
    }
  },
  async mounted() {
    await this.fetchMeterStates()
  },
  watch: {
    vehicleId: {
      immediate: true,
      async handler() {
        await this.fetchMeterStates()
      }
    }
  },
  methods: {
    async fetchMeterStates() {
      try {
        this.loading = true
        this.error = null
        
        const response = await apiService.getVehicleMeterStates(this.vehicleId)
        this.meterStates = response.data || response || []
      } catch (error) {
        this.error = 'Failed to load meter states. Please try again later.'
        console.error('Error fetching meter states:', error)
      } finally {
        this.loading = false
      }
    },
    formatDate(dateString) {
      if (!dateString) return 'N/A'
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    },
    formatTime(dateString) {
      if (!dateString) return 'N/A'
      const date = new Date(dateString)
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    formatNumber(value) {
      if (value === null || value === undefined) return 'N/A'
      return new Intl.NumberFormat().format(value)
    }
  }
}
</script>

<style scoped>
.meter-states-list {
  min-height: 400px;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
}

.table td {
  vertical-align: middle;
}

.badge {
  font-size: 0.875rem;
  padding: 0.5em 0.75em;
}

.btn-group .btn {
  border-radius: 0.375rem;
}

.btn-outline-primary:hover {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.loading-spinner {
  min-height: 300px;
}

.fw-semibold {
  font-weight: 600;
}

.card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.table-responsive {
  border-radius: 0 0 0.375rem 0.375rem;
}
</style>
