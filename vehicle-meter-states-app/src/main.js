import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'

// Import Bootstrap CSS and JS
import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap/dist/js/bootstrap.bundle.min.js'
import '@fortawesome/fontawesome-free/css/all.min.css'

// Import components
import VehicleList from './components/VehicleList.vue'
import MeterStatesList from './components/MeterStatesList.vue'
import MeterStateDetail from './components/MeterStateDetail.vue'

// Define routes
const routes = [
  { path: '/', redirect: '/vehicles' },
  { path: '/vehicles', component: VehicleList, name: 'VehicleList' },
  { 
    path: '/vehicles/:vehicleId/meter-states', 
    component: MeterStatesList, 
    name: 'MeterStatesList',
    props: true 
  },
  { 
    path: '/meter-states/:id', 
    component: MeterStateDetail, 
    name: 'MeterStateDetail',
    props: true 
  }
]

// Create router
const router = createRouter({
  history: createWebHistory(),
  routes
})

// Create and mount app
const app = createApp(App)
app.use(router)
app.mount('#app')
