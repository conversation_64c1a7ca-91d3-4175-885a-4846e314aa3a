{"name": "vehicle-meter-states-app", "version": "1.0.0", "description": "Vue.js application for managing vehicle meter states", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "axios": "^1.5.0", "bootstrap": "^5.3.0", "bootstrap-vue-next": "^0.14.0", "@fortawesome/fontawesome-free": "^6.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9"}}