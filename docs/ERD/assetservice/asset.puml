@startuml
hide circle
skinparam linetype ortho

entity "**ams_asset_assignments**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""asset_id"": //character varying(40) [FK]//
  *""user_id"": //character varying(40) //
  *""assigned_date_time"": //timestamp with time zone //
  ""unassigned_date_time"": //timestamp with time zone //
  *""client_id"": //character varying(40) //
  ""unassigned_by_user_id"": //character varying(40) //
  ""assigned_by_user_id"": //character varying(40) //
}

entity "**ams_asset_assignment_groups**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""asset_id"": //character varying(40) [FK]//
  *""user_id"": //character varying(40) //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
}

entity "**ams_ASSET_OWNERSHIP_CATEGORIES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_ASSET_CATEGORIES**" {
  + ""code"": //character varying(255) [PK]//
  --
  *""label"": //character varying(255) //
  *""description"": //text //
  ""is_general"": //boolean //
}

entity "**ams_ASSET_SUB_CATEGORIES**" {
  + ""code"": //character varying(255) [PK]//
  --
  *""label"": //character varying(255) //
  *""description"": //text //
}

entity "**ams_ASSET_CATEGORY_MAPPING**" {
  + ""category_code"": //character varying(255) [PK][FK]//
  + ""sub_category_code"": //character varying(255) [PK][FK]//
  --
}

entity "**ams_asset_components**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""serial_number"": //character varying(50) //
  *""component_name"": //character varying(100) //
  ""assigned_to"": //character varying(40) //
  ""expiry_date"": //timestamp with time zone //
  *""status_code"": //character varying(20) [FK]//
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
  ""expiry_reminder_date"": //timestamp with time zone //
  ""asset_id"": //character varying(40) [FK]//
  *""is_create_work_order_on_expiry"": //boolean //
  *""is_create_task_on_expiry"": //boolean //
}

entity "**ams_ASSET_COMPONENT_STATUTES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_ASSET_INITIAL_CONDITIONS**" {
  + ""code"": //character varying(255) [PK]//
  --
  *""label"": //character varying(255) //
  *""description"": //text //
}

entity "**ams_ASSET_HANDOVER_REQUEST_STATUSES**" {
  + ""code"": //character varying(50) [PK]//
  --
  *""label"": //character varying(50) //
  ""description"": //text //
}

entity "**ams_asset_handover_requests**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""asset_id"": //character varying(40) //
  ""before_assigned_user_id"": //character varying(40) //
  ""target_assigned_user_id"": //character varying(40) //
  ""form_template_id"": //character varying(40) //
  ""need_inspection"": //boolean //
  ""reject_reason"": //text //
  ""status_code"": //character varying(50) [FK]//
  ""confirm_date"": //timestamp with time zone //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
  *""client_id"": //character varying(40) //
  ""number"": //character varying(20) //
}

entity "**ams_ASSET_INSPECTION_ASSIGNMENT_TYPES**" {
  + ""code"": //character varying(255) [PK]//
  --
  *""label"": //character varying(255) //
  *""description"": //text //
}

entity "**ams_asset_inspection_assignments**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp without time zone //
  *""updated_at"": //timestamp without time zone //
  ""deleted_at"": //timestamp without time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
  *""inspection_id"": //character varying(40) [FK]//
  ""user_id"": //character varying(40) //
  ""user_name"": //character varying(255) //
  *""type_code"": //character varying(255) [FK]//
}

entity "**ams_ASSET_INSPECTION_REFERENCE**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_asset_inspections**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""inspect_by_user_id"": //character varying(40) //
  *""client_id"": //character varying(40) //
  ""reference_code"": //character varying(40) [FK]//
  ""reference_id"": //character varying(40) //
  ""inspection_number"": //character varying(40) //
  ""status_code"": //character varying(255) [FK]//
}

entity "**ams_ASSET_INSPECTION_STATUSES**" {
  + ""code"": //character varying(255) [PK]//
  --
  *""label"": //character varying(255) //
  *""description"": //text //
}

entity "**ams_asset_inspection_tyre**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  ""asset_inspection_id"": //character varying(40) [FK]//
  ""asset_tyre_id"": //character varying(40) [FK]//
  ""remark"": //character varying(255) //
  ""asset_assignment_id"": //character varying(40) //
  *""pressure"": //numeric(14,2) //
  ""rdt1"": //numeric(14,2) //
  ""rdt2"": //numeric(14,2) //
  ""rdt3"": //numeric(14,2) //
  ""tyre_position"": //numeric(14,2) //
  ""average_rtd"": //numeric(14,2) //
  *""client_id"": //character varying(40) //
  ""pressure_status_code"": //character varying(50) //
  ""tyre_km"": //bigint //
  ""tyre_hm"": //bigint //
  ""rdt4"": //numeric(14,2) //
  ""failed_visual_checking"": //boolean //
  ""require_rotation_tyre"": //boolean //
  ""require_spooring_vehicle"": //boolean //
  ""custom_serial_number"": //character varying(40) //
  ""device_id"": //character varying(40) //
  ""tire_tread_and_rim_damage"": //boolean //
  ""custom_brand_name"": //character varying(255) //
  ""custom_tyre_size"": //character varying(255) //
  ""temperature"": //double precision //
  ""number_of_inspection_point"": //integer //
  ""pressure_sensor_ref"": //character varying(255) //
  ""temperature_sensor_ref"": //character varying(255) //
  ""is_mismatch"": //boolean //
  ""custom_rfid"": //character varying(40) //
}

entity "**ams_asset_inspection_vehicle**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  ""asset_inspection_id"": //character varying(40) [FK]//
  ""asset_vehicle_id"": //character varying(40) [FK]//
  ""remark"": //character varying(255) //
  *""asset_assignment_id"": //character varying(40) //
  *""vehicle_km"": //numeric(14,2) //
  *""client_id"": //character varying(40) //
  ""vehicle_hm"": //bigint //
  ""require_spooring_vehicle"": //boolean //
  ""failed_visual_checking"": //boolean //
  ""require_rotation_tyre"": //boolean //
  ""tire_tread_and_rim_damage"": //boolean //
  ""axle_configuration_legacy"": //character varying(255)[] //
  ""custom_brand_name"": //character varying(255) //
  ""custom_model_name"": //character varying(255) //
  ""custom_reference_number"": //character varying(255) //
  ""max_rtd_diff_tolerance"": //smallint //
  ""axle_configuration"": //jsonb //
}

entity "**ams_LINKED_ASSET_TYPE**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_linked_asset_vehicle_tyres**" {
  + ""asset_linked_id"": //character varying(40) [PK][FK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""tyre_position"": //bigint //
  *""on_linked_vehicle_km"": //bigint //
  *""on_linked_tyre_km"": //bigint //
  ""on_unlinked_vehicle_km"": //bigint //
  ""on_unlinked_tyre_km"": //bigint //
  *""client_id"": //character varying(40) //
  ""retread_number"": //integer //
  ""on_linked_vehicle_hm"": //bigint //
  ""on_linked_tyre_hm"": //bigint //
  ""on_unlinked_vehicle_hm"": //bigint //
  ""on_unlinked_tyre_hm"": //bigint //
  ""is_claimed_bonus_penalty"": //boolean //
  ""is_mismatch"": //boolean //
}

entity "**ams_linked_assets**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""child_asset_id"": //character varying(40) [FK]//
  *""parent_asset_id"": //character varying(40) [FK]//
  *""linked_datetime"": //timestamp with time zone //
  ""unlinked_datetime"": //timestamp with time zone //
  *""client_id"": //character varying(40) //
  *""linked_asset_type_code"": //character varying(20) [FK]//
  ""updated_by"": //character varying(40) //
  ""created_by"": //character varying(40) //
}

entity "**ams_asset_logs**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  ""updated_by_user_id"": //character varying(40) //
  *""asset_id"": //character varying(40) [FK]//
  ""previous_value"": //jsonb //
  ""new_value"": //jsonb //
  *""client_id"": //character varying(40) //
  *""type"": //character varying(20) [FK]//
}

entity "**ams_ASSET_LOG_TYPE**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_assets**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""name"": //character varying(100) //
  ""brand_id"": //character varying(40) [FK]//
  ""model_number"": //character varying(50) //
  ""serial_number"": //character varying(50) //
  *""asset_category_code"": //character varying(255) [FK]//
  ""production_date"": //timestamp with time zone //
  *""client_id"": //character varying(40) //
  *""asset_status_code"": //character varying(20) [FK]//
  ""cost"": //bigint //
  ""created_by"": //character varying(40) //
  ""updated_by"": //character varying(40) //
  ""location_id"": //character varying(40) [FK]//
  *""ownership_category_code"": //character varying(20) [FK]//
  *""sub_category_code"": //character varying(255) //
  ""photo"": //character varying(255) //
  ""status_inactive_total_time"": //bigint //
  ""status_inactive_start_time"": //timestamp with time zone //
  ""reference_number"": //character varying(40) //
  ""partner_owner_id"": //character varying(40) //
  ""partner_owner_no"": //character varying(40) //
  ""rfid"": //character varying(40) //
  ""initial_condition_code"": //character varying(255) [FK]//
  ""partner_owner_name"": //character varying(255) //
  ""gps_imei"": //character varying(255) //
  ""handover_form_template_id"": //character varying(40) //
  ""handover_need_inspection"": //boolean //
  ""custom_asset_category_id"": //character varying(40) //
  ""custom_asset_sub_category_id"": //character varying(40) //
  ""use_tyre_optimax"": //boolean //
  ""use_fleet_optimax"": //boolean //
  ""downgrade_reason"": //character varying(255) //
  ""downgrade_tyre_optimax_reason"": //character varying(255) //
  ""model_id"": //character varying(40) [FK]//
}

entity "**ams_asset_models**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""asset_model_name"": //character varying(25) //
  *""brand_id"": //character varying(40) [FK]//
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  ""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
}

entity "**ams_brand_custom_category_mapping**" {
  --
  *""brand_id"": //character varying(40) [FK]//
  *""custom_asset_category_id"": //character varying(40) [FK]//
}

entity "**ams_ASSET_STATUSES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_ASSET_STATUS_REQUEST_GRADES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(255) //
}

entity "**ams_ASSET_STATUS_REQUEST_TYPES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_ASSET_STATUS_REQUEST_STATUSES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_ASSET_STATUS_REQUEST_REASONS**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
  *""asset_category_code"": //character varying(20) [FK]//
  ""type_code"": //character varying(20) //
}

entity "**ams_ASSET_STATUS_REQUEST_SUB_REASONS**" {
  + ""code"": //character varying(255) [PK]//
  --
  *""label"": //character varying(255) //
  *""description"": //text //
  *""reason_code"": //character varying(20) [FK]//
}

entity "**ams_asset_status_requests**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""asset_id"": //character varying(40) [FK]//
  *""reason_code"": //character varying(30) [FK]//
  ""reason"": //character varying(255) //
  ""sub_reason_code"": //character varying(255) [FK]//
  ""sub_reason"": //character varying(255) //
  ""notes"": //character varying(255) //
  *""status_code"": //character varying(20) [FK]//
  *""client_id"": //character varying(40) //
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  ""created_by"": //character varying(40) //
  ""updated_by"": //character varying(40) //
  ""cost"": //integer //
  *""type_code"": //character varying(20) [FK]//
  ""grade_code"": //character varying(20) [FK]//
  ""grade_reason"": //text //
}

entity "**ams_ASSET_TRANSACTION_CATEGORIES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  ""description"": //character varying(40) //
}

entity "**ams_asset_transaction_items**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""asset_transaction_id"": //character varying(40) [FK]//
  ""name"": //character varying(255) //
  ""unit_price"": //bigint //
  ""quantity"": //integer //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
}

entity "**ams_asset_transactions**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""asset_id"": //character varying(40) [FK]//
  *""partner_id"": //character varying(40) //
  ""purchase_order_date"": //timestamp with time zone //
  ""purchase_order_number"": //character varying(40) //
  ""invoice_date"": //timestamp with time zone //
  ""invoice_number"": //character varying(40) //
  ""service_start_date"": //timestamp with time zone //
  ""service_end_date"": //timestamp with time zone //
  ""reference_number"": //character varying(40) //
  *""status_code"": //character varying(20) [FK]//
  *""type_code"": //character varying(20) [FK]//
  ""cost"": //bigint //
  *""assigned_to_user_id"": //character varying(40) //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
  ""tax_cost"": //bigint //
  ""discount_amount"": //bigint //
  ""other_cost"": //bigint //
  ""sub_total"": //bigint //
  ""notes"": //text //
  ""category_code"": //character varying(20) [FK]//
  ""location"": //character varying(50) //
  ""odometer"": //double precision //
  ""payment_method_code"": //character varying(20) [FK]//
}

entity "**ams_ASSET_TRANSACTION_STATUSES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_ASSET_TRANSACTION_TYPES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_ASSET_TRANSACTION_PAYMENT_METHODS**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  ""description"": //character varying(40) //
}

entity "**ams_asset_tyres**" {
  + ""asset_id"": //character varying(40) [PK][FK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  ""datetime_of_last_check"": //timestamp with time zone //
  ""average_rtd"": //numeric(14,2) //
  ""utilization_rate_percentage"": //numeric(14,2) //
  ""utilization_rate_percentage_status_code"": //character varying(20) [FK]//
  ""total_km"": //bigint //
  ""projected_life_km"": //bigint //
  ""pressure"": //numeric(14,2) //
  ""retread_number"": //bigint //
  ""repaired_number"": //bigint //
  ""dot_code"": //character varying(100) //
  ""date_code"": //character varying(100) //
  *""client_id"": //character varying(40) //
  ""tyre_id"": //character varying(40) [FK]//
  ""start_thread_depth"": //numeric(14,2) //
  *""total_lifetime"": //integer //
  ""total_hm"": //bigint //
  ""updated_by"": //character varying(40) //
  ""created_by"": //character varying(40) //
  ""meter_calculation_code"": //character varying(20) [FK]//
  ""average_rtd_last_updated_at"": //timestamp with time zone //
  ""pressure_last_updated_at"": //timestamp with time zone //
  ""temperature"": //double precision //
  ""temperature_last_updated_at"": //timestamp with time zone //
  ""pressure_last_updated_sensor_ref"": //character varying(255) //
  ""temperature_last_updated_sensor_ref"": //character varying(255) //
  ""rtd1"": //numeric(14,2) //
  ""rtd2"": //numeric(14,2) //
  ""rtd3"": //numeric(14,2) //
  ""rtd4"": //numeric(14,2) //
  ""has_not_set_rtd"": //boolean //
  ""prev_km_hm_data_unavailable"": //boolean //
  ""prev_total_km"": //bigint //
  ""prev_total_hm"": //bigint //
  ""first_installed_datetime"": //timestamp with time zone //
  ""last_stock_date"": //timestamp with time zone //
}
entity "**ams_asset_tyres_tread_configs**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""brand_name"": //character varying(100) //
  *""retread_type"": //character varying(100) //
  *""original_tread_depth"": //integer //
  *""width"": //integer //
  *""weight"": //integer //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
}
entity "**ams_asset_tyres_treads**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""asset_id"": //character varying(40) [FK]//
  ""thread_sequence"": //integer //
  ""average_rtd"": //numeric(10,2) //
  ""total_km"": //integer //
  ""vendor_name"": //character varying(100) //
  ""brand_name"": //character varying(100) //
  ""retread_type"": //character varying(100) //
  ""start_thread_depth"": //numeric(10,2) //
  ""cost"": //bigint //
  *""client_id"": //character varying(40) //
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  ""updated_by"": //character varying(40) //
  ""created_by"": //character varying(40) //
  *""total_lifetime"": //integer //
  ""retread_date"": //timestamp with time zone //
  ""total_hm"": //bigint //
  ""type_code"": //character varying(255) [FK]//
  ""partner_id"": //character varying(40) //
  ""notes"": //text //
  ""tyres_tread_config_id"": //character varying(40) [FK]//
}

entity "**ams_asset_tyre_stats_histories**" {
  --
  ""datetime"": //timestamp with time zone //
  ""created_at"": //timestamp with time zone //
  *""asset_id"": //character varying(40) //
  *""client_id"": //character varying(40) //
  ""total_km"": //bigint //
  ""total_hm"": //bigint //
  ""total_lifetime"": //bigint //
}

entity "**ams_asset_tyres_treads**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""asset_id"": //character varying(40) [FK]//
  ""thread_sequence"": //integer //
  ""average_rtd"": //numeric(10,2) //
  ""total_km"": //integer //
  ""vendor_name"": //character varying(100) //
  ""brand_name"": //character varying(100) //
  ""retread_type"": //character varying(100) //
  ""start_thread_depth"": //numeric(10,2) //
  ""cost"": //bigint //
  *""client_id"": //character varying(40) //
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  ""updated_by"": //character varying(40) //
  ""created_by"": //character varying(40) //
  *""total_lifetime"": //integer //
  ""retread_date"": //timestamp with time zone //
  ""total_hm"": //bigint //
  ""type_code"": //character varying(255) [FK]//
  ""partner_id"": //character varying(40) //
  ""notes"": //text //
  ""tyres_tread_config_id"": //character varying(40) [FK]//
}

entity "**ams_UTILIZATION_RATE_PERCENTAGE_STATUS**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_ASSET_TYRE_TREAD_TYPES**" {
  + ""code"": //character varying(255) [PK]//
  --
  *""label"": //character varying(255) //
  *""description"": //text //
}

entity "**ams_asset_tyres_tread_configs**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""brand_name"": //character varying(100) //
  *""retread_type"": //character varying(100) //
  *""original_tread_depth"": //integer //
  *""width"": //integer //
  *""weight"": //integer //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
}

entity "**ams_ASSET_VEHICLE_BODY_TYPE_STATUSES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_asset_vehicle_body_types**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""label"": //character varying(50) //
  *""description"": //character varying(50) //
  *""status_code"": //character varying(20) [FK]//
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
  ""vehicle_body_type_number"": //character varying(20) //
}

entity "**ams_asset_vehicles**" {
  + ""asset_id"": //character varying(40) [PK][FK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  ""registration_number"": //character varying(100) //
  ""asset_vehicle_body_type_id"": //character varying(40) [FK]//
  ""number_of_tyres"": //bigint //
  ""engine_model"": //character varying(100) //
  ""transmission_model"": //character varying(100) //
  ""vrd_number"": //character varying(100) //
  ""vrd_expiry_date"": //timestamp with time zone //
  ""engine_number"": //character varying(100) //
  ""chassis_number"": //character varying(100) //
  ""gps_device_imei"": //character varying(100) //
  ""registration_certificate_number"": //character varying(100) //
  ""registration_certificate_assign_to"": //character varying(40) //
  ""inspection_book_number"": //character varying(100) //
  ""inspection_book_number_assign_to"": //character varying(40) //
  ""inspection_book_expiry_date"": //timestamp with time zone //
  *""client_id"": //character varying(40) //
  *""vehicle_km"": //numeric(14,2) //
  *""number_of_spare_tyres"": //bigint //
  ""vehicle_hm"": //bigint //
  *""use_kilometer"": //boolean //
  *""use_hourmeter"": //boolean //
  ""vrd_number_assign_to"": //character varying(40) //
  ""updated_by"": //character varying(40) //
  ""created_by"": //character varying(40) //
  ""vehicle_id"": //character varying(40) //
  ""axle_configuration_legacy"": //character varying(255)[] //
  ""max_rtd_diff_tolerance"": //smallint //
  ""axle_configuration"": //jsonb //
  ""last_inspected_at"": //timestamp with time zone //
}

entity "**ams_asset_vehicle_stats_histories**" {
  --
  ""datetime"": //timestamp with time zone //
  ""created_at"": //timestamp with time zone //
  *""asset_id"": //character varying(40) //
  *""client_id"": //character varying(40) //
  ""vehicle_km"": //bigint //
  ""vehicle_hm"": //bigint //
}

entity "**ams_bonus_penalty_target_formations**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""name"": //character varying(255) //
  *""description"": //text //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
}

entity "**ams_bonus_penalty_items**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""asset_tyre_id"": //character varying(40) [FK]//
  *""bonus_penalty_id"": //character varying(40) [FK]//
  *""target_formation_id"": //character varying(40) [FK]//
  *""replacement_reason"": //character varying(20) //
  *""start_km"": //bigint //
  *""end_km"": //bigint //
  *""target_km"": //bigint //
  *""rate_per_km"": //bigint //
  *""max_achievment_km"": //bigint //
  *""achievment_km"": //bigint //
  *""total"": //bigint //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
}

entity "**ams_bonus_penalties**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""bap_number"": //character varying(20) //
  *""asset_vehicle_id"": //character varying(40) [FK]//
  *""grand_total"": //bigint //
  *""driver_user_id"": //character varying(40) //
  *""notes"": //text //
  *""status_code"": //character varying(20) [FK]//
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
  ""approval_user_id"": //character varying(40) //
}

entity "**ams_BONUS_PENALTY_STATUSES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_bonus_penalty_parameters**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""tyre_id"": //character varying(40) [FK]//
  *""target_formation_id"": //character varying(40) [FK]//
  *""target_km"": //bigint //
  *""rate_per_km"": //bigint //
  *""max_achievment_km"": //bigint //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
}

entity "**ams_BRAND_STATUSES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_brands**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""brand_name"": //character varying(20) //
  *""brand_tags"": //character varying(255)[] //
  *""status_code"": //character varying(20) [FK]//
  *""client_id"": //character varying(40) //
  ""brand_number"": //character varying(20) //
}

entity "**ams_brand_custom_category_mapping**" {
  --
  *""brand_id"": //character varying(40) [FK]//
  *""custom_asset_category_id"": //character varying(40) [FK]//
}

entity "**ams_custom_asset_categories**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""name"": //character varying(100) //
  *""description"": //character varying(50) //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
  ""asset_category_code"": //character varying(40) [FK]//
}

entity "**ams_custom_asset_sub_categories**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""custom_asset_category_id"": //character varying(40) [FK]//
  *""name"": //character varying(100) //
  *""description"": //character varying(50) //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
}

entity "**ams_locations**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""name"": //character varying(255) //
  ""is_inventory_location"": //boolean //
  *""address"": //text //
  ""floor"": //character varying(20) //
  ""unit"": //character varying(20) //
  *""status_code"": //character varying(20) [FK]//
  *""description"": //text //
  *""pic_user_id"": //character varying(40) //
  *""map_lat"": //double precision //
  *""map_long"": //double precision //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
  ""location_number"": //character varying(20) //
}

entity "**ams_LOCATION_STATUSES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}

entity "**ams_tyres**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""brand_id"": //character varying(40) [FK]//
  ""pattern_type"": //text //
  ""tyre_size"": //character varying(40) //
  ""original_td"": //numeric(14,2) //
  ""construction_type"": //character varying(20) //
  ""ply_rating"": //numeric(14,2) //
  ""load_rating"": //character varying(40) //
  ""speed_index"": //character varying(10) //
  ""star_rating"": //numeric(14,2) //
  ""tra_code"": //character varying(100) //
  *""client_id"": //character varying(40) //
  ""section_width"": //character varying(20) //
  ""construction"": //character varying(20) //
  ""rim_diameter"": //character varying(20) //
  ""tyre_number"": //character varying(20) //
  ""recommended_rim_size"": //character varying(20) //
}

entity "**ams_vehicles**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""brand_id"": //character varying(40) [FK]//
  ""vehicle_type_code"": //character varying(50) [FK]//
  *""model"": //character varying(50) //
  ""engine_model"": //character varying(50) //
  ""transmission_model"": //character varying(50) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""client_id"": //character varying(40) //
  ""vehicle_number"": //character varying(20) //
  *""axle_configuration_legacy"": //character varying(50)[] //
  ""axle_configuration"": //jsonb //
}

' Relation
"**ams_asset_assignment_groups**"   }--  "**ams_assets**"
"**ams_asset_assignments**"   }--  "**ams_assets**"
"**ams_ASSET_CATEGORY_MAPPING**"   }--  "**ams_ASSET_CATEGORIES**"
"**ams_ASSET_CATEGORY_MAPPING**"   }--  "**ams_ASSET_SUB_CATEGORIES**"
"**ams_asset_components**"   }--  "**ams_assets**"
"**ams_asset_components**"   }--  "**ams_ASSET_COMPONENT_STATUTES**"
"**ams_asset_handover_requests**"   }--  "**ams_ASSET_HANDOVER_REQUEST_STATUSES**"
"**ams_asset_inspection_assignments**"   }--  "**ams_asset_inspections**"
"**ams_asset_inspection_assignments**"   }--  "**ams_ASSET_INSPECTION_ASSIGNMENT_TYPES**"
"**ams_asset_inspection_tyre**"   }--  "**ams_asset_tyres**"
"**ams_asset_inspection_tyre**"   }--  "**ams_asset_inspections**"
"**ams_asset_inspection_vehicle**"   }--  "**ams_asset_vehicles**"
"**ams_asset_inspection_vehicle**"   }--  "**ams_asset_inspections**"
"**ams_asset_inspections**"   }--  "**ams_ASSET_INSPECTION_REFERENCE**"
"**ams_asset_inspections**"   }--  "**ams_ASSET_INSPECTION_STATUSES**"
"**ams_asset_logs**"   }--  "**ams_assets**"
"**ams_asset_logs**"   }--  "**ams_ASSET_LOG_TYPE**"
"**ams_asset_models**"   }--  "**ams_brands**"
"**ams_ASSET_STATUS_REQUEST_REASONS**"   }--  "**ams_ASSET_CATEGORIES**"
"**ams_ASSET_STATUS_REQUEST_SUB_REASONS**"   }--  "**ams_ASSET_STATUS_REQUEST_REASONS**"
"**ams_asset_status_requests**"   }--  "**ams_ASSET_STATUS_REQUEST_REASONS**"
"**ams_asset_status_requests**"   }--  "**ams_assets**"
"**ams_asset_status_requests**"   }--  "**ams_ASSET_STATUS_REQUEST_GRADES**"
"**ams_asset_status_requests**"   }--  "**ams_ASSET_STATUS_REQUEST_TYPES**"
"**ams_asset_status_requests**"   }--  "**ams_ASSET_STATUS_REQUEST_STATUSES**"
"**ams_asset_status_requests**"   }--  "**ams_ASSET_STATUS_REQUEST_SUB_REASONS**"
"**ams_asset_transaction_items**"   }--  "**ams_asset_transactions**"
"**ams_asset_transactions**"   }--  "**ams_ASSET_TRANSACTION_STATUSES**"
"**ams_asset_transactions**"   }--  "**ams_assets**"
"**ams_asset_transactions**"   }--  "**ams_ASSET_TRANSACTION_PAYMENT_METHODS**"
"**ams_asset_transactions**"   }--  "**ams_ASSET_TRANSACTION_CATEGORIES**"
"**ams_asset_transactions**"   }--  "**ams_ASSET_TRANSACTION_TYPES**"
"**ams_asset_tyres**"   }--  "**ams_ASSET_TYRE_METER_CALCULATIONS**"
"**ams_asset_tyres**"   }--  "**ams_tyres**"
"**ams_asset_tyres**"   ||-||  "**ams_assets**"
"**ams_asset_tyres_treads**"   }--  "**ams_asset_tyres_tread_configs**"
"**ams_asset_tyres_treads**"   }--  "**ams_assets**"
"**ams_asset_tyres_treads**"   }--  "**ams_ASSET_TYRE_TREAD_TYPES**"
"**ams_asset_vehicle_body_types**"   }--  "**ams_ASSET_VEHICLE_BODY_TYPE_STATUSES**"
"**ams_asset_vehicles**"   ||-||  "**ams_assets**"
"**ams_asset_vehicles**"   }--  "**ams_asset_vehicle_body_types**"
"**ams_assets**"   }--  "**ams_ASSET_INITIAL_CONDITIONS**"
"**ams_assets**"   }--  "**ams_ASSET_CATEGORIES**"
"**ams_assets**"   }--  "**ams_ASSET_OWNERSHIP_CATEGORIES**"
"**ams_assets**"   }--  "**ams_ASSET_STATUSES**"
"**ams_assets**"   }--  "**ams_asset_models**"
"**ams_assets**"   }--  "**ams_locations**"
"**ams_assets**"   }--  "**ams_brands**"
"**ams_bonus_penalties**"   }--  "**ams_asset_vehicles**"
"**ams_bonus_penalties**"   }--  "**ams_BONUS_PENALTY_STATUSES**"
"**ams_bonus_penalty_items**"   }--  "**ams_bonus_penalties**"
"**ams_bonus_penalty_items**"   }--  "**ams_asset_tyres**"
"**ams_bonus_penalty_items**"   }--  "**ams_bonus_penalty_target_formations**"
"**ams_bonus_penalty_parameters**"   }--  "**ams_tyres**"
"**ams_bonus_penalty_parameters**"   }--  "**ams_bonus_penalty_target_formations**"
"**ams_brand_custom_category_mapping**"   }--  "**ams_custom_asset_categories**"
"**ams_brand_custom_category_mapping**"   }--  "**ams_brands**"
"**ams_brands**"   }--  "**ams_BRAND_STATUSES**"
"**ams_custom_asset_categories**"   }--  "**ams_ASSET_CATEGORIES**"
"**ams_custom_asset_sub_categories**"   }--  "**ams_custom_asset_categories**"
"**ams_linked_asset_vehicle_tyres**"   ||-||  "**ams_linked_assets**"
"**ams_linked_assets**"   }--  "**ams_LINKED_ASSET_TYPE**"
"**ams_linked_assets**"   }--  "**ams_assets**"
"**ams_locations**"   }--  "**ams_LOCATION_STATUSES**"
"**ams_tyres**"   }--  "**ams_brands**"
"**ams_vehicles**"   }--  "**ams_ASSET_VEHICLE_TYPES**"
"**ams_vehicles**"   }--  "**ams_brands**"
"**ins_asset_thresholds**"   }--  "**ams_assets**"
"**tks_TICKET_CATEGORY_asset_category_mapping**"   }--  "**ams_ASSET_CATEGORIES**"
"**tks_TICKET_CATEGORY_custom_asset_category_mapping**"   }--  "**ams_custom_asset_categories**"


@enduml