@startuml
hide circle
skinparam linetype ortho


entity "**uis_CLIENT_PACKAGES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}
entity "**uis_CLIENT_STATUSES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}
entity "**uis_DEPARTMENT_STATUSES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}
entity "**uis_DEVICE_STATUSES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}
entity "**uis_DEVICE_TYPES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}
entity "**uis_OPTIMAX_PACKAGES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}
entity "**uis_PARTNER_STATUSES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}
entity "**uis_PARTNER_TYPES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}
entity "**uis_PERMISSIONS**" {
  + ""code"": //character varying(255) [PK]//
  --
  *""user_permission_category_code"": //character varying(20) [FK]//
  *""label"": //character varying(100) //
  *""description"": //character varying(100) //
  *""display_sequence"": //smallint //
}
entity "**uis_PERMISSION_CATEGORIES**" {
  + ""code"": //character varying(255) [PK]//
  --
  *""title"": //character varying(255) //
  *""subtitle"": //character varying(255) //
  *""icon"": //character varying(255) //
  *""label"": //character varying(255) //
  *""description"": //text //
  *""display_sequence"": //smallint //
}
entity "**uis_PERMISSION_GROUP_TYPES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(100) //
  *""description"": //character varying(100) //
}
entity "**uis_USER_RIGHTS**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""right_name"": //character varying(100) //
  *""description"": //character varying(100) //
}
entity "**uis_USER_ROLE**" {
  + ""code"": //character varying(20) [PK]//
  --
  ""user_role"": //character varying(20) //
  ""description"": //character varying(100) //
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
}
entity "**uis_USER_ROLE_RIGHTS**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""user_right_code"": //character varying(20) [FK]//
  *""user_role_code"": //character varying(20) [FK]//
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
}
entity "**uis_USER_STATUSES**" {
  + ""code"": //character varying(20) [PK]//
  --
  *""label"": //character varying(20) //
  *""description"": //character varying(50) //
}
entity "**uis_clients**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""name"": //character varying(50) //
  ""description"": //character varying(100) //
  *""is_parent_client"": //boolean //
  ""business_sector"": //character varying(255) //
  ""referral_source"": //character varying(20) //
  *""status_code"": //character varying(20) [FK]//
  ""client_alias"": //character varying(30) //
  ""tax_id"": //character varying(255) //
  ""photo"": //character varying(255) //
  ""optimax_package_codes"": //character varying(20)[] //
  ""package_code"": //character varying(20) [FK]//
  ""secondary_photo"": //character varying(255) //
}
entity "**uis_contacts**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""name"": //character varying(255) //
  *""phone_number"": //character varying(50) //
  *""email"": //character varying(50) //
  ""role"": //character varying(20) //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
  ""tax_identity"": //character varying(255) //
  ""identification_number"": //character varying(255) //
}
entity "**uis_departments**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""name"": //character varying(255) //
  *""department_code"": //character varying(20) //
  *""contact_user_id"": //character varying(40) //
  *""status_code"": //character varying(20) [FK]//
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
}
entity "**uis_linked_clients**" {
  + ""parent_id"": //character varying(40) [PK][FK]//
  + ""child_id"": //character varying(40) [PK][FK]//
  --
  *""created_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
}
entity "**uis_partner_contacts**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""partner_id"": //character varying(40) [FK]//
  *""contact_id"": //character varying(40) [FK]//
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
}
entity "**uis_partners**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""name"": //character varying(255) //
  *""service_provided"": //character varying(255) //
  *""status_code"": //character varying(20) [FK]//
  *""address"": //text //
  ""floor"": //character varying(20) //
  ""unit"": //character varying(20) //
  *""map_lat"": //double precision //
  *""map_long"": //double precision //
  *""phone_number_1"": //character varying(50) //
  ""phone_number_2"": //character varying(50) //
  *""email"": //character varying(50) //
  ""notes"": //character varying(255) //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  *""updated_by"": //character varying(40) //
  *""created_by"": //character varying(40) //
  ""tax_identity"": //character varying(255) //
  ""partner_type_code"": //character varying(255) [FK]//
}
entity "**uis_permission_group_rights**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""permission_group_id"": //character varying(40) [FK]//
  *""permission_codes"": //character varying(255)[] //
  *""is_enable"": //boolean //
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp without time zone //
  *""updated_at"": //timestamp without time zone //
  ""deleted_at"": //timestamp without time zone //
  ""permission_category_code"": //character varying(40) [FK]//
  ""created_by"": //character varying(40) //
  ""updated_by"": //character varying(40) //
}
entity "**uis_permission_groups**" {
  + ""id"": //character varying(40) [PK]//
  --
  *""group_name"": //character varying(100) //
  *""description"": //character varying(255) //
  *""permission_group_type_code"": //character varying(20) [FK]//
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp without time zone //
  *""updated_at"": //timestamp without time zone //
  ""deleted_at"": //timestamp without time zone //
  ""created_by"": //character varying(40) //
  ""updated_by"": //character varying(40) //
}
entity "**uis_user_clients**" {
  + ""user_id"": //character varying(40) [PK][FK]//
  + ""client_id"": //character varying(40) [PK][FK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  ""permission_group_id"": //character varying(40) [FK]//
  ""department_id"": //character varying(40) [FK]//
}
entity "**uis_user_devices**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""user_id"": //character varying(40) //
  *""firebase_device_token"": //text //
  *""device_type_code"": //character varying(20) [FK]//
  *""device_type_ref"": //character varying(255) //
  *""status_code"": //character varying(20) [FK]//
  *""client_id"": //character varying(40) //
  *""created_at"": //timestamp with time zone //
  *""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  ""last_activity_date"": //timestamp with time zone //
}
entity "**uis_users**" {
  + ""id"": //character varying(40) [PK]//
  --
  ""created_at"": //timestamp with time zone //
  ""updated_at"": //timestamp with time zone //
  ""deleted_at"": //timestamp with time zone //
  ""first_name"": //character varying(100) //
  ""last_name"": //character varying(100) //
  ""email"": //character varying(50) //
  ""firebase_user_id"": //character varying(100) //
  ""user_role_code"": //character varying(20) [FK]//
  *""status_code"": //character varying(20) [FK]//
  ""reference_id"": //character varying(50) //
  ""photo"": //character varying(255) //
  ""reference_role"": //character varying(50) //
  ""phone_number"": //character varying(50) //
  ""created_by"": //character varying(40) //
  ""updated_by"": //character varying(40) //
}

entity "**uis_DIGISPECT_PACKAGES**" {
  + ""code"": //character varying(40) [PK]//
  --
  *""label"": //character varying(40) //
  *""description"": //character varying(255) //
  *""rank"": //smallint //
  *""limit_days"": //smallint //
}

"**app_approval_requests**"   }--  "**uis_clients**"
"**app_approvals**"   }--  "**uis_clients**"
"**uis_PERMISSIONS**"   }--  "**uis_PERMISSION_CATEGORIES**"
"**uis_USER_ROLE_RIGHTS**"   }--  "**uis_USER_RIGHTS**"
"**uis_USER_ROLE_RIGHTS**"   }--  "**uis_USER_ROLE**"
"**uis_clients**"   }--  "**uis_CLIENT_PACKAGES**"
"**uis_clients**"   }--  "**uis_CLIENT_STATUSES**"
"**uis_departments**"   }--  "**uis_DEPARTMENT_STATUSES**"
"**uis_linked_clients**"   }--  "**uis_clients**"
"**uis_linked_clients**"   }--  "**uis_clients**"
"**uis_partner_contacts**"   }--  "**uis_contacts**"
"**uis_partner_contacts**"   }--  "**uis_partners**"
"**uis_partners**"   }--  "**uis_PARTNER_TYPES**"
"**uis_partners**"   }--  "**uis_PARTNER_STATUSES**"
"**uis_permission_group_rights**"   }--  "**uis_PERMISSION_CATEGORIES**"
"**uis_permission_group_rights**"   }--  "**uis_permission_groups**"
"**uis_permission_groups**"   }--  "**uis_PERMISSION_GROUP_TYPES**"
"**uis_user_clients**"   }--  "**uis_departments**"
"**uis_user_clients**"   }--  "**uis_clients**"
"**uis_user_clients**"   }--  "**uis_users**"
"**uis_user_clients**"   }--  "**uis_permission_groups**"
"**uis_user_devices**"   }--  "**uis_DEVICE_TYPES**"
"**uis_user_devices**"   }--  "**uis_DEVICE_STATUSES**"
"**uis_users**"   }--  "**uis_USER_STATUSES**"
"**uis_users**"   }--  "**uis_USER_ROLE**"

@enduml