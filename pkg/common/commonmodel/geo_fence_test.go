package commonmodel

import (
	"testing"

	"github.com/peterstace/simplefeatures/geom"
)

func TestGeoLine_IsPointsInRoute(t *testing.T) {
	type fields struct {
		LineString geom.LineString
	}
	type args struct {
		point     GeoPoint
		tolerance float64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				LineString: geom.NewLineString(geom.NewSequence([]float64{0, 0, 0, 2}, geom.DimXY)),
			},
			args: args{
				point: GeoPoint{
					Long: 1,
					Lat:  1,
				},
				tolerance: 9,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &GeoLine{
				LineString: tt.fields.LineString,
			}
			if got := a.IsPointsInRoute(tt.args.point, tt.args.tolerance); got != tt.want {
				t.Errorf("GeoLine.IsPointsInRoute() = %v, want %v", got, tt.want)
			}
		})
	}
}
