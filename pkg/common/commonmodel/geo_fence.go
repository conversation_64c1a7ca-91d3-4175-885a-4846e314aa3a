package commonmodel

import (
	"math"

	"github.com/peterstace/simplefeatures/geom"
)

type GeoArea struct {
	geom.Polygon
}

type GeoPoint struct {
	Long float64 `json:"long"`
	Lat  float64 `json:"lat"`
}

func (a *GeoArea) IsPointsInArea(point GeoPoint) bool {
	if a == nil {
		return true
	}

	var inside bool

	coords := a.Coordinates()[0]
	long := point.Long
	lat := point.Lat

	for i := 0; i < coords.Length()-1; i++ {
		first := coords.GetXY(i)
		next := coords.GetXY(i + 1)
		longi := first.X
		lati := first.Y
		longj := next.X
		latj := next.Y

		intersects := ((lati > lat) != (latj > lat)) &&
			(long < (longj-longi)*(lat-lati)/(latj-lati)+longi)
		if intersects {
			inside = !inside
		}
	}

	return inside
}

type GeoLine struct {
	geom.LineString
}

func (a *GeoLine) IsPointsInRoute(point GeoPoint, tolerance float64) bool {
	if a == nil {
		return true
	}

	minDist := math.Inf(1)

	for i := 0; i < a.Coordinates().Length()-1; i++ {
		start := a.Coordinates().Get(i)
		end := a.Coordinates().Get(i + 1)

		dist := distancePointToSegment(point, start, end)
		if dist < minDist {
			minDist = dist
		}
	}

	return minDist < tolerance
}

func distancePointToSegment(p GeoPoint, start, end geom.Coordinates) float64 {
	if start.X == end.X && start.Y == end.Y {
		// Start and end are the same point
		return math.Hypot(p.Long-start.X, p.Lat-start.Y)
	}

	// Vector from start to point
	vx := p.Long - start.X
	vy := p.Lat - start.Y

	// Vector from start to end
	lx := end.X - start.X
	ly := end.Y - start.Y

	// Project vector v onto line segment l
	t := (vx*lx + vy*ly) / (lx*lx + ly*ly)

	if t < 0 {
		// Point projection falls before the start point
		return math.Hypot(vx, vy)
	} else if t > 1 {
		// Point projection falls after the end point
		return math.Hypot(p.Long-end.X, p.Lat-end.Y)
	}

	// Projection point
	projX := start.X + t*lx
	projY := start.Y + t*ly

	// Distance from point to projection
	return math.Hypot(p.Long-projX, p.Lat-projY)
}
