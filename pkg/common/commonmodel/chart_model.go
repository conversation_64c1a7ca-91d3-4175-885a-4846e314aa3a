package commonmodel

import "gopkg.in/guregu/null.v4"

type Chart struct {
	Name       string      `json:"name"`
	Code       null.String `json:"code"`
	Y          float64     `json:"y"`
	X          string      `json:"x,omitempty"`
	ParentCode string      `json:"parent_code,omitempty"`
}

type ChartBar struct {
	X    string      `json:"x"`
	Code null.String `json:"code"`
	Y    float64     `json:"y"`
}
