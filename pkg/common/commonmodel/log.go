package commonmodel

import (
	"time"
)

type Log struct {
	ID            string                 `json:"id"`
	TableName     string                 `json:"table_name"`
	PreviousValue map[string]interface{} `json:"previous_value"`
	NewValue      map[string]interface{} `json:"new_value"`
	ClientID      string                 `json:"client_id"`
	CreatedAt     time.Time              `json:"created_at"`
	CreatedBy     string                 `json:"created_by"`
}
