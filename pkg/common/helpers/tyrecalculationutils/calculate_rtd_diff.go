package tyrecalculationutils

import "math"

func CalculateRTDDiff(RTD1, RTD2, OTD1, OTD2 float64) float64 {
	// Calculate absolute difference between RTD1 and RTD2
	absoluteDifference := math.Abs(RTD1 - RTD2)

	// Calculate average of OTD1 and OTD2
	averageOTD := (OTD1 + OTD2) / 2

	if averageOTD == 0 {
		return 0
	}

	// Calculate the percentage difference
	return (absoluteDifference / averageOTD) * 100
}
