package helpers

func CalculateAverageRTD(RDT1 float64, RDT2 float64, RDT3 float64, RDT4 float64) float64 {
	totalRTD := RDT1
	divider := 1

	if RDT2 > 0 {
		totalRTD += RDT2
		divider += 1
	}

	if RDT3 > 0 {
		totalRTD += RDT3
		divider += 1
	}

	if RDT4 > 0 {
		totalRTD += RDT4
		divider += 1
	}

	averageRTD := totalRTD / float64(divider)

	return averageRTD
}

func CalculateTyreUtilRate(startThreadDepth float64, AverageRTD float64) float64 {
	if startThreadDepth == 0 {
		return 0
	}
	TUR := ((startThreadDepth - AverageRTD) / startThreadDepth) * 100
	return TUR
}

func CalculateTyreProjectedLife(totalKM int, tUR float64) int {
	if tUR == 0 {
		return 0
	}

	projectedLifeKM := int((float64(totalKM) / tUR) * 100)
	return projectedLifeKM
}

func CalculateTyreProjectedLifeHM(totalHM float64, tUR float64) float64 {
	if tUR == 0 {
		return 0
	}

	return (totalHM / tUR) * 100
}

func CalculateTyreTreadRunningCost(startThreadDepth, averageRTD float64, amountCost int) float64 {
	if startThreadDepth == 0 {
		return 0
	}

	costPerMm := float64(amountCost) / float64(startThreadDepth)
	mmUsed := startThreadDepth - averageRTD
	return costPerMm * mmUsed
}
