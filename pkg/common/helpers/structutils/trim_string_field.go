package structutils

import (
	"reflect"
	"strings"
)

func TrimSpaces(s interface{}) {
	// Get the reflection value of the struct
	v := reflect.ValueOf(s).Elem()

	// Loop over the struct's fields
	for i := 0; i < v.NumField(); i++ {
		// Get the field
		field := v.Field(i)

		// Check if the field is a string
		if field.Kind() == reflect.String {
			// Trim leading and trailing spaces
			trimmed := strings.TrimSpace(field.String())

			// Set the trimmed string back to the field
			field.SetString(trimmed)
		}
	}
}
