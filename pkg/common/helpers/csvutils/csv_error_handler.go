package csvutils

import (
	"assetfindr/internal/errorhandler"
	"bytes"
	"encoding/csv"
	"fmt"
	"strconv"

	"github.com/gocarina/gocsv"
)

func BuildErrHandler(headers []string) gocsv.ErrorHandler {
	return func(pe *csv.ParseError) bool {
		if val, ok := pe.Err.(*strconv.NumError); ok {
			pe.Err = errorhandler.ErrBadRequest(
				fmt.Sprintf("%s must be a number, %s is not a number", headers[pe.Column-1], val.Num),
			)
			return false
		}
		return pe.Err == nil
	}
}

func Unmarshal(b []byte, headers []string, out interface{}) error {
	err := gocsv.UnmarshalWithErrorHandler(bytes.NewReader(b), BuildErrHandler(headers), out)
	if err != nil {
		if val, ok := err.(*csv.ParseError); ok {
			return val.Err
		}
	}

	return nil
}
