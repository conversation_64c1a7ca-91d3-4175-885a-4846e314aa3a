package maphelpers

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/jackc/pgtype"
)

func FlattenMap(nested map[string]interface{}) map[string]interface{} {
	result := map[string]interface{}{}
	flattenMap("", nested, result)
	return result
}

func flattenMap(prefix string, nested map[string]interface{}, flat map[string]interface{}) {
	for key, value := range nested {
		switch v := value.(type) {
		case map[string]interface{}:
			flattenMap(prefix+key+".", v, flat)
		case []interface{}:
			for i, item := range v {
				switch item := item.(type) {
				case map[string]interface{}:
					flattenMap(prefix+key+"["+strconv.Itoa(i)+"].", item, flat)
				default:
					flat[prefix+key+"["+strconv.Itoa(i)+"]"] = item
				}
			}
		default:
			flat[prefix+key] = value
		}
	}
}

func JSONBToMapStringToString(jsonb pgtype.JSONB) (map[string]string, error) {
	var result map[string]string
	if jsonb.Status == pgtype.Present {
		err := json.Unmarshal(jsonb.Bytes, &result)
		if err != nil {
			return nil, fmt.Errorf("error unmarshalling JSONB: %w", err)
		}
	} else {
		return nil, fmt.Errorf("JSONB data is not present")
	}
	return result, nil
}
