package tmplhelpers

import (
	"assetfindr/internal/app/task/constants"
	"html/template"
)

type TicketReminderSchedule struct {
	AssetIdent,
	AssetReferenceNumber,
	CustomerName,
	TicketCategoryCode,
	TicketDescription,
	PriorityLevel,
	Assignee,
	RequestedBy string
	ScheduledDateTime, CreatedDateTime string

	RedirectLink template.URL

	IsFromWorkshop bool
}

func (r *TicketReminderSchedule) Normalize() {
	if r.TicketDescription == "" {
		r.TicketDescription = "-"
	}

	if r.Assignee == "" {
		r.Assignee = "-"
	}

	if r.PriorityLevel == constants.TICKET_SEVERITY_NOT_SET {
		r.PriorityLevel = "-"
	}

	if r.CustomerName == "" {
		r.CustomerName = "Customer Name"
	}

	if r.AssetReferenceNumber == "" {
		r.AssetReferenceNumber = "Plat No"
	}
}

// GenerateEmailSubject creates the email subject.
func (r TicketReminderSchedule) GenerateEmailSubject() string {
	if r.IsFromWorkshop {
		return r.GenerateEmailSubjectWorkshop()
	}
	title, _ := ParseStringTemplate(
		"Work Order Scheduled in 3 Days for {{.AssetIdent}}", r)

	return title
}

// GenerateEmailBody creates the email body content.
func (r TicketReminderSchedule) GenerateEmailBody() string {
	if r.IsFromWorkshop {
		return r.GenerateEmailBodyWorkshop()
	}
	body, _ := ParseStringTemplate(
		`This is to inform you that a work order for the following details is scheduled in 3 days. Please review the details below:
		<br><br>
		<table>
			<tr><td>Asset</td>: <td>{{.AssetIdent}}</td></tr>
			<tr><td>Work Order Subject</td>: <td>{{.TicketCategoryCode}}</td></tr>
			<tr><td>Description</td>: <td>{{.TicketDescription}}</td></tr>
			<tr><td>Priority Level</td>: <td>{{.PriorityLevel}}</td></tr>
			<tr><td>Assigned To</td>: <td>{{.Assignee}}</td></tr>
			<tr><td>Scheduled Date & Time</td>: <td>{{.ScheduledDateTime}}</td></tr>
		</table><br>

		For further details and to manage the work order, please click the button below:
		<br><br>
		<a href="{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Work Order Details
		</button>
		</a><br><br>`, r)

	return body
}

// GeneratePushNotifSubject creates the push notification subject.
func (r TicketReminderSchedule) GeneratePushNotifSubject() string {
	if r.IsFromWorkshop {
		return r.GeneratePushNotifSubjectWorkshop()
	}
	return "Scheduled Work Order in 3 Days"
}

// GeneratePushNotifBody creates the push notification body content.
func (r TicketReminderSchedule) GeneratePushNotifBody() string {
	if r.IsFromWorkshop {
		return r.GeneratePushNotifBodyWorkshop()
	}
	body, _ := ParseStringTemplate(
		"{{.TicketCategoryCode}} on {{.AssetIdent}} is coming up in 3 days.", r)

	return body
}

func (r TicketReminderSchedule) GenerateEmailSubjectWorkshop() string {
	title, _ := ParseStringTemplate(
		"Workshop - Work Order Scheduled in 3 Days for {{.AssetReferenceNumber}} - {{.CustomerName}}", r)

	return title
}

// GenerateEmailBody creates the email body content.
func (r TicketReminderSchedule) GenerateEmailBodyWorkshop() string {
	body, _ := ParseStringTemplate(
		`This is to inform you that a work order for the following details is scheduled in 3 days. Please review the details below:
		<br><br>
		<table>
			<tr><td>Vehicle</td>: <td>{{.AssetReferenceNumber}} - {{.CustomerName}}</td></tr>
			<tr><td>Description</td>: <td>{{.TicketDescription}}</td></tr>
			<tr><td>Scheduled Date & Time</td>: <td>{{.ScheduledDateTime}}</td></tr>
			<tr><td>Requested By</td>: <td>{{.RequestedBy}}</td></tr>
			<tr><td>Created Date</td>: <td>{{.CreatedDateTime}}</td></tr>
		</table><br>

		For further details and to manage the work order, please click the button below:
		<br><br>
		<a href="{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Work Order Details
		</button>
		</a><br><br>`, r)

	return body
}

// GeneratePushNotifSubject creates the push notification subject.
func (r TicketReminderSchedule) GeneratePushNotifSubjectWorkshop() string {
	return "Workshop - Scheduled Work Order in 3 Days"
}

// GeneratePushNotifBody creates the push notification body content.
func (r TicketReminderSchedule) GeneratePushNotifBodyWorkshop() string {
	body, _ := ParseStringTemplate(
		"Work Order on {{.AssetReferenceNumber}} - {{.CustomerName}} is coming up in 3 days.", r)

	return body
}
