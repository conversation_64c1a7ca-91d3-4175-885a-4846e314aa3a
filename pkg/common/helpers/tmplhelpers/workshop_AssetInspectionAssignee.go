package tmplhelpers

import (
	"fmt"
	"html/template"
)

type AssetInspectionAssignee struct {
	CustomerName, PlatNo, UserAssigned, TicketDesc, TicketNo, ScheduleDate string
	RedirectWOLink                                                         template.URL
	BodyEmail, BodyPushNotif                                               string
	TitleEmail, TitlePushNotif                                             string
	IsVehicleTyre                                                          bool
	Scenario                                                               int
}

func (u *AssetInspectionAssignee) ConstrucPushNotifSubject() string {
	if u.IsVehicleTyre {
		return u.GenerateSubjectSendNotifVehicleTyre()
	}

	u.TitlePushNotif = "Workshop - Vehicle Inspection Assigned"

	return u.TitlePushNotif
}

func (u *AssetInspectionAssignee) ConstrucPushNotifBody() string {
	if u.IsVehicleTyre {
		return u.GenerateBodySendNotifVehicleTyre()
	}

	u.BodyPushNotif = fmt.Sprintf("Vehicle Inspection for %s - %s", u.PlatNo, u.CustomerName)

	return u.BodyPushNotif
}

func (u *AssetInspectionAssignee) ConstructSubjectEmail() string {
	if u.<PERSON>Tyre {
		return u.GenerateSubjectEmailVehicleTyre()
	}

	title := fmt.Sprintf("Workshop - You Have Been Assigned a Vehicle Inspection for %s - %s", u.PlatNo, u.CustomerName)

	return title
}

func (u *AssetInspectionAssignee) ConstructBodyEmail() string {
	if u.IsVehicleTyre {
		return u.GenerateBodyEmailVehicleTyre()
	}

	var templateTable string

	templateTable = "You've been assigned to a new vehicle inspection. Below are the details for your reference:"

	templateTable += `<br><br><table border="0" cellpadding="10" cellspacing="0"><tr><td>Vehicle</td><td>{{.PlatNo}} - {{.CustomerName}}</td></tr>
	<tr><td>Description</td><td>{{.TicketDesc}}</td></tr>
	<tr><td>Work Order No.</td><td>{{.TicketNo}}</td></tr>`

	templateTable += `<tr><td>Schedule Date</td><td>{{.ScheduleDate}}</td></tr>`

	templateTable += `</table><br>Please click the link below to view and begin the work order:<br><a href="{{.RedirectWOLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Work Order Ticket</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)

	return u.BodyEmail + tableBody
}

//
// Vechile area
//

func (u *AssetInspectionAssignee) GenerateSubjectEmailVehicleTyre() string {

	title := fmt.Sprintf("Workshop - You Have Been Assigned a Tyre Inspection for %s - %s", u.PlatNo, u.CustomerName)

	return title
}

func (u *AssetInspectionAssignee) GenerateBodyEmailVehicleTyre() string {
	var templateTable string

	templateTable = "You've been assigned to a new tyre inspection. Below are the details for your reference:"

	templateTable += `<br><br><table border="0" cellpadding="10" cellspacing="0"><tr><td>Vehicle</td><td>{{.PlatNo}} - {{.CustomerName}}</td></tr>
	<tr><td>Description</td><td>{{.TicketDesc}}</td></tr>
	<tr><td>Work Order No.</td><td>{{.TicketNo}}</td></tr>`

	templateTable += `<tr><td>Scedule Date</td><td>{{.ScheduleDate}}</td></tr>`

	templateTable += `</table><br>Please click the link below to view and begin the work order:<br><a href="{{.RedirectWOLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Work Order Ticket</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)
	return u.BodyEmail + tableBody
}

func (u *AssetInspectionAssignee) GenerateSubjectSendNotifVehicleTyre() string {

	u.TitlePushNotif = "Workshop - You've Been Assigned a Tyre Inspection"

	return u.TitlePushNotif
}
func (u *AssetInspectionAssignee) GenerateBodySendNotifVehicleTyre() string {

	u.BodyPushNotif = fmt.Sprintf("Tyre Inspection for %s - %s. ", u.PlatNo, u.CustomerName)

	return u.BodyPushNotif
}
