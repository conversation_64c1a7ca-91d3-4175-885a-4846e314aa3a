package tmplhelpers

import (
	"fmt"
	"html/template"
	"strings"
)

type NearExpiredAssetTransactionNotificationBody struct {
	Subject, AssetName, UserAssigned, TransactionType, ProviderName, ExpirationDate string
	RedirectLink                                                                    template.URL
	BodyEmail, BodyFirebase                                                         string
	TitleEmail, TitleFirebase                                                       string
}

func ConstructNearExpiredAssetTransactionNotification(bod *NearExpiredAssetTransactionNotificationBody) error {
	// construct notif firebase
	bod.TitleFirebase = fmt.Sprintf("%s Expiring Soon", bod.TransactionType)
	bod.BodyFirebase = fmt.Sprintf("The %s from %s for %s is expiring on %s", strings.ToLower(bod.TransactionType), bod.ProviderName, bod.AssetName, bod.ExpirationDate)

	// construct notif email
	bod.TitleEmail = bod.Subject
	err := bod.constructNearExpiredAssetTransactionNotificationEmailBody(bod)
	if err != nil {
		return err
	}

	return err
}

func (u *NearExpiredAssetTransactionNotificationBody) constructNearExpiredAssetTransactionNotificationEmailBody(bod *NearExpiredAssetTransactionNotificationBody) error {
	// Typing after dear ...
	u.BodyEmail = fmt.Sprintf("This is a friendly reminder that the %s for your asset is expiring soon. Here are the details:<br><br>", strings.ToLower(bod.TransactionType))

	// add table
	templateTable := fmt.Sprintf(`<table border="0" cellpadding="10" cellspacing="0"><tr><td>Asset</td><td> : {{.AssetName}}</td></tr><tr><td>%s Provider</td><td> : {{.ProviderName}}</td></tr><tr><td>%s Expiration Date</td><td> : {{.ExpirationDate}}</td></tr><tr><td>%s Assigned To</td><td> : {{.UserAssigned}}</td></tr></table><br>For more information about the asset, please click below:<br><a href="{{.RedirectLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Asset Detail</button></a>`, bod.TransactionType, bod.TransactionType, bod.TransactionType)
	tableBody, err := ParseStringTemplate(templateTable, u)
	if err != nil {
		return err
	}

	u.BodyEmail += tableBody

	return nil
}
