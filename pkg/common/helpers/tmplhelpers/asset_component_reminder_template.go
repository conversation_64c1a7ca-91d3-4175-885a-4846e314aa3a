package tmplhelpers

import (
	"assetfindr/pkg/common/commonlogger"
	"html/template"
)

type AssetComponentReminderNoAction struct {
	AssetIdent, ComponentName, ComponentSerialNumber, ComponentExpiryDate, ComponentAssignedTo, AssetAssignee string
	RedirectLink                                                                                              template.URL
}

func (a AssetComponentReminderNoAction) GenerateEmailSubject() string {
	title, _ := ParseStringTemplate(
		"[Asset] Reminder: Upcoming Expiry for {{.ComponentName}} of {{.AssetIdent}}", a)

	return title
}

func (a AssetComponentReminderNoAction) GenerateEmailBody() string {
	body, _ := ParseStringTemplate(
		`This is a friendly reminder that a component of your asset is expiring soon. Here are the details:
		<br>
		<br>
		<table>
			<tr><td>Asset</td>: <td> {{.AssetIdent}}</td></tr>
			<tr><td>Component</td>: <td> {{.ComponentName}} - {{.ComponentSerialNumber}}</td></tr>
			<tr><td>Expiry Date</td>: <td> {{.ComponentExpiryDate}}</td></tr>
			<tr><td>Component Assigned To</td>: <td> {{.ComponentAssignedTo}}</td></tr>
			<tr><td>Asset Assigned To</td>: <td> {{.AssetAssignee}}</td></tr>
		</table><br>

		Please take the necessary actions to address this expiry before the date. For more information, you can view the asset details by clicking the button below:
		<br><br>
		<a href = "{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Asset
		</button>
		</a>
		<br>`, a)

	return body
}

func (a AssetComponentReminderNoAction) GeneratePushNotifSubject() string {
	return "Component Expiring Soon"
}

func (a AssetComponentReminderNoAction) GeneratePushNotifBody() string {
	body, _ := ParseStringTemplate(
		`The component {{.ComponentName}} on {{.AssetIdent}} is expiring on {{.ComponentExpiryDate}}.`, a)

	return body
}

// Deprecated
type AssetComponentReminderWithTicket struct {
	AssetComponentReminderNoAction
	RedirectLinkTicket template.URL
	TicketSubject      string
	TicketAssignee     string
}

// Deprecated
func (a AssetComponentReminderWithTicket) GenerateEmailBody() string {
	body, _ := ParseStringTemplate(
		`This is a friendly reminder that a component of your asset is expiring soon. Here are the details:
		<br>
		<br>
		<table>
			<tr><td>Asset</td>: <td> {{.AssetIdent}}</td></tr>
			<tr><td>Component</td>: <td> {{.ComponentName}} - {{.ComponentSerialNumber}}</td></tr>
			<tr><td>Expiry Date</td>: <td> {{.ComponentExpiryDate}}</td></tr>
			<tr><td>Component Assigned To</td>: <td> {{.ComponentAssignedTo}}</td></tr>
		</table><br>

		For more details about the asset, please click below:<br><br>

		<a href = "{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Asset
		</button>
		</a>
		<br><br>

		To ensure everything is in order, a work order has been automatically created:<br><br>

		<table>
		<tr><td>Work Order Subject</td>: <td> {{.TicketSubject}} </td></tr> 
		<tr><td>Work Order Assignee</td>: <td> {{.TicketAssignee}} </td></tr>
		</table>

		You can view the work order details here:<br><br>

		<a href = "{{.RedirectLinkTicket}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Work Order
		</button>
		</a>
		<br>
		
		`, a)

	return body
}

type AssetComponentReminderWithTask struct {
	AssetComponentReminderNoAction
	RedirectLinkTicket template.URL
	TaskSubject        string
	TaskDescription    string
	AssetNo            string
	CustomerName       string
}

func (a AssetComponentReminderWithTask) GeneratePushNotifSubject() string {
	return "Component Expiring Soon"
}

func (a AssetComponentReminderWithTask) GeneratePushNotifBody() string {
	body, _ := ParseStringTemplate(
		`The component {{.ComponentName}} on {{.AssetNo}} - {{.CustomerName}} is expiring on {{.ComponentExpiryDate}}.`, a)

	return body
}

func (a AssetComponentReminderWithTask) GenerateEmailSubject() string {
	title, _ := ParseStringTemplate(
		"Task Reminder: Upcoming Expiry for {{.ComponentName}} of {{.AssetNo}} - {{.CustomerName}}", a)

	return title
}

func (a AssetComponentReminderWithTask) GenerateEmailBody() string {
	body, err := ParseStringTemplate(
		`This is a friendly reminder that a component of the following asset is expiring soon. Here are the details:
		<br>
		<br>
		<table>
			<tr><td>Asset</td>: <td> {{.AssetNo}}</td></tr>
			<tr><td>Customer Name</td>: <td> {{.CustomerName}}</td></tr>
			<tr><td>Component</td>: <td> {{.ComponentName}} - {{.ComponentSerialNumber}}</td></tr>
			<tr><td>Expiry Date</td>: <td> {{.ComponentExpiryDate}}</td></tr>
		</table><br>

		For more details about the asset, please click below:<br><br>

		<a href = "{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Asset
		</button>
		</a>
		<br><br>

		To ensure everything is in order, a task has been automatically created:<br><br>

		<table>
		<tr><td>Task Subject</td>: <td> {{.TaskSubject}} </td></tr> 
		<tr><td>Task Description</td>: <td> <pre>{{.TaskDescription}}</pre> </td></tr> 
		</table>

		You can view the task details here:<br><br>

		<a href = "{{.RedirectLinkTicket}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View task
		</button>
		</a>
		<br>
		
		`, a)
	if err != nil {
		commonlogger.Errorf("Error parsing template: %v", err)
		return ""
	}

	return body
}
