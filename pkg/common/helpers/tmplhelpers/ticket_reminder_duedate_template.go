package tmplhelpers

import (
	"assetfindr/internal/app/task/constants"
	"html/template"
)

type TicketReminderDuedate struct {
	AssetIdent,
	AssetReferenceNumber,
	CustomerName,
	TicketCategoryCode,
	TicketDescription,
	PriorityLevel,
	Assignee string
	DueDateTime    string
	DueLabel       string
	DueLabel2      string
	IsFromWorkshop bool

	RedirectLink template.URL
}

func (r *TicketReminderDuedate) Normalize() {
	if r.TicketDescription == "" {
		r.TicketDescription = "-"
	}

	if r.Assignee == "" {
		r.Assignee = "-"
	}

	if r.PriorityLevel == constants.TICKET_SEVERITY_NOT_SET {
		r.PriorityLevel = "-"
	}
}

// GenerateEmailSubject creates the email subject.
func (r TicketReminderDuedate) GenerateEmailSubject() string {
	if r.IsFromWorkshop {
		return r.generateEmailSubjectWorkshop()
	}
	title, _ := ParseStringTemplate(
		"Reminder: Work Order Due {{.DueLabel}} for {{.AssetIdent}}", r)

	return title
}

// GenerateEmailBody creates the email body content.
func (r TicketReminderDuedate) GenerateEmailBody() string {
	if r.IsFromWorkshop {
		return r.generateEmailBodyWorkshop()
	}
	body, _ := ParseStringTemplate(
		`This is to inform you that a work order for the following details is due {{.DueLabel}}. Please review the details below:
		<br><br>
		<table>
			<tr><td>Asset</td>: <td>{{.AssetIdent}}</td></tr>
			<tr><td>Work Order Subject</td>: <td>{{.TicketCategoryCode}}</td></tr>
			<tr><td>Description</td>: <td>{{.TicketDescription}}</td></tr>
			<tr><td>Priority Level</td>: <td>{{.PriorityLevel}}</td></tr>
			<tr><td>Assigned To</td>: <td>{{.Assignee}}</td></tr>
			<tr><td>Due Date & Time</td>: <td>{{.DueDateTime}}</td></tr>
		</table><br>

		For further details and to manage the work order, please click the button below:
		<br><br>
		<a href="{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Work Order Details
		</button>
		</a><br><br>`, r)

	return body
}

// GeneratePushNotifSubject creates the push notification subject.
func (r TicketReminderDuedate) GeneratePushNotifSubject() string {
	if r.IsFromWorkshop {
		return r.generatePushNotifSubjectWorkshop()
	}
	title, _ := ParseStringTemplate(
		"Work Order Due {{.DueLabel1}}", r)

	return title
}

// GeneratePushNotifBody creates the push notification body content.
func (r TicketReminderDuedate) GeneratePushNotifBody() string {
	if r.IsFromWorkshop {
		return r.generatePushNotifBodyWorkshop()
	}
	body, _ := ParseStringTemplate(
		"{{.TicketCategoryCode}} on {{.AssetIdent}} nearing its deadline.", r)

	return body
}

func (r TicketReminderDuedate) generateEmailSubjectWorkshop() string {
	title, _ := ParseStringTemplate(
		"Workshop - Work Order Scheduled in 3 Days for {{.PlateNo}} - {{.CustomerName}}", r)

	return title
}

func (r TicketReminderDuedate) generateEmailBodyWorkshop() string {
	body, _ := ParseStringTemplate(
		`This is to inform you that a work order for the following details is due {{.DueLabel}}. Please review the details below:
		<br><br>
		<table>
			<tr><td>Vehicle</td>: <td>{{.PlatNo}} - {{.CustomerName}}</td></tr>
			<tr><td>Description</td>: <td>{{.TicketDescription}}</td></tr>
			<tr><td>Assigned To</td>: <td>{{.Assignee}}</td></tr>
			<tr><td>Schedule Date</td>: <td>{{.DueDateTime}}</td></tr>
		</table><br>

		For further details and to manage the work order, please click the button below:
		<br><br>
		<a href="{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Work Order Details
		</button>
		</a><br><br>`, r)

	return body
}

func (r TicketReminderDuedate) generatePushNotifSubjectWorkshop() string {
	title, _ := ParseStringTemplate(
		"Workshop - Work Order Due {{.DueLabel1}}", r)

	return title
}

func (r TicketReminderDuedate) generatePushNotifBodyWorkshop() string {
	body, _ := ParseStringTemplate(
		"Work Order for {{.PlatNo}} - {{.CustomerName}} is due {{.DueLabel1}}.", r)

	return body
}
