package tmplhelpers

import (
	"fmt"
	"html/template"
)

type AssetAssignBod struct {
	AssetAssignName, AssetAssignPlatNo                         string
	AssetBrand, AssetCategory, AssetSubCategory, AssetLocation string

	RedirectAssetLink template.URL

	TitleEmail, BodyEmail         string
	TitlePushNotif, BodyPushNotif string

	Scenario int
}

func (u *AssetAssignBod) ConstrucPushNotifSubject() string {
	title := "New Asset Assigned to You"
	u.TitlePushNotif = title

	return u.TitlePushNotif
}

func (u *AssetAssignBod) ConstrucPushNotifBody() string {
	body := fmt.Sprintf("You've been assigned %s - (%s).", u.AssetAssignName, u.AssetAssignPlatNo)
	u.BodyPushNotif = body

	return u.BodyPushNotif
}

func (u *AssetAssignBod) ConstructSubjectEmail() string {
	title := fmt.Sprintf("You Have a New Asset Assignment: %s - (%s)", u.<PERSON>set<PERSON>sign<PERSON>, u.AssetAssignPlatNo)
	u.TitleEmail = title

	return u.TitleEmail
}

func (u *AssetAssignBod) ConstructBodyEmail() string {
	body := "We would like to inform you that you have been assigned a new asset. Here are the details:"
	var templateTable string

	templateTable = `<br><br><table border="0" cellpadding="10" cellspacing="0">
	<tr><td>Asset</td><td>: {{.AssetAssignName}}</td></tr>
	<tr><td>Asset Brand</td><td>: {{.AssetBrand}}</td></tr>
	<tr><td>Asset Category</td><td>: {{.AssetCategory}}</td></tr>
	<tr><td>Asset Sub-Category</td><td>: {{.AssetSubCategory}}</td></tr>
	<tr><td>Asset Location</td><td>: {{.AssetLocation}}</td></tr>
	</table>`
	templateTable += `<br>To view the asset and its details, please click below:<br><a href="{{.RedirectAssetLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Asset Details</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)

	u.BodyEmail = body + tableBody

	return u.BodyEmail
}
