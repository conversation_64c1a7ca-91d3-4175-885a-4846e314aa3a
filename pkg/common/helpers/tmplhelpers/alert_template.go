package tmplhelpers

import "html/template"

type AlertTemplate struct {
	AlertName           string
	AlertDesc           string
	AssetIdent1         string
	AssetIdent          string
	MappedRecordedValue []AlertRecordedValue
	RedirectLink        template.URL
}

type AlertRecordedValue struct {
	Val   interface{}
	Label string
	Unit  string
}

func (a AlertTemplate) GenerateEmailSubject() string {
	title, _ := ParseStringTemplate(
		"ALERT - {{.AssetIdent1}} - {{.AlertName}}", a)

	return title
}

func (a AlertTemplate) GenerateEmailBody() string {
	body, _ := ParseStringTemplate(
		`The system has detected an alert with details:
		<br>
		<br>
		<table>
			<tr><td>Alert Name</td>: <td> {{.AlertName}}</td></tr>
			<tr><td>Alert Description</td>: <td> {{.AlertDesc}}</td></tr>
			<tr><td>Asset</td>: <td> {{.AssetIdent}}</td></tr>
		</table><br>

		Recorded Value:
		<table>
		{{ range .MappedRecordedValue }}
			<tr>
				<td>{{ .Label }}</td>: <td>{{ .Val }} {{ .Unit }}</td>
			</tr>
		{{ end }}
		</table>
		<br>

		<br><br>
		<a href = "{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Asset
		</button>
		</a>
		<br>`, a)

	return body
}

func (a AlertTemplate) GeneratePushNotifSubject() string {
	title, _ := ParseStringTemplate(
		"Alert Detected", a)

	return title
}

func (a AlertTemplate) GeneratePushNotifBody() string {
	body, _ := ParseStringTemplate(
		`ALERT - {{.AssetIdent1}} - {{.AlertName}}`, a)

	return body
}
