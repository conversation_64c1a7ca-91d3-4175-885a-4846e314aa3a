package tmplhelpers

import (
	"assetfindr/internal/app/asset/constants"
	"fmt"
	"html/template"
)

type AssetHandoverNotificationBody struct {
	Subject, AssetName, TargetUserName, BeforeUserName, HandoverDate, NotifType string
	RedirectLink                                                                template.URL
	BodyEmail, BodyFirebase                                                     string
	TitleEmail, TitleFirebase                                                   string
}

func ConstructAssetHandoverNotification(bod *AssetHandoverNotificationBody) error {
	// construct notif firebase
	if bod.NotifType == constants.ASSET_HANDOVER_NOTIF_TYPE_REQUEST {
		bod.TitleFirebase = "Asset Handover Request"
		bod.BodyFirebase = fmt.Sprintf("You've been handed over %s by %s", bod.AssetName, bod.BeforeUserName)
	}
	if bod.NotifType == constants.ASSET_HANDOVER_NOTIF_TYPE_ACCEPT {
		bod.TitleFirebase = "Handover Accepted"
		bod.BodyFirebase = fmt.Sprintf("%s has accepted the asset %s you handed over", bod.TargetUserName, bod.AssetName)
	}
	if bod.NotifType == constants.ASSET_HANDOVER_NOTIF_TYPE_DECLINE {
		bod.TitleFirebase = "Handover Declined"
		bod.BodyFirebase = fmt.Sprintf("%s has declined the asset %s you handed over", bod.TargetUserName, bod.AssetName)
	}

	// construct notif email
	bod.TitleEmail = bod.Subject
	err := bod.constructAssetHandoverNotificationEmailBody(bod)
	if err != nil {
		return err
	}

	return err
}

func (u *AssetHandoverNotificationBody) constructAssetHandoverNotificationEmailBody(bod *AssetHandoverNotificationBody) error {
	// Typing after dear ...
	if bod.NotifType == constants.ASSET_HANDOVER_NOTIF_TYPE_REQUEST {
		u.BodyEmail = "You have a new handover request that needs your attention. Please review the details below:<br><br>"
	}
	if bod.NotifType == constants.ASSET_HANDOVER_NOTIF_TYPE_ACCEPT {
		u.BodyEmail = "We're pleased to inform you that the handover for the following asset has been accepted:<br><br>"
	}
	if bod.NotifType == constants.ASSET_HANDOVER_NOTIF_TYPE_DECLINE {
		u.BodyEmail = "We would like to inform you that the handover for the following asset has been declined:<br><br>"
	}

	// add table
	templateTable := ""
	if bod.NotifType == constants.ASSET_HANDOVER_NOTIF_TYPE_REQUEST {
		templateTable = `<table border="0" cellpadding="10" cellspacing="0"><tr><td>Asset</td><td> : {{.AssetName}}</td></tr><tr><td>Requested By</td><td> : {{.BeforeUserName}}</td></tr><tr><td>Handover Date and Time</td><td> : {{.HandoverDate}}</td></tr></table><br>To review the request and decide whether to accept or decline, please click the link below:<br><a href="{{.RedirectLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Asset Detail</button></a>`
	}
	if bod.NotifType == constants.ASSET_HANDOVER_NOTIF_TYPE_ACCEPT {
		templateTable = `<table border="0" cellpadding="10" cellspacing="0"><tr><td>Asset</td><td> : {{.AssetName}}</td></tr><tr><td>Accepted By</td><td> : {{.TargetUserName}}</td></tr></table><br>To view the asset details and any relevant information, please click the link below:<br><a href="{{.RedirectLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Asset Detail</button></a>`
	}
	if bod.NotifType == constants.ASSET_HANDOVER_NOTIF_TYPE_DECLINE {
		templateTable = `<table border="0" cellpadding="10" cellspacing="0"><tr><td>Asset</td><td> : {{.AssetName}}</td></tr><tr><td>Declined By</td><td> : {{.TargetUserName}}</td></tr></table><br>To view the asset details and any relevant information, please click the link below:<br><a href="{{.RedirectLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Asset Detail</button></a>`
	}

	tableBody, err := ParseStringTemplate(templateTable, u)
	if err != nil {
		return err
	}

	u.BodyEmail += tableBody

	return nil
}
