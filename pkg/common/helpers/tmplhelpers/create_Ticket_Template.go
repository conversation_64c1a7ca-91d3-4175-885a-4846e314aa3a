package tmplhelpers

import (
	"fmt"
	"html/template"
)

type CreateTicketBody struct {
	Subject, AssetName, TicketDesc, UserAssigned, SeverityLevel string
	RedirectWOLink                                              template.URL
	BodyEmail, BodyFirebase                                     string
	TitleEmail, TitleFirebase                                   string
}

func (u *CreateTicketBody) ConstructTitleEmail() string {
	return fmt.Sprintf("New Work Order Created for Asset: %s", u.AssetName)
}

func (u *CreateTicketBody) ConstructBodyEmail() string {
	// Typing after dear ...
	u.BodyEmail = "We would like to inform you that a new work order has been created for the following asset:<br><br>"

	// add table
	templateTable := `<table border="0" cellpadding="10" cellspacing="0">
	<tr><td>Asset</td><td>{{.AssetName}}</td></tr>
	<tr><td>Work Order Subject</td><td>{{.Subject}}</td></tr>
	<tr><td>Description</td><td><pre>{{.TicketDesc}}</pre></td></tr>
	<tr><td>Priority Level</td><td>{{.SeverityLevel}}</td></tr>
	</table>
	<br>To view full details if this work order abd take required actions, please click the button below.<br><a href="{{.RedirectWOLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Work Order Ticket</button></a>`
	tableBody, _ := ParseStringTemplate(templateTable, u)

	u.BodyEmail += tableBody

	return u.BodyEmail
}

func (u *CreateTicketBody) ConstructTitlePushNotif() string {
	return "New Work Order"
}

func (u *CreateTicketBody) ConstructBodyPushNotif() string {
	body := fmt.Sprintf("%s On %s", u.Subject, u.AssetName)
	return body
}
