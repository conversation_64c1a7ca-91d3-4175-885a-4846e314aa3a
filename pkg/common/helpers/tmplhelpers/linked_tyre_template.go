package tmplhelpers

import (
	"assetfindr/internal/app/asset/constants"
	"fmt"
	"html/template"
)

type LinkedTyreBod struct {
	AssetName, AssetAssignPlatNo string
	AssignedBy                   string

	TyrePosition string
	DateOfLink   string

	RedirectAssetLink template.URL

	TitleEmail, BodyEmail         string
	TitlePushNotif, BodyPushNotif string

	Scenario string
}

func (u *LinkedTyreBod) ConstrucPushNotifSubject() string {

	// link : 1, unlink : 2, replace : 3, nonotif : 0
	var title string
	switch u.Scenario {
	case constants.ASSET_LINKED_STATUS_LINK:
		title = "New Tyres Linked"
	case constants.ASSET_LINKED_STATUS_UNLINK:
		title = "Tyres Unlinked from Asset"
	case constants.ASSET_LINKED_STATUS_REPLACE:
		title = "Linked Tyres Recently Replaced"
	}

	u.TitlePushNotif = title

	return u.TitlePushNotif
}

func (u *LinkedTyreBod) ConstrucPushNotifBody() string {
	// link : 1, unlink : 2, replace : 3, nonotif : 0
	var body string
	switch u.Scenario {
	case constants.ASSET_LINKED_STATUS_LINK:
		body = fmt.Sprintf("New tyres have been linked to %s - (%s) by %s.", u.AssetName, u.AssetAssignPlatNo, u.AssignedBy)
	case constants.ASSET_LINKED_STATUS_UNLINK:
		body = fmt.Sprintf("Tyres Unlinked from %s - (%s) by %s.", u.AssetName, u.AssetAssignPlatNo, u.AssignedBy)
	case constants.ASSET_LINKED_STATUS_REPLACE:
		body = fmt.Sprintf("Linked tyres on %s - (%s) have been replaced by %s.", u.AssetName, u.AssetAssignPlatNo, u.AssignedBy)
	}

	u.BodyPushNotif = body

	return u.BodyPushNotif
}

func (u *LinkedTyreBod) ConstructSubjectEmail() string {
	// link : 1, unlink : 2, replace : 3, nonotif : 0
	var title string
	switch u.Scenario {
	case constants.ASSET_LINKED_STATUS_LINK:
		title = fmt.Sprintf("New Tyres Successfully Linked to %s - (%s)", u.AssetName, u.AssetAssignPlatNo)
	case constants.ASSET_LINKED_STATUS_UNLINK:
		title = fmt.Sprintf("Tyres Unliked from %s - (%s)", u.AssetName, u.AssetAssignPlatNo)
	case constants.ASSET_LINKED_STATUS_REPLACE:
		title = fmt.Sprintf("Linked Tyres on %s - (%s)] have been replaced.", u.AssetName, u.AssetAssignPlatNo)
	}

	u.TitleEmail = title

	return u.TitleEmail
}

func (u *LinkedTyreBod) ConstructBodyEmail() string {

	header := ""
	switch u.Scenario {
	case constants.ASSET_LINKED_STATUS_LINK:
		header = "new tyres have been successfully linked to your asset."
	case constants.ASSET_LINKED_STATUS_UNLINK:
		header = "tyres have been unlinked from your asset."
	case constants.ASSET_LINKED_STATUS_REPLACE:
		header = "tyres on your asset have been replaced."
	}
	body := fmt.Sprintf("We wanted to inform you that %s Here are the details:", header)

	var templateTable string

	templateTable = `<br><br><table border="0" cellpadding="10" cellspacing="0">
	<tr><td>Asset</td><td>{{.AssetName}} - {{.AssetAssignPlatNo}}</td></tr>
	<tr><td>On Tyre Positions:</td><td>{{.TyrePosition}}</td></tr>
	<tr><td>Linked By</td><td>{{.AssignedBy}}</td></tr>
	<tr><td>Date of Link:</td><td>{{.DateOfLink}}</td></tr></table>`
	templateTable += `<br>For more details about your asset and the new tyre, please click below:<br><a href="{{.RedirectAssetLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Asset Details</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)

	u.BodyEmail = body + tableBody

	return u.BodyEmail
}
