package tmplhelpers

import (
	"fmt"
	"html/template"
)

type UpdateTicketBody struct {
	Subject, AssetName, UserAssigned, SeverityLevel, TicketDesc, DueDate string
	PlatNo, CustomerName                                                 string
	IsFromWorkshop                                                       bool
	RedirectWOLink                                                       template.URL
	BodyEmail, BodySendNotif                                             string
	TitleEmail, TitleSendNotif                                           string
}

func (u *UpdateTicketBody) ConstructSubjectSenNotif() string {
	if u.IsFromWorkshop {
		return u.constructSubjectSenNotifWorkshop()
	}

	u.TitleSendNotif = "Updated Work Order"

	return u.TitleSendNotif
}

func (u *UpdateTicketBody) ConstructBodySenNotif() string {
	if u.IsFromWorkshop {
		return u.constructBodySenNotifWorkshop()
	}

	u.BodySendNotif = fmt.Sprintf("The Work Order %s on %s has been edited", u.Subject, u.AssetName)

	return u.BodySendNotif
}

func (u *UpdateTicketBody) ConstructSubjectEmail() string {
	if u.IsFromWorkshop {
		return u.constructSubjectEmailWorkshop()
	}

	u.TitleEmail = fmt.Sprintf("Changes Made to Work Order %s for %s", u.Subject, u.AssetName)

	return u.TitleEmail
}

func (u *UpdateTicketBody) ConstructBodyEmail() string {
	if u.IsFromWorkshop {
		return u.constructBodyEmailWorkshop()
	}

	// Typing after dear ...
	u.BodyEmail = "We wanted to let you know that changes have been made to your work order. Here are the details of the updates:"

	// add spacing
	u.BodyEmail += "<br><br>"

	// add table
	templateTable := `<table border="0" cellpadding="10" cellspacing="0"><tr><td>Asset</td><td>{{.AssetName}}</td></tr><tr><td>Work Order Subject</td><td>{{.Subject}}</td></tr><tr><td>Description</td><td>{{.TicketDesc}}</td></tr><tr><td>Priority</td><td>{{.SeverityLevel}}</td></tr><tr><td>Due Date</td><td>{{.DueDate}}</td></tr></table><br>For a complete overview of the work order and the changes, please click the button below:<br><a href="{{.RedirectWOLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Work Order Ticket</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)

	return u.BodyEmail + tableBody
}

/*
	Workshop Area
*/

func (u *UpdateTicketBody) constructSubjectSenNotifWorkshop() string {
	u.TitleSendNotif = "Workshop - Updated Work Order"

	return u.TitleSendNotif
}

func (u *UpdateTicketBody) constructBodySenNotifWorkshop() string {
	u.BodySendNotif = fmt.Sprintf("The work order for %s - %s has been edited.", u.PlatNo, u.CustomerName)

	return u.BodySendNotif
}

func (u *UpdateTicketBody) constructSubjectEmailWorkshop() string {
	u.TitleEmail = fmt.Sprintf("Workshop - Changes Made to Work Order for a %s - %s", u.PlatNo, u.CustomerName)

	return u.TitleEmail
}

func (u *UpdateTicketBody) constructBodyEmailWorkshop() string {
	// Typing after dear ...
	u.BodyEmail = "We wanted to let you know that changes have been made to your work order. Here are the details of the updates:"

	// add spacing
	u.BodyEmail += "<br><br>"

	// add table
	templateTable := `<table border="0" cellpadding="10" cellspacing="0"><tr><td>Vehicle</td><td>{{.PlatNo}} - {{.CustomerName}}</td></tr><tr><td>Description</td><td>{{.TicketDesc}}</td></tr></table><br>For a complete overview of the work order and the changes, please click the button below:<br><a href="{{.RedirectWOLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Work Order Ticket</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)

	return u.BodyEmail + tableBody
}
