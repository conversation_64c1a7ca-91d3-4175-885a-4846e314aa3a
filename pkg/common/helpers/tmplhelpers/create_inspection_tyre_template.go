package tmplhelpers

import "html/template"

type CreateInspectionTyre struct {
	AssetName,
	AssetIdent,
	InspectionNo,
	InspectionDate,
	InspectorName string
	RedirectLink template.URL
}

// GenerateEmailSubject creates the email subject.
func (i CreateInspectionTyre) GenerateEmailSubject() string {
	title, _ := ParseStringTemplate(
		"New Tyre Inspection Added for {{.AssetName}} - ({{.AssetIdent}})", i)

	return title
}

// GenerateEmailBody creates the email body content.
func (i CreateInspectionTyre) GenerateEmailBody() string {
	body, _ := ParseStringTemplate(
		`We wanted to let you know that a new tyre inspection has been successfully added to your asset. Here are the details:
		<br><br>
		<table>
			<tr><td>Asset</td>: <td>{{.AssetName}} - ({{.AssetIdent}})</td></tr>
			<tr><td>Inspection No.</td>: <td>{{.InspectionNo}}</td></tr>
			<tr><td>Inspection Date</td>: <td>{{.InspectionDate}}</td></tr>
			<tr><td>Inspected By</td>: <td>{{.InspectorName}}</td></tr>
		</table><br>

		For more information about the asset and the tyre inspection, please click the button below:
		<br><br>
		<a href="{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Inspection Result
		</button>
		</a><br>`, i)

	return body
}

// GeneratePushNotifSubject creates the push notification subject.
func (i CreateInspectionTyre) GeneratePushNotifSubject() string {
	return "Tyre Inspection Added"
}

// GeneratePushNotifBody creates the push notification body.
func (i CreateInspectionTyre) GeneratePushNotifBody() string {
	body, _ := ParseStringTemplate(
		"A tyre inspection has been added to {{.AssetName}} - ({{.AssetIdent}}).", i)

	return body
}
