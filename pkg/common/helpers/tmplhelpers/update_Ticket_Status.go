package tmplhelpers

import (
	"assetfindr/internal/app/task/constants"
	"fmt"
	"html/template"
	"strings"

	"github.com/leekchan/accounting"
)

type UpdateTicketStatusBody struct {
	Subject, AssetName, TicketDesc, UserAssigned, SeverityLevel, Note, Status, DueDate string
	Cost                                                                               int
	RedirectWOLink                                                                     template.URL
	BodyEmail, BodyFirebase                                                            string
	TitleEmail, TitleFirebase                                                          string
}

func (u *UpdateTicketStatusBody) GenerateSubjectEmail() string {
	status := u.Status
	switch u.Status {
	case constants.TICKET_STATUS_CODE_IN_PROGRESS_LABEL:
		status = "Now " + constants.TICKET_STATUS_CODE_IN_PROGRESS_LABEL
	case constants.TICKET_STATUS_CODE_ON_HOLD_LABEL:
		status = "Is Currently " + constants.TICKET_STATUS_CODE_ON_HOLD_LABEL
	}

	title := fmt.Sprintf("Update: Your Work Order Is %s", status)

	switch u.Status {
	case constants.TICKET_STATUS_CODE_RESOLVED_LABEL, constants.TICKET_STATUS_CODE_CLOSED_LABEL:
		title = fmt.Sprintf("Work Order %s: %s for %s", status, u.Subject, u.AssetName)
	}

	return title
}

func (u *UpdateTicketStatusBody) GenerateBodyEmail() string {
	status := u.Status
	switch u.Status {
	case constants.TICKET_STATUS_CODE_IN_PROGRESS_LABEL:
		status = "Is Now " + constants.TICKET_STATUS_CODE_IN_PROGRESS_LABEL
	case constants.TICKET_STATUS_CODE_ON_HOLD_LABEL:
		status = "Is Currently " + constants.TICKET_STATUS_CODE_ON_HOLD_LABEL
	case constants.TICKET_STATUS_CODE_RESOLVED_LABEL:
		status = "has been successfully resolved"
	case constants.TICKET_STATUS_CODE_CLOSED_LABEL:
		status = "has been successfully closed"
	}

	opening := fmt.Sprintf("We would like to inform you that the work order for %s %s. Below are the details:", u.AssetName, strings.ToLower(status))

	bodyTable := `
	<table border="0" cellpadding="10" cellspacing="0">
	<tr><tr><td>Asset</td><td>{{.AssetName}}</td></tr>
	<tr><td>Work Order Title</td><td>{{.Subject}}</td></tr>
	<tr><td>Priority</td><td>{{.SeverityLevel}}</td></tr>
	<tr><td>Description</td><td>{{.TicketDesc}}</td></tr>
	`
	if u.Status != constants.TICKET_STATUS_CODE_CLOSED_LABEL {
		bodyTable += `<tr><td>Assigned To</td><td>{{.UserAssigned}}</td></tr>`
	}

	bodyTable += `<tr><td>Due Date</td><td>{{.DueDate}}</td></tr>`
	ac := accounting.Accounting{Thousand: "."}

	cost := fmt.Sprintf("Rp %v", ac.FormatMoney(u.Cost))

	switch u.Status {
	case constants.TICKET_STATUS_CODE_RESOLVED_LABEL:
		bodyTable += `<tr><td>Resolution Summary</td><td>{{.Note}}</td></tr>
		<tr><td>Cost Details</td><td>` + cost + `</td></tr>`
	case constants.TICKET_STATUS_CODE_CLOSED_LABEL:
		bodyTable += `<tr><td>Resolution Notes</td><td>{{.Note}}</td></tr>`
	}

	bodyTable += `</table><br>To view the work order details and understand the current status, please click the link below:<br><a href="{{.RedirectWOLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Work Order Ticket</button></a>`

	renderedText, _ := ParseStringTemplate(bodyTable, u)

	return opening + renderedText
}

func (u *UpdateTicketStatusBody) GenerateSubjectPushNotif() string {
	return "Work Order " + u.Status
}

func (u *UpdateTicketStatusBody) GenerateBodyPushNotif() string {
	status := u.Status
	if u.Status == constants.TICKET_STATUS_CODE_IN_PROGRESS_LABEL {
		status = "now " + constants.TICKET_STATUS_CODE_IN_PROGRESS_LABEL
	}

	bodyPush := fmt.Sprintf("Your work order %s on %s is %s", u.Subject, u.AssetName, status)
	if u.Status == constants.TICKET_STATUS_CODE_CLOSED_LABEL {
		bodyPush = fmt.Sprintf("The work order %s on %s has been marked as closed by the requester.", u.Subject, u.AssetName)
	}

	return bodyPush
}
