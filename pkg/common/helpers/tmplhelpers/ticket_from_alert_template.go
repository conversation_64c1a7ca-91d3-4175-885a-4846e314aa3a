package tmplhelpers

import "html/template"

type TicketFromAlert struct {
	AlertState, AlertName, Parameter string
	RedirectLink                     template.URL
}

func (a TicketFromAlert) GenerateEmailSubject() string {
	title, _ := ParseStringTemplate(
		"New Work Order Created: {{.AlertState}} for {{.AlertName}}", a)

	return title
}

func (a TicketFromAlert) GenerateEmailBody() string {
	body, _ := ParseStringTemplate(
		`The system has detected an issue and created a new work order based on the following alert details:
		<br>
		<br>
		<table>
			<tr><td>Alert Name</td>: <td> {{.AlertName}}</td></tr>
			<tr><td>Alert State</td>: <td> {{.AlertState}}</td></tr>
			<tr><td>Affected Parameter</td>: <td> {{.Parameter}}</td></tr>
		</table><br>

		You have been assigned to address this work order. Please click the button below to view the work order details:
		<br><br>
		<a href = "{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Work Order
		</button>
		</a>
		<br>`, a)

	return body
}

func (a TicketFromAlert) GeneratePushNotifSubject() string {
	title, _ := ParseStringTemplate("New Alert-triggered Work Order: {{.AlertName}}", a)
	return title
}

func (a TicketFromAlert) GeneratePushNotifBody() string {
	body, _ := ParseStringTemplate(
		`A new work order has been generated on alert: {{.AlertName}}. The parameter {{.Parameter}} has triggered a {{.AlertState}}. You have been assigned to this task.`, a)

	return body
}
