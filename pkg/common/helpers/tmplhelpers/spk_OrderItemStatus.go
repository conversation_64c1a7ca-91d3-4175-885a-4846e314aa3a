package tmplhelpers

import (
	"assetfindr/internal/app/inventory/constants"
	"fmt"
	"html/template"
)

type SPKOrderItemStatus struct {
	CustomerName, PlatNo, UserAssigned, TicketDesc, TicketNo string
	SPKItemName, Notes                                       string
	RedirectWOLink                                           template.URL
	BodyEmail, BodyPushNotif                                 string
	TitleEmail, TitlePushNotif                               string
	StatusCode                                               string
}

func (u *SPKOrderItemStatus) GenerateSubjectEmail() string {
	u.TitleEmail = fmt.Sprintf("Workshop - Your SPK Item for %s - %s Is Now %s", u.PlatNo, u.CustomerName, constants.MapOrderItemStatusCodeToLabel()[u.StatusCode])

	if u.StatusCode == constants.ORDER_ITEM_STATUS_CODE_DONE {
		u.TitleEmail = fmt.Sprintf("Workshop - Your SPK Item for %s - %s is Marked as Done", u.PlatNo, u.CustomerName)
	}

	return u.TitleEmail
}

func (u *SPKOrderItemStatus) GenerateBodyEmail() string {
	var templateTable string

	templateTable = fmt.Sprintf(`We want to inform you that the following SPK item is now %s. Below are the details for your reference:`, constants.MapOrderItemStatusCodeToLabel()[u.StatusCode])
	if u.StatusCode == constants.ORDER_ITEM_STATUS_CODE_DONE {
		templateTable = `We want to inform you that the following SPK item is marked as done. Below are the details for your reference:`
	}

	templateTable += `<br><br><table border="0" cellpadding="10" cellspacing="0">
	<tr><td>Vehicle</td><td>{{.PlatNo}} - {{.CustomerName}}</td></tr>
	<tr><td>Description</td><td>{{.TicketDesc}}</td></tr>
	<tr><td>SPK Item Name</td><td>{{.SPKItemName}}</td></tr>
	<tr><td>Assign To</td><td>{{.UserAssigned}}</td></tr>
	<tr><td>Notes</td><td>{{.Notes}}</td></tr>`

	templateTable += `</table><br>To view the work order details and track its progress, please click the link below:<br><a href="{{.RedirectWOLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Work Order Ticket</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)

	return u.BodyEmail + tableBody
}

func (u *SPKOrderItemStatus) GenerateSubjectSendNotif() string {
	u.TitlePushNotif = fmt.Sprintf("Workshop - SPK Item %s", constants.MapOrderItemStatusCodeToLabel()[u.StatusCode])

	return u.TitlePushNotif
}
func (u *SPKOrderItemStatus) GenerateBodySendNotif() string {
	u.BodyPushNotif = fmt.Sprintf("Your work order for %s - %s is now %s.", u.PlatNo, u.CustomerName, constants.MapOrderItemStatusCodeToLabel()[u.StatusCode])

	if u.StatusCode == constants.ORDER_ITEM_STATUS_CODE_DONE {
		u.BodyPushNotif = fmt.Sprintf("Your work order for %s - %s is now marked as Done.", u.PlatNo, u.CustomerName)
	}

	return u.BodyPushNotif
}
