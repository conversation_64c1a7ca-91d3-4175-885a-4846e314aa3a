package tmplhelpers

import (
	"html/template"
)

type TaskReminder struct {
	TaskSubject       string
	TaskDescription   string
	ScheduledDateTime string
	RelatedAsset      string
	RedirectLink      template.URL
}

func (t *TaskReminder) Normalize() {
	if t.TaskDescription == "" {
		t.TaskDescription = "-"
	}

	if t.RelatedAsset == "" {
		t.RelatedAsset = "-"
	}
}

const (
	TASK_REMINDER_TODAY     string = "TODAY"
	TASK_REMINDER_TOMORROW  string = "TOMORROW"
	TASK_REMINDER_YESTERDAY string = "YESTERDAY"
)

func (t TaskReminder) GenerateEmailSubject(typeCode string) string {
	switch typeCode {
	case TASK_REMINDER_TODAY:
		return t.GenerateEmailSubjectToday()
	case TASK_REMINDER_TOMORROW:
		return t.GenerateEmailSubjectTomorrow()
	case TASK_REMINDER_YESTERDAY:
		return t.GenerateEmailSubjectYesterday()
	default:
		return ""
	}
}

func (t TaskReminder) GenerateEmailBody(typeCode string) string {
	switch typeCode {
	case TASK_REMINDER_TODAY:
		return t.GenerateEmailBodyToday()
	case TASK_REMINDER_TOMORROW:
		return t.GenerateEmailBodyTomorrow()
	case TASK_REMINDER_YESTERDAY:
		return t.GenerateEmailBodyYesterday()
	default:
		return ""
	}

}
func (t TaskReminder) GeneratePushNotifSubject(typeCode string) string {
	switch typeCode {
	case TASK_REMINDER_TODAY:
		return t.GeneratePushNotifSubjectToday()
	case TASK_REMINDER_TOMORROW:
		return t.GeneratePushNotifSubjectTomorrow()
	case TASK_REMINDER_YESTERDAY:
		return t.GeneratePushNotifSubjectYesterday()
	default:
		return ""
	}

}
func (t TaskReminder) GeneratePushNotifBody(typeCode string) string {
	switch typeCode {
	case TASK_REMINDER_TODAY:
		return t.GeneratePushNotifBodyToday()
	case TASK_REMINDER_TOMORROW:
		return t.GeneratePushNotifBodyTomorrow()
	case TASK_REMINDER_YESTERDAY:
		return t.GeneratePushNotifBodyYesterday()
	default:
		return ""
	}

}

func (t TaskReminder) GenerateEmailSubjectToday() string {
	title, _ := ParseStringTemplate(
		"[To-Do List] Task {{.TaskSubject}} Scheduled fo Today", t)

	return title
}

func (t TaskReminder) GenerateEmailBodyToday() string {
	body, _ := ParseStringTemplate(
		`This is to inform you that a task for the following details is scheduled today. Please review the details below:
        <br>
        <br>
        <table>
            <tr><td>Subject</td>: <td>{{.TaskSubject}}</td></tr>
            <tr><td>Description</td>: <td>{{.TaskDescription}}</td></tr>
            <tr><td>Related Asset</td>: <td>{{.RelatedAsset}}</td></tr>
            <tr><td>Scheduled Date & Time</td>: <td>{{.ScheduledDateTime}}</td></tr>
        </table>
        <br>

        For further details, please click the link below:
        <br><br>
        <a href="{{.RedirectLink}}">
        <button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
        View Task Details
        </button>
        </a>
        <br>`, t)

	return body
}

func (t TaskReminder) GeneratePushNotifSubjectToday() string {
	return "Task from To-Do List Scheduled for Today"
}

func (t TaskReminder) GeneratePushNotifBodyToday() string {
	body, _ := ParseStringTemplate(
		`{{.TaskSubject}} is scheduled for today at {{.ScheduledDateTime}} {{ if ne .RelatedAsset "-" }}for {{.RelatedAsset}}{{ end }}`, t)

	return body
}

// Tomorow
func (t TaskReminder) GenerateEmailSubjectTomorrow() string {
	title, _ := ParseStringTemplate(
		"[To-Do List] Task {{.TaskSubject}} Scheduled fo Tomorrow", t)

	return title
}

func (t TaskReminder) GenerateEmailBodyTomorrow() string {
	body, _ := ParseStringTemplate(
		`This is to inform you that a task for the following details is scheduled Tomorrow. Please review the details below:
        <br>
        <br>
        <table>
            <tr><td>Subject</td>: <td>{{.TaskSubject}}</td></tr>
            <tr><td>Description</td>: <td>{{.TaskDescription}}</td></tr>
            <tr><td>Related Asset</td>: <td>{{.RelatedAsset}}</td></tr>
            <tr><td>Scheduled Date & Time</td>: <td>{{.ScheduledDateTime}}</td></tr>
        </table>
        <br>

        For further details, please click the link below:
        <br><br>
        <a href="{{.RedirectLink}}">
        <button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
        View Task Details
        </button>
        </a>
        <br>`, t)

	return body
}

func (t TaskReminder) GeneratePushNotifSubjectTomorrow() string {
	return "Task from To-Do List Scheduled for Tomorrow"
}

func (t TaskReminder) GeneratePushNotifBodyTomorrow() string {
	body, _ := ParseStringTemplate(
		`{{.TaskSubject}} is scheduled for Tomorrow at {{.ScheduledDateTime}} {{ if ne .RelatedAsset "-" }} for {{.RelatedAsset}}{{ end }}`, t)
	return body
}

// End Tomorrow

// Yesterday
func (t TaskReminder) GenerateEmailSubjectYesterday() string {
	title, _ := ParseStringTemplate(
		"[To-Do List] Pending Task {{.TaskSubject}} from Yesterday", t)

	return title
}

func (t TaskReminder) GenerateEmailBodyYesterday() string {
	body, _ := ParseStringTemplate(
		`This is a reminder that the following task was scheduled yesterday but has not been marked as completed. Please review the details and take necessary action:
        <br>
        <br>
        <table>
            <tr><td>Subject</td>: <td>{{.TaskSubject}}</td></tr>
            <tr><td>Description</td>: <td>{{.TaskDescription}}</td></tr>
            <tr><td>Related Asset</td>: <td>{{.RelatedAsset}}</td></tr>
            <tr><td>Scheduled Date & Time</td>: <td>{{.ScheduledDateTime}}</td></tr>
        </table>
        <br>

        For further details, please click the link below:
        <br><br>
        <a href="{{.RedirectLink}}">
        <button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
        View Task Details
        </button>
        </a>
        <br>`, t)

	return body
}

func (t TaskReminder) GeneratePushNotifSubjectYesterday() string {
	return "Pending Scheduled Task from Yesterday"
}

func (t TaskReminder) GeneratePushNotifBodyYesterday() string {
	body, _ := ParseStringTemplate(
		`{{.TaskSubject}} is scheduled for Yesterday at {{.ScheduledDateTime}} {{ if ne .RelatedAsset "-" }}for {{.RelatedAsset}}{{ end }}`, t)

	return body
}

// End Yesterday
