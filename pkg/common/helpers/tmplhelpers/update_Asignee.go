package tmplhelpers

import (
	"fmt"
	"html/template"
)

type UpdateTicketAsigneeBod struct {
	Subject, CustomerName, PlatNo, AssetName, UserAssigned, PreviousAssignee, SeverityLevel, TicketDesc, ScheduleDate, DueDate, Note, Status string
	RedirectWOLink                                                                                                                           template.URL
	BodyEmail,

	BodyPushNotif string
	TitleEmail, TitlePushNotif string
	Scenario                   int
	IsFromWorkshop             bool
}

func (u *UpdateTicketAsigneeBod) ConstrucPushNotifSubject() string {
	if u.IsFromWorkshop {
		return u.constructSubjectSendNotifWorkshop()
	}

	u.TitlePushNotif = "Work Order Reassigned"

	if u.Scenario == 1 || u.Scenario == 2 {
		u.TitlePushNotif = "You've Been Assign a Work Order"
	}

	return u.TitlePushNotif
}

func (u *UpdateTicketAsigneeBod) ConstrucPushNotifBody() string {
	if u.IsFromWorkshop {
		return u.constructBodySendNotifWorkshop()
	}

	switch u.Scenario {
	case 1, 2:

		u.BodyPushNotif = fmt.Sprintf("%s on %s", u.Subject, u.AssetName)
	case 3, 4:

		u.BodyPushNotif = fmt.Sprintf("The assignee for your work order %s on %s has been changed to %s.", u.Subject, u.AssetName, u.UserAssigned)
	}

	return u.BodyPushNotif
}

func (u *UpdateTicketAsigneeBod) ConstructSubjectEmail() string {
	if u.IsFromWorkshop {
		return u.constructSubjectEmailWorkshop()
	}

	u.TitleEmail = fmt.Sprintf("You Have Been Assigned a Work Order for %s", u.AssetName)

	if u.Scenario == 3 {
		u.TitleEmail = fmt.Sprintf("Your Work Order for  %s Has Been Reassigned", u.AssetName)
	}

	return u.TitleEmail
}

func (u *UpdateTicketAsigneeBod) ConstructBodyEmail() string {
	if u.IsFromWorkshop {
		return u.constructBodyEmailWorkshop()
	}

	switch u.Scenario {
	case 1:
		u.BodyEmail = "You`ve been assigned to a new work order. Below are the details for your reference:"
	case 2, 4:
		u.BodyEmail = "We would like to inform you that the work order has been reassigned. Below are the details for your reference:"
	case 3:
		u.BodyEmail = "We would like to inform you that the work order has been reassigned. Below are the details for your reference:"
	}

	var templateTable string

	templateTable = `<br><br><table border="0" cellpadding="10" cellspacing="0"><tr><td>Asset</td><td>{{.AssetName}}</td></tr><tr><td>Work Order Subject</td><td>{{.Subject}}</td></tr><tr><td>Description</td><td>{{.TicketDesc}}</td></tr><tr><td>Priority Level</td><td>{{.SeverityLevel}}</td></tr>`
	if u.Scenario == 1 {
		templateTable += `<tr><td>Scheduled Date</td><td>{{.ScheduleDate}}</td></tr>`
	} else {
		templateTable += `<tr><td>New Assigned To</td><td><b>{{.UserAssigned}}</b></td></tr>`
	}
	templateTable += `<tr><td>Due Date</td><td>{{.DueDate}}</td></tr></table><br>Please click the link below to view and begin the work order:<br><a href="{{.RedirectWOLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Work Order Ticket</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)

	return u.BodyEmail + tableBody
}

//
// Workshop area
//

func (u *UpdateTicketAsigneeBod) constructSubjectEmailWorkshop() string {
	switch u.Scenario {
	case 1:
		u.TitleEmail = fmt.Sprintf("Workshop - You Have Been Assigned a Work Order for %s - %s", u.PlatNo, u.CustomerName)
	case 3:
		u.TitleEmail = fmt.Sprintf("Workshop - Your Work Order for %s - %s Has Been Reassigned", u.PlatNo, u.CustomerName)
	default:
		u.TitleEmail = `Workshop - Your Work Order Has Been Reassigned`
	}

	return u.TitleEmail
}

func (u *UpdateTicketAsigneeBod) constructBodyEmailWorkshop() string {
	var templateTable string
	switch u.Scenario {
	case 1:
		templateTable = `You've been assigned to a new work order. Below are the details for your reference:`
	default:
		templateTable = `We would like to inform you that the work order has been reassigned. Below are the details for your reference:`
	}

	templateTable += `<br><br><table border="0" cellpadding="10" cellspacing="0"><tr><td>Vehicle</td><td>{{.PlatNo}} - {{.CustomerName}}</td></tr><tr><td>Description</td><td>{{.TicketDesc}}</td></tr>`

	switch u.Scenario {
	case 1:
		templateTable += `<tr><td>Scheduled Date</td><td>{{.ScheduleDate}}</td></tr>`
	case 3:
		templateTable += `<tr><td>Previous Assignee</td><td>{{.PreviousAssignee}}</td></tr>
	<tr><td>New Assign To</td><td>{{.UserAssigned}}</td></tr>`
	default:
		templateTable += `<tr><td>New Assign To</td><td>{{.UserAssigned}}</td></tr>`
	}

	templateTable += `</table><br>Please click the link below to view and begin the work order:<br><a href="{{.RedirectWOLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Work Order Ticket</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)

	return u.BodyEmail + tableBody
}

func (u *UpdateTicketAsigneeBod) constructSubjectSendNotifWorkshop() string {
	u.TitlePushNotif = "Workshop - Work Order Reassigned"
	if u.Scenario == 1 {
		u.TitlePushNotif = "Workshop - You've Been Assigned a Work Order"
	}

	return u.TitlePushNotif
}
func (u *UpdateTicketAsigneeBod) constructBodySendNotifWorkshop() string {
	u.BodyPushNotif = fmt.Sprintf("The assignee for your work order %s - %s has been changed to %s.", u.PlatNo, u.CustomerName, u.UserAssigned)

	switch u.Scenario {
	case 1:
		u.BodyPushNotif = fmt.Sprintf("Work Order for %s - %s.", u.PlatNo, u.CustomerName)
	case 3:
		u.BodyPushNotif = fmt.Sprintf("The assignee for your work order %s - %s has been changed from %s to %s.", u.PlatNo, u.CustomerName, u.PreviousAssignee, u.UserAssigned)
	}

	return u.BodyPushNotif
}
