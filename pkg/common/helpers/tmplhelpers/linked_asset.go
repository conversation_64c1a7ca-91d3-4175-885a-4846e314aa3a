package tmplhelpers

import (
	"fmt"
	"html/template"
)

type LinkedAssetBod struct {
	CurrentAssetName, AssetChildPlatNo  string
	LinkedAssetName, AssetParrentPlatNo string

	CurrentAssetAssignTo, LinkedAssetAssignTo string

	RedirectCurrentAssetLink, RedirectLinkedAssetLink template.URL

	TitleEmail, BodyEmail         string
	TitlePushNotif, BodyPushNotif string
}

func (u *LinkedAssetBod) ConstrucPushNotifSubject() string {
	u.TitlePushNotif = "New Link Asset Added"

	return u.TitlePushNotif
}

func (u *LinkedAssetBod) ConstrucPushNotifBody() string {
	u.BodyPushNotif = fmt.Sprintf("%s - (%s) is now linked to %s - (%s) by %s.", u.CurrentAssetName, u.AssetChildPlatNo, u.LinkedAssetName, u.AssetParrentPlatNo, u.LinkedAssetAssignTo)

	return u.BodyPushNotif
}

func (u *LinkedAssetBod) ConstructSubjectEmail() string {
	u.TitleEmail = fmt.Sprintf("New Asset Linked to %s - (%s)", u.LinkedAssetName, u.AssetParrentPlatNo)

	return u.TitleEmail
}

func (u *LinkedAssetBod) ConstructBodyEmail() string {

	u.BodyEmail = `We wanted to inform you that a new asset has been successfully linked to your current asset. Here are the details:`

	var templateTable string

	templateTable = `<br><br><table border="0" cellpadding="10" cellspacing="0">
	<tr><td>Current Asset</td><td>{{.CurrentAssetName}}</td></tr>
	<tr><td>Current Asset Assign To</td><td>{{.CurrentAssetAssignTo}}</td></tr></table>`
	templateTable += `<br>Click link to view current asset details:<br><a href="{{.RedirectCurrentAssetLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Current Asset Details</button></a>`

	templateTable += `<table border="0" cellpadding="10" cellspacing="0">
	<tr><td>Linked Asset</td><td>{{.LinkedAssetName}} - {{.AssetParrentPlatNo}}</td></tr>
	<tr><td>Linked Asset Assign To</td><td>{{.LinkedAssetAssignTo}}</td></tr></table>`
	templateTable += `<br>Click link to view linked asset details:<br><a href="{{.RedirectLinkedAssetLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Current Asset Details</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)

	return u.BodyEmail + tableBody
}
