package tmplhelpers

import (
	"html/template"
	"time"
)

type GeoFenceInOut struct {
	AssetIdent      string
	GeoFenceStatus  string
	GeoFenceStatus1 string
	GeoFenceName    string
	RedirectLink    template.URL
	Latitude        float64
	Longitude       float64
	AssigneeName    string
	EventTime       time.Time
}

func (a GeoFenceInOut) GenerateEmailSubject() string {
	subject, _ := ParseStringTemplate(
		"[Alert] Asset {{.GeoFenceStatus1}} Geofence Area Notification - {{.AssetIdent}}", a)
	return subject
}

func (a GeoFenceInOut) GenerateEmailBody() string {
	a.EventTime = a.EventTime.In(time.Local)
	body, _ := ParseStringTemplate(
		`Geofence event Detected
		The Asset has {{.GeoFenceStatus1}} the geofenced area.
		<br><br>
		<table>
			<tr><td>Date/Time</td>:<td>{{.EventTime.Format "Monday, 02-Jan-06 15:04:05"}}UTC+7</td></tr>
			<tr><td>Asset</td>:<td>{{.AssetIdent}}</td></tr>
			<tr><td>Geofence Name</td>:<td>{{.GeoFenceName}}</td></tr>
			<tr><td>Location</td>:<td>{{.Latitude}},{{.Longitude}} (<a href="https://www.google.com/maps/place/{{.Latitude}},{{.Longitude}}">View on Map</a>)</td></tr>
			<tr><td>Asset Assignee</td>:<td>{{.AssigneeName}}</td></tr>
		</table>
		<br>
		<a href = "{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Playback
		</button>
		</a>
		<br>`, a)

	return body
}

func (a GeoFenceInOut) GeneratePushNotifSubject() string {
	body, _ := ParseStringTemplate("Asset {{.GeoFenceStatus}} Geofence alert", a)
	return body
}

func (a GeoFenceInOut) GeneratePushNotifBody() string {
	body, _ := ParseStringTemplate(
		`{{.AssetIdent}} is {{.GeoFenceStatus}} of geofence {{.GeoFenceName}} boundary now.`, a)

	return body
}
