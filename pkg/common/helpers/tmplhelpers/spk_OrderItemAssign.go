package tmplhelpers

import (
	"fmt"
	"html/template"
)

type SPKOrderItemAssign struct {
	CustomerName, PlatNo, UserAssigned, TicketDesc, TicketNo string
	SPKItemName, ScheduleDate                                string
	RedirectWOLink                                           template.URL
	BodyEmail, BodyPushNotif                                 string
	TitleEmail, TitlePushNotif                               string
	Scenario                                                 int
}

func (u *SPKOrderItemAssign) GenerateSubjectEmail() string {
	u.TitleEmail = fmt.Sprintf("Workshop - SPK Item for %s - %s Has Been Reassigned", u.PlatNo, u.CustomerName)

	if u.Scenario == 4 {
		u.TitleEmail = fmt.Sprintf("Workshop - You Have Been Assigned a SPK Item for %s - %s", u.PlatNo, u.CustomerName)
	}

	return u.TitleEmail
}

func (u *SPKOrderItemAssign) GenerateBodyEmail() string {
	var templateTable string

	templateTable = `We would like to inform you that the SPK item has been reassigned. Below are the details for your reference:`
	if u.Scenario == 4 {
		templateTable = "You've been assigned to a new SPK item. Below are the details for your reference:"
	}

	templateTable += `<br><br><table border="0" cellpadding="10" cellspacing="0">
	<tr><td>Vehicle</td><td>{{.PlatNo}} - {{.CustomerName}}</td></tr>
	<tr><td>Work Order No.</td><td>{{.TicketNo}}</td></tr>
	<tr><td>Description</td><td>{{.TicketDesc}}</td></tr>
	<tr><td>SPK Item Name</td><td>{{.SPKItemName}}</td></tr>`

	if u.Scenario == 4 {
		templateTable += `<tr><td>Scheduled Date</td><td>{{.ScheduleDate}}</td></tr>`
	} else {
		templateTable += `<tr><td>New Assign To</td><td>{{.UserAssigned}}</td></tr>`
	}

	templateTable += `</table><br>Please click the link below to view and begin the work order:<br><a href="{{.RedirectWOLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Work Order Ticket</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)

	return u.BodyEmail + tableBody
}

func (u *SPKOrderItemAssign) GenerateSubjectSendNotif() string {
	u.TitlePushNotif = "Workshop - You've Been Assigned a SPK Item"
	if u.Scenario == 4 {
		u.TitlePushNotif = "Workshop - You've Been Assigned a SPK Item"
	}

	return u.TitlePushNotif
}
func (u *SPKOrderItemAssign) GenerateBodySendNotif() string {
	u.BodyPushNotif = fmt.Sprintf("SPK Item %s for %s - %s.", u.SPKItemName, u.PlatNo, u.CustomerName)

	return u.BodyPushNotif
}
