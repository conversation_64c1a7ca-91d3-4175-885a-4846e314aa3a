package tmplhelpers

import "html/template"

type TicketAlertTemplate struct {
	AlertName           string
	AssetIdent          string
	MappedRecordedValue []AlertRecordedValue
	TicketCategoryLabel string
	TicketDesc          string
	RedirectLink        template.URL
}

func (a TicketAlertTemplate) GenerateEmailSubject() string {
	title, _ := ParseStringTemplate(
		"New Work Order Created: Alert {{.AlertName}}", a)

	return title
}

func (a TicketAlertTemplate) GenerateEmailBody() string {
	body, _ := ParseStringTemplate(
		`The system has detected an issue and created a new work order based on the following alert details:
		<br>
		<br>
		<table>
			<tr><td>Alert Name</td>: <td> {{.AlertName}}</td></tr>
			<tr><td>Asset Ident</td>: <td> {{.AssetIdent}}</td></tr>
			<tr><td>Ticket Subject</td>: <td> {{.TicketCategoryLabel}}</td></tr>
			<tr><td>Ticket Description</td>: <td> {{.TicketDesc}}</td></tr>
		</table><br>

		Recorded Value:
		<table>
		{{ range .MappedRecordedValue }}
			<tr>
				<td>{{ .Label }}</td>: <td>{{ .Val }} {{ .Unit }}</td>
			</tr>
		{{ end }}
		</table>
		<br>

		Please click the button below to view the work order details:
		<br><br>
		<a href = "{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Work Order
		</button>
		</a>
		<br>`, a)

	return body
}

func (a TicketAlertTemplate) GeneratePushNotifSubject() string {
	title, _ := ParseStringTemplate(
		"New Work Order Created: Alert {{.AlertName}}", a)

	return title
}

func (a TicketAlertTemplate) GeneratePushNotifBody() string {
	body, _ := ParseStringTemplate(
		`A new work order has been generated on alert: {{.AlertName}}. You have been assigned to this task.`, a)

	return body
}
