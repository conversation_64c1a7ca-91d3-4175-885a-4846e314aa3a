package tmplhelpers

import "html/template"

type TaskAlertTemplate struct {
	AlertName           string
	AssetIdent          string
	MappedRecordedValue []AlertRecordedValue
	TaskSubject         string
	TaskDesc            string
	RedirectLink        template.URL
}

func (a TaskAlertTemplate) GenerateEmailSubject() string {
	title, _ := ParseStringTemplate(
		"New Task Created: Alert {{.AlertName}}", a)

	return title
}

func (a TaskAlertTemplate) GenerateEmailBody() string {
	body, _ := ParseStringTemplate(
		`The system has detected an issue and created a new task based on the following alert details:
		<br>
		<br>
		<table>
			<tr><td>Alert Name</td>: <td> {{.AlertName}}</td></tr>
			<tr><td>Asset Ident</td>: <td> {{.AssetIdent}}</td></tr>
			<tr><td>Task Subject</td>: <td> {{.TaskSubject}}</td></tr>
			<tr><td>Task Description</td>: <td> {{.TaskDesc}}</td></tr>
		</table><br>

		Recorded Value:
		<table>
		{{ range .MappedRecordedValue }}
			<tr>
				<td>{{ .Label }}</td>: <td>{{ .Val }} {{ .Unit }}</td>
			</tr>
		{{ end }}
		</table>
		<br>

		Please click the button below to view the task details:
		<br><br>
		<a href = "{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Task
		</button>
		</a>
		<br>`, a)

	return body
}

func (a TaskAlertTemplate) GeneratePushNotifSubject() string {
	title, _ := ParseStringTemplate(
		"New Task Created: Alert {{.AlertName}}", a)

	return title
}

func (a TaskAlertTemplate) GeneratePushNotifBody() string {
	body, _ := ParseStringTemplate(
		`A new task has been generated on alert: {{.AlertName}}. You have been assigned to this task.`, a)

	return body
}
