package exceltmpl

import (
	"testing"

	"github.com/xuri/excelize/v2"
)

func Test_numberToExcelColumn(t *testing.T) {
	type args struct {
		n int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				n: 1,
			},
			want: "B",
		},
		{
			name: "",
			args: args{
				n: 26,
			},
			want: "AA",
		},
		{
			name: "",
			args: args{
				n: 27,
			},
			want: "AB",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := numberToExcelColumn(tt.args.n); got != tt.want {
				t.Errorf("numberToExcelColumn() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestReportTempl_ExportToExcelFile(t *testing.T) {
	type fields struct {
		Title       string
		Logo        []byte
		SheetName   string
		DownloadBy  string
		ClientName  string
		Description string
		Columns     []ReportColumn
		Rows        [][]any
	}
	tests := []struct {
		name    string
		fields  fields
		want    *excelize.File
		wantErr bool
	}{
		{
			name: "",
			fields: fields{
				Title:       "Title Test",
				Logo:        []byte{},
				SheetName:   "Sheet Test",
				DownloadBy:  "User Test",
				ClientName:  "Client Test",
				Description: "Description Test",
				Columns: []ReportColumn{
					{
						Name:  "No",
						Width: 10,
						Style: TableFloatNumberValueStyle,
					},
				},
				Rows: [][]any{
					{
						1,
					},
					{
						2,
					},
				},
			},
			want:    &excelize.File{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ReportTempl{
				Title:       tt.fields.Title,
				Logo:        tt.fields.Logo,
				SheetName:   tt.fields.SheetName,
				DownloadBy:  tt.fields.DownloadBy,
				ClientName:  tt.fields.ClientName,
				Description: tt.fields.Description,
				Columns:     tt.fields.Columns,
				Rows:        tt.fields.Rows,
			}
			got, err := r.ExportToExcelFile()
			if (err != nil) != tt.wantErr {
				t.Errorf("ReportTempl.ExportToExcelFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err := got.SaveAs("atest.xlsx"); err != nil {
				t.Error("err savev", err)
			}

			// if !reflect.DeepEqual(got, tt.want) {
			// 	t.Errorf("ReportTempl.ExportToExcelFile() = %v, want %v", got, tt.want)
			// }
		})
	}
}
