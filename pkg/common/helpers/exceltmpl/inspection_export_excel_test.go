package exceltmpl

import (
	"testing"
)

func TestExportInspection_ExportToExcelFile(t *testing.T) {
	type fields struct {
		DowndladedUsername string
		Item               []ExportInspectionItem
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				DowndladedUsername: "Test",
				Item:               []ExportInspectionItem{
					// {
					// 	InspectionDatetime:          time.Now(),
					// 	InspectionNo:                "12345",
					// 	AssetVehicleReferenceNumber: "VRN001",
					// 	AssetVehicleBrandName:       "BrandA",
					// 	AssetVehicleModelName:       "ModelX",
					// 	VehicleKM:                   15000,
					// 	VehicleHM:                   500,
					// 	TyrePosition:                1,
					// 	AssetTyreSerialNumber:       "SN123456",
					// 	AssetTyreRfid:               "RFID123456",
					// 	AssetTyreBrandName:          "TireBrandA",
					// 	TyreSize:                    "225/45R17",
					// 	RTD1:                        8.5,
					// 	RTD2:                        8.5,
					// 	RTD3:                        8.5,
					// 	RTD4:                        8.5,
					// 	AverageRTD:                  8.5,
					// 	UtilizationRatePercentage:   75,
					// 	Pressure:                    32,
					// 	InspectedBy:                 "InspectorA",
					// 	Notes:                       "All good",
					// 	DeviceID:                    "Device001",
					// 	Location:                    "LocationA",
					// 	CustomerName:                "Nama Pelangi",
					// },
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ExportInspection{
				DowndladedUsername: tt.fields.DowndladedUsername,
				Items:              tt.fields.Item,
			}
			if f, err := r.ExportToExcelFileWithCustomerData(true); (err != nil) != tt.wantErr {
				t.Errorf("ExportInspection.ExportToExcelFile() error = %v, wantErr %v", err, tt.wantErr)
			} else {
				if err := f.SaveAs("test.xlsx"); err != nil {
					t.Error("err savev", err)
				}
			}
		})
	}
}
