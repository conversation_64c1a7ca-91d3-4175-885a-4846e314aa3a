package exceltmpl

import (
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"fmt"
	"strconv"
	"time"

	"github.com/xuri/excelize/v2"
)

type ExportInspectionItem struct {
	InspectionDatetime        time.Time
	InspectionNo              string
	AssetVehicleIdent         string
	AssetVehicleBrandName     string
	AssetVehicleModelName     string
	VehicleKM                 float64
	VehicleHM                 int
	Odometer                  float64
	TyrePosition              int
	AssetTyreSerialNumber     string
	AssetTyreRfid             string
	AssetTyreBrandName        string
	TyreSize                  string
	RTD1                      float64
	RTD2                      float64
	RTD3                      float64
	RTD4                      float64
	AverageRTD                float64
	UtilizationRatePercentage float64
	Temperature               float64
	Pressure                  float64
	InspectedBy               string
	Notes                     string
	Attachment1               string
	Attachment2               string
	Attachment3               string
	DeviceID                  string
	Location                  string
	LocationPinLink           string
	CustomerName              string
	Source                    string

	VehiclePhoto                 string
	VehilceInspectionAttachment1 string
	VehilceInspectionAttachment2 string
	VehilceInspectionAttachment3 string
}

type ExportInspection struct {
	DowndladedUsername string
	ClientName         string
	Items              []ExportInspectionItem
}

func excelizeBorderAll(color string, style int) []excelize.Border {
	return []excelize.Border{
		{Type: "top", Color: color, Style: style},
		{Type: "left", Color: color, Style: style},
		{Type: "right", Color: color, Style: style},
		{Type: "bottom", Color: color, Style: style},
	}
}

var (
	headerExcelizeStyle = &excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
	}

	titleExcelixeStyle = &excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
		Font: &excelize.Font{
			Size: 27,
			Bold: true,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	}

	downloadExcelizeDateStyle = &excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
		Font: &excelize.Font{
			Size:  9,
			Color: "#1F1F1F",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	}

	tableTitleExcelizeStyle = &excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#185FFF"},
		},
		Font: &excelize.Font{
			Size:  10,
			Bold:  true,
			Color: "#FFFFFF",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "top",
			WrapText:   true,
		},
		Border: excelizeBorderAll("#000000", 4),
	}

	TableValExcelizeStyle = &excelize.Style{
		Font:   &excelize.Font{Size: 10, Color: "#000000"},
		Border: excelizeBorderAll("#000000", 4),
	}

	TableHiperlinkStyle = &excelize.Style{
		Font:   &excelize.Font{Size: 10, Color: "#1265BE", Underline: "single"},
		Border: excelizeBorderAll("#000000", 4),
	}

	TableFloatNumberValueStyle = &excelize.Style{
		Font:         &excelize.Font{Size: 10, Color: "#000000"},
		Border:       excelizeBorderAll("#000000", 4),
		CustomNumFmt: &floatNumFormat,
	}

	floatNumFormat = "#,##0.##;-#,##0.##;-"

	inspectionTimeFormat = "02 January 2006 15:04:05"
)

func cell(col string, row int) string {
	return col + strconv.Itoa(row)
}

func (r *ExportInspection) ExportToExcelFile(isTyreView bool) (*excelize.File, error) {

	// Define EndCol
	endColumn := "AA"

	// Set Sheet Name
	xlsx := excelize.NewFile()
	sheet := "TyreOptimaX DigiSpect"
	err := xlsx.SetSheetName(xlsx.GetSheetName(0), sheet)
	if err != nil {
		commonlogger.Errorf("Error in set sheet name to excel file", err.Error())
		return nil, err
	}

	// defer xlsx.SaveAs("test.xlsx")

	if err := r.applyStylesAndHeader(xlsx, sheet, r.DowndladedUsername, endColumn); err != nil {
		return nil, err
	}

	if isTyreView {
		return r.exportTyreView(xlsx, sheet)
	}

	return r.exportInspectionView(xlsx, sheet)

}

func (r *ExportInspection) applyStylesAndHeader(xlsx *excelize.File, sheet, username, endColumn string) error {

	// Styles
	headerStyle, err := xlsx.NewStyle(headerExcelizeStyle)
	if err != nil {
		return fmt.Errorf("creating header style: %w", err)
	}

	titleStyle, err := xlsx.NewStyle(titleExcelixeStyle)
	if err != nil {
		return fmt.Errorf("creating title style: %w", err)
	}

	downloadDateStyle, err := xlsx.NewStyle(downloadExcelizeDateStyle)
	if err != nil {
		return fmt.Errorf("creating download date style: %w", err)
	}

	// Apply Styles & Content
	if err := xlsx.SetCellStyle(sheet, "A1", endColumn+"7", headerStyle); err != nil {
		return fmt.Errorf("setting header style: %w", err)
	}

	if err := xlsx.MergeCell(sheet, "A2", endColumn+"4"); err != nil {
		return fmt.Errorf("merging title cells: %w", err)
	}

	if err := xlsx.SetCellStr(sheet, "A2", "Riwayat Inspeksi Ban"); err != nil {
		return fmt.Errorf("setting title text: %w", err)
	}
	if err := xlsx.SetCellStyle(sheet, "A2", "A2", titleStyle); err != nil {
		return fmt.Errorf("setting title style: %w", err)
	}

	if err := addImages(xlsx, sheet); err != nil {
		return err
	}

	downloadDate := fmt.Sprintf("downloaded by %s at %s", username, time.Now().Local().Format("02-Jan-2006 15:04"))
	if err := xlsx.MergeCell(sheet, "A5", endColumn+"5"); err != nil {
		return fmt.Errorf("merging download date cells: %w", err)
	}
	if err := xlsx.SetCellStr(sheet, "A5", downloadDate); err != nil {
		return fmt.Errorf("setting download date text: %w", err)
	}
	if err := xlsx.SetCellStyle(sheet, "A5", "A5", downloadDateStyle); err != nil {
		return fmt.Errorf("setting download date style: %w", err)
	}

	// Create Table Style
	tableTitleStyle, err := xlsx.NewStyle(tableTitleExcelizeStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return fmt.Errorf("setting table title style: %w", err)
	}

	err = xlsx.SetCellStyle(sheet, "A8", endColumn+"8", tableTitleStyle)
	if err != nil {
		return fmt.Errorf("setting table cell style: %w", err)
	}

	if len(r.Items) > 0 {
		// Create Table Value Style
		tableValueStyle, err := xlsx.NewStyle(TableValExcelizeStyle)
		if err != nil {
			commonlogger.Errorf("Error in styling to excel file", err.Error())
			return fmt.Errorf("setting table value style: %w", err)
		}

		err = xlsx.SetCellStyle(sheet, "A9", cell(endColumn, 8+len(r.Items)), tableValueStyle)
		if err != nil {
			return fmt.Errorf("setting download date style: %w", err)
		}
	}

	return nil
}

func addImages(xlsx *excelize.File, sheet string) error {
	if err := xlsx.AddPicture(sheet, "A2", "./statics/Logo-Wordmark-Vertical-Blue-400.png", &excelize.GraphicOptions{ScaleX: 0.4, ScaleY: 0.4}); err != nil {
		return fmt.Errorf("adding digispect image: %w", err)
	}

	if err := addImageLogo(xlsx, sheet); err != nil {
		return err
	}

	return nil
}

func addImageLogo(xlsx *excelize.File, sheet string) error {
	if err := xlsx.AddPicture(sheet, "W2", "./statics/AssetFindr-Logo-S2-Transparent-200.png", &excelize.GraphicOptions{ScaleX: 0.53, ScaleY: 0.53}); err != nil {
		return fmt.Errorf("adding logo image: %w", err)
	}

	return nil
}

func (r *ExportInspection) exportInspectionView(xlsx *excelize.File, sheet string) (*excelize.File, error) {
	hyperlinkStyle, err := xlsx.NewStyle(TableHiperlinkStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return nil, err
	}

	floatNumberValStyle, err := xlsx.NewStyle(TableFloatNumberValueStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return nil, err
	}

	// Kolom Odometer
	err = xlsx.SetCellStyle(sheet, "G9", cell("G", 8+len(r.Items)), floatNumberValStyle)
	if err != nil {
		commonlogger.Warnf("error set cell Items")
		return nil, err
	}

	// Kolom Posisi Ban
	err = xlsx.SetCellStyle(sheet, "H9", cell("H", 8+len(r.Items)), floatNumberValStyle)
	if err != nil {
		commonlogger.Warnf("error set cell Items")
		return nil, err
	}

	// Inspecsi Ban Koloms
	err = xlsx.SetCellStyle(sheet, "M9", cell("S", 8+len(r.Items)), floatNumberValStyle)
	if err != nil {
		commonlogger.Warnf("error set cell Items")
		return nil, err
	}

	err = xlsx.SetColWidth(sheet, "Z", "AA", 20)
	if err != nil {
		return nil, err
	}

	tableTitles := [27][2]string{
		{"A8", "No"},
		{"B8", "Waktu Inspeksi"},
		{"C8", "No. Inspeksi"},
		{"D8", "Pelat Nomor"},
		{"E8", "Merek Kendaraan"},
		{"F8", "Model Kendaraan"},
		{"G8", "Odometer"},
		{"H8", "Posisi Ban"},
		{"I8", "Nomor Seri Ban"},
		{"J8", "RFID"},
		{"K8", "Merek Ban"},
		{"L8", "Ukuran Ban"},
		{"M8", "Tebal Tapak Ban 1 (mm)"},
		{"N8", "Tebal Tapak Ban 2 (mm)"},
		{"O8", "Tebal Tapak Ban 3 (mm)"},
		{"P8", "Tebal Tapak Ban 4 (mm)"},
		{"Q8", "Rata-Rata Tebal Tapak Ban (mm)"},
		{"R8", "TUR (%)"},
		{"S8", "Tekanan Ban (psi)"},
		{"T8", "Diinspeksi Oleh"},
		{"U8", "Catatan"},
		{"V8", "Gambar ke-1"},
		{"W8", "Gambar ke-2"},
		{"X8", "Gambar ke-3"},
		{"Y8", "ID Perangkat"},
		{"Z8", "Lokasi"},
		{"AA8", "Pin Lokasi"},
	}

	for _, v := range tableTitles {
		col, val := v[0], v[1]
		err = xlsx.SetCellStr(sheet, col, val)
		if err != nil {
			return nil, err
		}
	}

	for i, item := range r.Items {
		row := i + 9
		err = xlsx.SetCellInt(sheet, cell("A", row), i+1)
		if err != nil {
			commonlogger.Warnf("error set cell idx")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("B", row), item.InspectionDatetime.Format(inspectionTimeFormat))
		if err != nil {
			commonlogger.Warnf("error set cell InspectionDatetime")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("C", row), item.InspectionNo)
		if err != nil {
			commonlogger.Warnf("error set cell InspectionNo")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("D", row), item.AssetVehicleIdent)
		if err != nil {
			commonlogger.Warnf("error set cell AssetVehicleReferenceNumber")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("E", row), item.AssetVehicleBrandName)
		if err != nil {
			commonlogger.Warnf("error set cell AssetVehicleBrandName")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("F", row), item.AssetVehicleModelName)
		if err != nil {
			commonlogger.Warnf("error set cell AssetVehicleModelName")
			return nil, err
		}
		if item.VehicleKM > 0 {
			err = xlsx.SetCellValue(sheet, cell("G", row), item.VehicleKM)
			if err != nil {
				commonlogger.Warnf("error set cell VehicleKM")
				return nil, err
			}
		} else {
			err = xlsx.SetCellValue(sheet, cell("G", row), calculationhelpers.Div100(item.VehicleHM))
			if err != nil {
				commonlogger.Warnf("error set cell VehicleKM")
				return nil, err
			}
		}
		err = xlsx.SetCellInt(sheet, cell("H", row), item.TyrePosition)
		if err != nil {
			commonlogger.Warnf("error set cell TyrePosition")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("I", row), item.AssetTyreSerialNumber)
		if err != nil {
			commonlogger.Warnf("error set cell AssetTyreSerialNumber")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("J", row), item.AssetTyreRfid)
		if err != nil {
			commonlogger.Warnf("error set cell AssetTyreRfid")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("K", row), item.AssetTyreBrandName)
		if err != nil {
			commonlogger.Warnf("error set cell AssetTyreBrandName")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("L", row), item.TyreSize)
		if err != nil {
			commonlogger.Warnf("error set cell TyreSize")
			return nil, err
		}

		err = xlsx.SetCellValue(sheet, cell("M", row), item.RTD1)
		if err != nil {
			commonlogger.Warnf("error set cell RTD1")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("N", row), item.RTD2)
		if err != nil {
			commonlogger.Warnf("error set cell RTD2")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("O", row), item.RTD3)
		if err != nil {
			commonlogger.Warnf("error set cell RTD3")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("P", row), item.RTD4)
		if err != nil {
			commonlogger.Warnf("error set cell RTD4")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("Q", row), item.AverageRTD)
		if err != nil {
			commonlogger.Warnf("error set cell AverageRTD")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("R", row), item.UtilizationRatePercentage)
		if err != nil {
			commonlogger.Warnf("error set cell UtilizationRatePercentage")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("S", row), item.Pressure)
		if err != nil {
			commonlogger.Warnf("error set cell Pressure")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("T", row), item.InspectedBy)
		if err != nil {
			commonlogger.Warnf("error set cell InspectedBy")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("U", row), item.Notes)
		if err != nil {
			commonlogger.Warnf("error set cell Notes")
			return nil, err
		}
		err = setCellStrLink(xlsx, sheet, cell("V", row), item.Attachment1, "Open", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell Attachment1")
			return nil, err
		}
		err = setCellStrLink(xlsx, sheet, cell("W", row), item.Attachment2, "Open", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell Attachment2")
			return nil, err
		}
		err = setCellStrLink(xlsx, sheet, cell("X", row), item.Attachment3, "Open", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell Attachment3")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("Y", row), item.DeviceID)
		if err != nil {
			commonlogger.Warnf("error set cell DeviceID")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("Z", row), item.Location)
		if err != nil {
			commonlogger.Warnf("error set cell Location")
			return nil, err
		}

		err = setCellStrLink(xlsx, sheet, cell("AA", row), item.LocationPinLink, "Open maps", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell LocationPinLink")
			return nil, err
		}

	}

	return xlsx, nil
}

func (r *ExportInspection) exportTyreView(xlsx *excelize.File, sheet string) (*excelize.File, error) {
	hyperlinkStyle, err := xlsx.NewStyle(TableHiperlinkStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return nil, err
	}

	floatNumberValStyle, err := xlsx.NewStyle(TableFloatNumberValueStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return nil, err
	}

	// Kolom Odometer
	err = xlsx.SetCellStyle(sheet, "X9", cell("X", 8+len(r.Items)), floatNumberValStyle)
	if err != nil {
		commonlogger.Warnf("error set cell Items")
		return nil, err
	}

	// Kolom Posisi Ban
	err = xlsx.SetCellStyle(sheet, "Y9", cell("Y", 8+len(r.Items)), floatNumberValStyle)
	if err != nil {
		commonlogger.Warnf("error set cell Items")
		return nil, err
	}

	// Inspecsi Ban Koloms
	err = xlsx.SetCellStyle(sheet, "G9", cell("M", 8+len(r.Items)), floatNumberValStyle)
	if err != nil {
		commonlogger.Warnf("error set cell Items")
		return nil, err
	}

	err = xlsx.SetColWidth(sheet, "Z", "AA", 20)
	if err != nil {
		return nil, err
	}

	tableTitles := [27][2]string{
		{"A8", "No"},
		{"B8", "Waktu Inspeksi"},
		{"C8", "Nomor Seri Ban"},
		{"D8", "RFID"},
		{"E8", "Merek Ban"},
		{"F8", "Ukuran Ban"},
		{"G8", "Tebal Tapak Ban 1 (mm)"},
		{"H8", "Tebal Tapak Ban 2 (mm)"},
		{"I8", "Tebal Tapak Ban 3 (mm)"},
		{"J8", "Tebal Tapak Ban 4 (mm)"},
		{"K8", "Rata-Rata Tebal Tapak Ban (m)"},
		{"L8", "TUR (%)"},
		{"M8", "Tekanan Ban (psi)"},
		{"N8", "Diinspeksi Oleh"},
		{"O8", "Catatan"},
		{"P8", "Gambar ke-1"},
		{"Q8", "Gambar ke-2"},
		{"R8", "Gambar ke-3"},
		{"S8", "ID Perangkat"},
		{"T8", "No. Inspeksi"},
		{"U8", "Pelat Nomor"},
		{"V8", "Merek Kendaraan"},
		{"W8", "Model Kendaraan"},
		{"X8", "Odometer"},
		{"Y8", "Posisi Ban"},
		{"Z8", "Lokasi"},
		{"AA8", "Pin Lokasi"},
	}

	for _, v := range tableTitles {
		col, val := v[0], v[1]
		err = xlsx.SetCellStr(sheet, col, val)
		if err != nil {
			return nil, err
		}
	}

	for i, item := range r.Items {
		row := i + 9
		err = xlsx.SetCellInt(sheet, cell("A", row), i+1)
		if err != nil {
			commonlogger.Warnf("error set cell idx")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("B", row), item.InspectionDatetime.Format(inspectionTimeFormat))
		if err != nil {
			commonlogger.Warnf("error set cell InspectionDatetime")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("T", row), item.InspectionNo)
		if err != nil {
			commonlogger.Warnf("error set cell InspectionNo")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("U", row), item.AssetVehicleIdent)
		if err != nil {
			commonlogger.Warnf("error set cell AssetVehicleReferenceNumber")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("V", row), item.AssetVehicleBrandName)
		if err != nil {
			commonlogger.Warnf("error set cell AssetVehicleBrandName")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("W", row), item.AssetVehicleModelName)
		if err != nil {
			commonlogger.Warnf("error set cell AssetVehicleModelName")
			return nil, err
		}
		if item.VehicleKM > 0 {
			err = xlsx.SetCellValue(sheet, cell("X", row), item.VehicleKM)
			if err != nil {
				commonlogger.Warnf("error set cell VehicleKM")
				return nil, err
			}
		} else {
			err = xlsx.SetCellValue(sheet, cell("X", row), calculationhelpers.Div100(item.VehicleHM))
			if err != nil {
				commonlogger.Warnf("error set cell VehicleKM")
				return nil, err
			}
		}
		err = xlsx.SetCellInt(sheet, cell("Y", row), item.TyrePosition)
		if err != nil {
			commonlogger.Warnf("error set cell TyrePosition")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("C", row), item.AssetTyreSerialNumber)
		if err != nil {
			commonlogger.Warnf("error set cell AssetTyreSerialNumber")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("D", row), item.AssetTyreRfid)
		if err != nil {
			commonlogger.Warnf("error set cell AssetTyreRfid")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("E", row), item.AssetTyreBrandName)
		if err != nil {
			commonlogger.Warnf("error set cell AssetTyreBrandName")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("F", row), item.TyreSize)
		if err != nil {
			commonlogger.Warnf("error set cell TyreSize")
			return nil, err
		}

		err = xlsx.SetCellValue(sheet, cell("G", row), item.RTD1)
		if err != nil {
			commonlogger.Warnf("error set cell RTD1")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("H", row), item.RTD2)
		if err != nil {
			commonlogger.Warnf("error set cell RTD2")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("I", row), item.RTD3)
		if err != nil {
			commonlogger.Warnf("error set cell RTD3")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("J", row), item.RTD4)
		if err != nil {
			commonlogger.Warnf("error set cell RTD4")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("K", row), item.AverageRTD)
		if err != nil {
			commonlogger.Warnf("error set cell AverageRTD")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("L", row), item.UtilizationRatePercentage)
		if err != nil {
			commonlogger.Warnf("error set cell UtilizationRatePercentage")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("M", row), item.Pressure)
		if err != nil {
			commonlogger.Warnf("error set cell Pressure")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("N", row), item.InspectedBy)
		if err != nil {
			commonlogger.Warnf("error set cell InspectedBy")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("O", row), item.Notes)
		if err != nil {
			commonlogger.Warnf("error set cell Notes")
			return nil, err
		}
		err = setCellStrLink(xlsx, sheet, cell("P", row), item.Attachment1, "Open", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell Attachment1")
			return nil, err
		}
		err = setCellStrLink(xlsx, sheet, cell("Q", row), item.Attachment2, "Open", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell Attachment2")
			return nil, err
		}
		err = setCellStrLink(xlsx, sheet, cell("R", row), item.Attachment3, "Open", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell Attachment3")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("S", row), item.DeviceID)
		if err != nil {
			commonlogger.Warnf("error set cell DeviceID")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("Z", row), item.Location)
		if err != nil {
			commonlogger.Warnf("error set cell Location")
			return nil, err
		}

		err = setCellStrLink(xlsx, sheet, cell("AA", row), item.LocationPinLink, "Open maps", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell LocationPinLink")
			return nil, err
		}

	}

	return xlsx, nil
}

func setCellStr(f *excelize.File, sheet, cell, val string) error {
	if val == "" {
		val = "-"
	}

	return f.SetCellStr(sheet, cell, val)
}

var openImage string = "Open"

func setCellStrLink(f *excelize.File, sheet, cell, link string, val string, hyperLinkStyle int) error {
	if link == "" {
		return f.SetCellStr(sheet, cell, "-")
	} else {
		if err := f.SetCellStr(sheet, cell, val); err != nil {
			return err
		}

		if err := f.SetCellHyperLink(sheet, cell, link, "External", excelize.HyperlinkOpts{
			Tooltip: &openImage,
		}); err != nil {
			return err
		}

		return f.SetCellStyle(sheet, cell, cell, hyperLinkStyle)
	}
}

func (r *ExportInspection) ExportToExcelFileWithCustomerData(isTyreView bool) (*excelize.File, error) {

	// Define EndCol
	endColumn := "AB"

	// Set Sheet Name
	xlsx := excelize.NewFile()
	sheet := "TyreOptimaX DigiSpect"
	err := xlsx.SetSheetName(xlsx.GetSheetName(0), sheet)
	if err != nil {
		commonlogger.Errorf("Error in set sheet name to excel file", err.Error())
		return nil, err
	}

	// defer xlsx.SaveAs("test.xlsx")

	if err := r.applyStylesAndHeader(xlsx, sheet, r.DowndladedUsername, endColumn); err != nil {
		return nil, err
	}

	if isTyreView {
		return r.exportTyreViewWithCustomerData(xlsx, sheet)
	}

	return r.exportInspectionViewWithCustomerData(xlsx, sheet)

}

func (r *ExportInspection) ExportToExcelTyreViewScenario4(isTyreView bool) (*excelize.File, error) {
	templ := ReportTempl{
		Title:         "Tyre Inspection Report",
		SheetName:     "Tyre Inspection Report",
		Logo:          []byte{},
		LogoExtension: "",
		DownloadBy:    r.DowndladedUsername,
		ClientName:    r.ClientName,
		Description:   "This report is to show the current condition of tyre installed on vehicle",
		Columns: []ReportColumn{
			{Name: "No"},             //0
			{Name: "Waktu Inspeksi"}, //1
			{Name: "Nomor Seri Ban"}, //2
			{Name: "RFID"},           //3
			{Name: "Merek Ban"},      //4
			{Name: "Ukuran Ban"},     //5
			{Name: "Tebal Tapak Ban 1 (mm)", Style: TableFloatNumberValueStyle},        //6
			{Name: "Tebal Tapak Ban 2 (mm)", Style: TableFloatNumberValueStyle},        //7
			{Name: "Tebal Tapak Ban 3 (mm)", Style: TableFloatNumberValueStyle},        //8
			{Name: "Tebal Tapak Ban 4 (mm)", Style: TableFloatNumberValueStyle},        //9
			{Name: "Rata-Rata Tebal Tapak Ban (m)", Style: TableFloatNumberValueStyle}, //10
			{Name: "TUR (%)", Style: TableFloatNumberValueStyle},                       //11
			{Name: "Tekanan Ban (psi)", Style: TableFloatNumberValueStyle},             //12
			{Name: "Diinspeksi Oleh"}, //13
			{Name: "Catatan"},         //14
			{Name: "Gambar ke-1", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //15
			{Name: "Gambar ke-2", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //16
			{Name: "Gambar ke-3", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //17
			{Name: "ID Perangkat"},   //18
			{Name: "No. Inspeksi"},   //19
			{Name: "Nama Pelanggan"}, //20
			{Name: "Pelat Nomor"},    //21
			{Name: "Gambar Profil Kendaraan", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //22
			{Name: "Merek Kendaraan"}, //23
			{Name: "Model Kendaraan"}, //24
			{Name: "Gambar Kendaraan ke -1", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //25
			{Name: "Gambar Kendaraan ke -2", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //26
			{Name: "Gambar Kendaraan ke -3", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //27
			{Name: "Odometer", Style: TableFloatNumberValueStyle},                                   //28
			{Name: "Posisi Ban", Style: TableFloatNumberValueStyle},                                 //29
			{Name: "Lokasi"}, //30
			{Name: "Pin Lokasi", CustomDisplayLink: "Open maps", Style: TableHiperlinkStyle}, //31
		},
		Rows: make([][]any, len(r.Items)),
	}

	for i, item := range r.Items {
		templ.Rows[i] = []any{
			i + 1, //0
			item.InspectionDatetime.Format(inspectionTimeFormat), //1
			item.AssetTyreSerialNumber,                           //2
			item.AssetTyreRfid,                                   //3
			item.AssetTyreBrandName,                              //4
			item.TyreSize,                                        //5
			item.RTD1,                                            //6
			item.RTD2,                                            //7
			item.RTD3,                                            //8
			item.RTD4,                                            //9
			item.AverageRTD,                                      //10
			item.UtilizationRatePercentage,                       //11
			item.Pressure,                                        //12
			item.InspectedBy,                                     //13
			item.Notes,                                           //14
			item.Attachment1,                                     //15
			item.Attachment2,                                     //16
			item.Attachment3,                                     //17
			item.DeviceID,                                        //18
			item.InspectionNo,                                    //19
			item.CustomerName,                                    //20
			item.AssetVehicleIdent,                               //21
			item.VehiclePhoto,                                    //22
			item.AssetVehicleBrandName,                           //23
			item.AssetVehicleModelName,                           //24
			item.VehilceInspectionAttachment1,                    //25
			item.VehilceInspectionAttachment2,                    //26
			item.VehilceInspectionAttachment3,                    //27
			item.Odometer,                                        //28
			item.TyrePosition,                                    //29
			item.Location,                                        //30
			item.LocationPinLink,                                 //31
		}
	}

	return templ.ExportToExcelFileOldStyle()
}

func (r *ExportInspection) ExportToExcelInspectionViewScenario4(isTyreView bool) (*excelize.File, error) {
	templ := ReportTempl{
		Title:         "Inspection Report",
		SheetName:     "Inspection Report",
		Logo:          []byte{},
		LogoExtension: "",
		DownloadBy:    r.DowndladedUsername,
		ClientName:    r.ClientName,
		Description:   "This report is to show the current condition of tyre installed on vehicle",
		Columns: []ReportColumn{
			{Name: "No"},             //0
			{Name: "Waktu Inspeksi"}, //1
			{Name: "No. Inspeksi"},   //2
			{Name: "Nama Pelanggan"}, //3
			{Name: "Pelat Nomor"},    //4
			{Name: "Gambar Profil Kendaraan", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //5
			{Name: "Merek Kendaraan"}, //6
			{Name: "Model Kendaraan"}, //7
			{Name: "Gambar Kendaraan ke -1", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //8
			{Name: "Gambar Kendaraan ke -2", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //9
			{Name: "Gambar Kendaraan ke -3", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //10
			{Name: "Odometer", Style: TableFloatNumberValueStyle},                                   //11
			{Name: "Posisi Ban", Style: TableFloatNumberValueStyle},                                 //12
			{Name: "Nomor Seri Ban"}, //13
			{Name: "RFID"},           //14
			{Name: "Merek Ban"},      //15
			{Name: "Ukuran Ban"},     //16
			{Name: "Tebal Tapak Ban 1 (mm)", Style: TableFloatNumberValueStyle},         //17
			{Name: "Tebal Tapak Ban 2 (mm)", Style: TableFloatNumberValueStyle},         //18
			{Name: "Tebal Tapak Ban 3 (mm)", Style: TableFloatNumberValueStyle},         //19
			{Name: "Tebal Tapak Ban 4 (mm)", Style: TableFloatNumberValueStyle},         //20
			{Name: "Rata-Rata Tebal Tapak Ban (mm)", Style: TableFloatNumberValueStyle}, //21
			{Name: "TUR (%)", Style: TableFloatNumberValueStyle},                        //22
			{Name: "Tekanan Ban (psi)", Style: TableFloatNumberValueStyle},              //23
			{Name: "Diinspeksi Oleh"}, //24
			{Name: "Catatan"},         //25
			{Name: "Gambar ke-1", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //26
			{Name: "Gambar ke-2", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //27
			{Name: "Gambar ke-3", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, //28
			{Name: "ID Perangkat"}, //29
			{Name: "Lokasi"},       //30
			{Name: "Pin Lokasi", CustomDisplayLink: "Open maps", Style: TableHiperlinkStyle}, //31
		},
		Rows: make([][]any, len(r.Items)),
	}

	for i, item := range r.Items {
		templ.Rows[i] = []any{
			i + 1, //0
			item.InspectionDatetime.Format(inspectionTimeFormat), //1
			item.InspectionNo,                 //2
			item.CustomerName,                 //3
			item.AssetVehicleIdent,            //4
			item.VehiclePhoto,                 //5
			item.AssetVehicleBrandName,        //6
			item.AssetVehicleModelName,        //7
			item.VehilceInspectionAttachment1, //8
			item.VehilceInspectionAttachment2, //9
			item.VehilceInspectionAttachment3, //10
			item.Odometer,                     //11
			item.TyrePosition,                 //12
			item.AssetTyreSerialNumber,        //13
			item.AssetTyreRfid,                //14
			item.AssetTyreBrandName,           //15
			item.TyreSize,                     //16
			item.RTD1,                         //17
			item.RTD2,                         //18
			item.RTD3,                         //19
			item.RTD4,                         //20
			item.AverageRTD,                   //21
			item.UtilizationRatePercentage,    //22
			item.Pressure,                     //23
			item.InspectedBy,                  //24
			item.Notes,                        //25
			item.Attachment1,                  //26
			item.Attachment2,                  //27
			item.Attachment3,                  //28
			item.DeviceID,                     //29
			item.Location,                     //30
			item.LocationPinLink,              //31
		}
	}

	return templ.ExportToExcelFileOldStyle()
}

func (r *ExportInspection) exportTyreViewWithCustomerData(xlsx *excelize.File, sheet string) (*excelize.File, error) {
	hyperlinkStyle, err := xlsx.NewStyle(TableHiperlinkStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return nil, err
	}

	floatNumberValStyle, err := xlsx.NewStyle(TableFloatNumberValueStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return nil, err
	}

	err = xlsx.SetColWidth(sheet, "AA", "AB", 20)
	if err != nil {
		return nil, err
	}

	tableTitles := [28][2]string{
		{"A8", "No"},
		{"B8", "Waktu Inspeksi"},
		{"C8", "Nomor Seri Ban"},
		{"D8", "RFID"},
		{"E8", "Merek Ban"},
		{"F8", "Ukuran Ban"},
		{"G8", "Tebal Tapak Ban 1 (mm)"},
		{"H8", "Tebal Tapak Ban 2 (mm)"},
		{"I8", "Tebal Tapak Ban 3 (mm)"},
		{"J8", "Tebal Tapak Ban 4 (mm)"},
		{"K8", "Rata-Rata Tebal Tapak Ban (m)"},
		{"L8", "TUR (%)"},
		{"M8", "Tekanan Ban (psi)"},
		{"N8", "Diinspeksi Oleh"},
		{"O8", "Catatan"},
		{"P8", "Gambar ke-1"},
		{"Q8", "Gambar ke-2"},
		{"R8", "Gambar ke-3"},
		{"S8", "ID Perangkat"},
		{"T8", "No. Inspeksi"},
		{"U8", "Nama Pelanggan"},
		{"V8", "Pelat Nomor"},
		{"W8", "Merek Kendaraan"},
		{"X8", "Model Kendaraan"},
		{"Y8", "Odometer"},
		{"Z8", "Posisi Ban"},
		{"AA8", "Lokasi"},
		{"AB8", "Pin Lokasi"},
	}

	for _, v := range tableTitles {
		col, val := v[0], v[1]
		err := xlsx.SetCellStr(sheet, col, val)
		if err != nil {
			return nil, err
		}
	}

	if len(r.Items) == 0 {
		return xlsx, nil
	}

	// Kolom Odometer
	err = xlsx.SetCellStyle(sheet, "Y9", cell("Y", 8+len(r.Items)), floatNumberValStyle)
	if err != nil {
		commonlogger.Warnf("error set cell Items")
		return nil, err
	}

	// Kolom Posisi Ban
	err = xlsx.SetCellStyle(sheet, "Z9", cell("Z", 8+len(r.Items)), floatNumberValStyle)
	if err != nil {
		commonlogger.Warnf("error set cell Items")
		return nil, err
	}

	// Inspecsi Ban Koloms
	err = xlsx.SetCellStyle(sheet, "G9", cell("M", 8+len(r.Items)), floatNumberValStyle)
	if err != nil {
		commonlogger.Warnf("error set cell Items")
		return nil, err
	}

	for i, item := range r.Items {
		row := i + 9
		err = xlsx.SetCellInt(sheet, cell("A", row), i+1)
		if err != nil {
			commonlogger.Warnf("error set cell idx")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("B", row), item.InspectionDatetime.Format(inspectionTimeFormat))
		if err != nil {
			commonlogger.Warnf("error set cell InspectionDatetime")
			return nil, err
		}

		err = setCellStr(xlsx, sheet, cell("C", row), item.AssetTyreSerialNumber)
		if err != nil {
			commonlogger.Warnf("error set cell AssetTyreSerialNumber")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("D", row), item.AssetTyreRfid)
		if err != nil {
			commonlogger.Warnf("error set cell AssetTyreRfid")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("E", row), item.AssetTyreBrandName)
		if err != nil {
			commonlogger.Warnf("error set cell AssetTyreBrandName")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("F", row), item.TyreSize)
		if err != nil {
			commonlogger.Warnf("error set cell TyreSize")
			return nil, err
		}

		err = xlsx.SetCellValue(sheet, cell("G", row), item.RTD1)
		if err != nil {
			commonlogger.Warnf("error set cell RTD1")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("H", row), item.RTD2)
		if err != nil {
			commonlogger.Warnf("error set cell RTD2")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("I", row), item.RTD3)
		if err != nil {
			commonlogger.Warnf("error set cell RTD3")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("J", row), item.RTD4)
		if err != nil {
			commonlogger.Warnf("error set cell RTD4")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("K", row), item.AverageRTD)
		if err != nil {
			commonlogger.Warnf("error set cell AverageRTD")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("L", row), item.UtilizationRatePercentage)
		if err != nil {
			commonlogger.Warnf("error set cell UtilizationRatePercentage")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("M", row), item.Pressure)
		if err != nil {
			commonlogger.Warnf("error set cell Pressure")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("N", row), item.InspectedBy)
		if err != nil {
			commonlogger.Warnf("error set cell InspectedBy")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("O", row), item.Notes)
		if err != nil {
			commonlogger.Warnf("error set cell Notes")
			return nil, err
		}
		err = setCellStrLink(xlsx, sheet, cell("P", row), item.Attachment1, "Open", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell Attachment1")
			return nil, err
		}
		err = setCellStrLink(xlsx, sheet, cell("Q", row), item.Attachment2, "Open", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell Attachment2")
			return nil, err
		}
		err = setCellStrLink(xlsx, sheet, cell("R", row), item.Attachment3, "Open", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell Attachment3")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("S", row), item.DeviceID)
		if err != nil {
			commonlogger.Warnf("error set cell DeviceID")
			return nil, err
		}

		err = setCellStr(xlsx, sheet, cell("T", row), item.InspectionNo)
		if err != nil {
			commonlogger.Warnf("error set cell InspectionNo")
			return nil, err
		}

		err = setCellStr(xlsx, sheet, cell("U", row), item.CustomerName)
		if err != nil {
			commonlogger.Warnf("error set cell CustomerName")
			return nil, err
		}

		err = setCellStr(xlsx, sheet, cell("V", row), item.AssetVehicleIdent)
		if err != nil {
			commonlogger.Warnf("error set cell AssetVehicleReferenceNumber")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("W", row), item.AssetVehicleBrandName)
		if err != nil {
			commonlogger.Warnf("error set cell AssetVehicleBrandName")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("X", row), item.AssetVehicleModelName)
		if err != nil {
			commonlogger.Warnf("error set cell AssetVehicleModelName")
			return nil, err
		}
		if item.VehicleKM > 0 {
			err = xlsx.SetCellValue(sheet, cell("Y", row), item.VehicleKM)
			if err != nil {
				commonlogger.Warnf("error set cell VehicleKM")
				return nil, err
			}
		} else {
			err = xlsx.SetCellValue(sheet, cell("Y", row), calculationhelpers.Div100(item.VehicleHM))
			if err != nil {
				commonlogger.Warnf("error set cell VehicleKM")
				return nil, err
			}
		}
		err = xlsx.SetCellInt(sheet, cell("Z", row), item.TyrePosition)
		if err != nil {
			commonlogger.Warnf("error set cell TyrePosition")
			return nil, err
		}

		err = setCellStr(xlsx, sheet, cell("AA", row), item.Location)
		if err != nil {
			commonlogger.Warnf("error set cell Location")
			return nil, err
		}

		err = setCellStrLink(xlsx, sheet, cell("AB", row), item.LocationPinLink, "Open maps", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell LocationPinLink")
			return nil, err
		}

	}

	return xlsx, nil
}

func (r *ExportInspection) exportInspectionViewWithCustomerData(xlsx *excelize.File, sheet string) (*excelize.File, error) {
	hyperlinkStyle, err := xlsx.NewStyle(TableHiperlinkStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return nil, err
	}

	floatNumberValStyle, err := xlsx.NewStyle(TableFloatNumberValueStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return nil, err
	}

	// Kolom Odometer
	err = xlsx.SetCellStyle(sheet, "H9", cell("H", 8+len(r.Items)), floatNumberValStyle)
	if err != nil {
		commonlogger.Warnf("error set cell Items")
		return nil, err
	}

	// Kolom Posisi Ban
	err = xlsx.SetCellStyle(sheet, "I9", cell("I", 8+len(r.Items)), floatNumberValStyle)
	if err != nil {
		commonlogger.Warnf("error set cell Items")
		return nil, err
	}

	// Inspecsi Ban Koloms
	err = xlsx.SetCellStyle(sheet, "N9", cell("T", 8+len(r.Items)), floatNumberValStyle)
	if err != nil {
		commonlogger.Warnf("error set cell Items")
		return nil, err
	}

	err = xlsx.SetColWidth(sheet, "AA", "AB", 20)
	if err != nil {
		return nil, err
	}

	tableTitles := [28][2]string{
		{"A8", "No"},
		{"B8", "Waktu Inspeksi"},
		{"C8", "No. Inspeksi"},
		{"D8", "Nama Pelanggan"},
		{"E8", "Pelat Nomor"},
		{"F8", "Merek Kendaraan"},
		{"G8", "Model Kendaraan"},
		{"H8", "Odometer"},
		{"I8", "Posisi Ban"},
		{"J8", "Nomor Seri Ban"},
		{"K8", "RFID"},
		{"L8", "Merek Ban"},
		{"M8", "Ukuran Ban"},
		{"N8", "Tebal Tapak Ban 1 (mm)"},
		{"O8", "Tebal Tapak Ban 2 (mm)"},
		{"P8", "Tebal Tapak Ban 3 (mm)"},
		{"Q8", "Tebal Tapak Ban 4 (mm)"},
		{"R8", "Rata-Rata Tebal Tapak Ban (mm)"},
		{"S8", "TUR (%)"},
		{"T8", "Tekanan Ban (psi)"},
		{"U8", "Diinspeksi Oleh"},
		{"V8", "Catatan"},
		{"W8", "Gambar ke-1"},
		{"X8", "Gambar ke-2"},
		{"Y8", "Gambar ke-3"},
		{"Z8", "ID Perangkat"},
		{"AA8", "Lokasi"},
		{"AB8", "Pin Lokasi"},
	}

	for _, v := range tableTitles {
		col, val := v[0], v[1]
		err = xlsx.SetCellStr(sheet, col, val)
		if err != nil {
			return nil, err
		}
	}

	for i, item := range r.Items {
		row := i + 9
		err = xlsx.SetCellInt(sheet, cell("A", row), i+1)
		if err != nil {
			commonlogger.Warnf("error set cell idx")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("B", row), item.InspectionDatetime.Format(inspectionTimeFormat))
		if err != nil {
			commonlogger.Warnf("error set cell InspectionDatetime")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("C", row), item.InspectionNo)
		if err != nil {
			commonlogger.Warnf("error set cell InspectionNo")
			return nil, err
		}

		err = setCellStr(xlsx, sheet, cell("D", row), item.CustomerName)
		if err != nil {
			commonlogger.Warnf("error set cell CustomerName")
			return nil, err
		}

		err = setCellStr(xlsx, sheet, cell("E", row), item.AssetVehicleIdent)
		if err != nil {
			commonlogger.Warnf("error set cell AssetVehicleReferenceNumber")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("F", row), item.AssetVehicleBrandName)
		if err != nil {
			commonlogger.Warnf("error set cell AssetVehicleBrandName")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("G", row), item.AssetVehicleModelName)
		if err != nil {
			commonlogger.Warnf("error set cell AssetVehicleModelName")
			return nil, err
		}
		if item.VehicleKM > 0 {
			err = xlsx.SetCellValue(sheet, cell("H", row), item.VehicleKM)
			if err != nil {
				commonlogger.Warnf("error set cell VehicleKM")
				return nil, err
			}
		} else {
			err = xlsx.SetCellValue(sheet, cell("H", row), calculationhelpers.Div100(item.VehicleHM))
			if err != nil {
				commonlogger.Warnf("error set cell VehicleKM")
				return nil, err
			}
		}
		err = xlsx.SetCellInt(sheet, cell("I", row), item.TyrePosition)
		if err != nil {
			commonlogger.Warnf("error set cell TyrePosition")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("J", row), item.AssetTyreSerialNumber)
		if err != nil {
			commonlogger.Warnf("error set cell AssetTyreSerialNumber")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("K", row), item.AssetTyreRfid)
		if err != nil {
			commonlogger.Warnf("error set cell AssetTyreRfid")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("L", row), item.AssetTyreBrandName)
		if err != nil {
			commonlogger.Warnf("error set cell AssetTyreBrandName")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("M", row), item.TyreSize)
		if err != nil {
			commonlogger.Warnf("error set cell TyreSize")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("N", row), item.RTD1)
		if err != nil {
			commonlogger.Warnf("error set cell RTD1")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("O", row), item.RTD2)
		if err != nil {
			commonlogger.Warnf("error set cell RTD2")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("P", row), item.RTD3)
		if err != nil {
			commonlogger.Warnf("error set cell RTD3")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("Q", row), item.RTD4)
		if err != nil {
			commonlogger.Warnf("error set cell RTD4")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("R", row), item.AverageRTD)
		if err != nil {
			commonlogger.Warnf("error set cell AverageRTD")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("S", row), item.UtilizationRatePercentage)
		if err != nil {
			commonlogger.Warnf("error set cell UtilizationRatePercentage")
			return nil, err
		}
		err = xlsx.SetCellValue(sheet, cell("T", row), item.Pressure)
		if err != nil {
			commonlogger.Warnf("error set cell Pressure")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("U", row), item.InspectedBy)
		if err != nil {
			commonlogger.Warnf("error set cell InspectedBy")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("V", row), item.Notes)
		if err != nil {
			commonlogger.Warnf("error set cell Notes")
			return nil, err
		}
		err = setCellStrLink(xlsx, sheet, cell("W", row), item.Attachment1, "Open", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell Attachment1")
			return nil, err
		}
		err = setCellStrLink(xlsx, sheet, cell("X", row), item.Attachment2, "Open", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell Attachment2")
			return nil, err
		}
		err = setCellStrLink(xlsx, sheet, cell("Y", row), item.Attachment3, "Open", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell Attachment3")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("Z", row), item.DeviceID)
		if err != nil {
			commonlogger.Warnf("error set cell DeviceID")
			return nil, err
		}
		err = setCellStr(xlsx, sheet, cell("AA", row), item.Location)
		if err != nil {
			commonlogger.Warnf("error set cell Location")
			return nil, err
		}

		err = setCellStrLink(xlsx, sheet, cell("AB", row), item.LocationPinLink, "Open maps", hyperlinkStyle)
		if err != nil {
			commonlogger.Warnf("error set cell LocationPinLink")
			return nil, err
		}

	}

	return xlsx, nil
}

func (r *ExportInspection) ExportToExcelTyreViewInspectionList() (*excelize.File, error) {
	templ := ReportTempl{
		DownloadBy: r.DowndladedUsername,
		ClientName: r.ClientName,
		SheetName:  "Inspections",
		Columns: []ReportColumn{
			{Name: "No"},             // 0
			{Name: "Waktu Inspeksi"}, // 1
			{Name: "No. Inspeksi"},   // 2
			{Name: "Nomor Seri Ban"}, // 3
			{Name: "RFID"},           // 4
			{Name: "Merek Ban"},      // 5
			{Name: "Ukuran Ban"},     // 6
			{Name: "Tebal Tapak Ban 1 (mm)", Style: TableFloatNumberValueStyle},         // 7
			{Name: "Tebal Tapak Ban 2 (mm)", Style: TableFloatNumberValueStyle},         // 8
			{Name: "Tebal Tapak Ban 3 (mm)", Style: TableFloatNumberValueStyle},         // 9
			{Name: "Tebal Tapak Ban 4 (mm)", Style: TableFloatNumberValueStyle},         // 10
			{Name: "Rata-Rata Tebal Tapak Ban (mm)", Style: TableFloatNumberValueStyle}, // 11
			{Name: "TUR (%)", Style: TableFloatNumberValueStyle},                        // 12
			{Name: "Temperatur Ban (ºC)", Style: TableFloatNumberValueStyle},            // 13
			{Name: "Tekanan Ban (psi)", Style: TableFloatNumberValueStyle},              // 14
			{Name: "Diinspeksi Oleh"}, // 15
			{Name: "Catatan"},         // 16
			{Name: "Foto Ban ke-1", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, // 17
			{Name: "Foto Ban ke-2", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, // 18
			{Name: "Foto Ban ke-3", CustomDisplayLink: "Open", Style: TableHiperlinkStyle}, // 19
			{Name: "Source"},       // 20
			{Name: "ID Perangkat"}, // 21
			{Name: "Lokasi"},       // 22
			{Name: "Pin Lokasi", CustomDisplayLink: "Open maps", Style: TableHiperlinkStyle}, // 23
		},
		Rows: make([][]any, len(r.Items)),
	}

	for i, item := range r.Items {
		templ.Rows[i] = []any{
			i + 1, //0
			item.InspectionDatetime.Format(inspectionTimeFormat), //1
			item.InspectionNo,              //2
			item.AssetTyreSerialNumber,     //3
			item.AssetTyreRfid,             //4
			item.AssetTyreBrandName,        //5
			item.TyreSize,                  //6
			item.RTD1,                      //7
			item.RTD2,                      //8
			item.RTD3,                      //9
			item.RTD4,                      //10
			item.AverageRTD,                //11
			item.UtilizationRatePercentage, //12
			item.Temperature,               //13
			item.Pressure,                  //14
			item.InspectedBy,               //15
			item.Notes,                     //16
			item.Attachment1,               //17
			item.Attachment2,               //18
			item.Attachment3,               //19
			item.Source,                    //20
			item.DeviceID,                  //21
			item.Location,                  //22
			item.LocationPinLink,           //23
		}
	}

	return templ.ExportToExcelFileOldStyle(applyStylesAndHeaderOldStyleInspectionList)
}

func (r *ExportInspection) ExportToExcelInspectionViewInspectionList(hasCustomerNameCol bool) (*excelize.File, error) {
	templ := ReportTempl{
		DownloadBy: r.DowndladedUsername,
		ClientName: r.ClientName,
		SheetName:  "Inspections",
		Columns: []ReportColumn{
			{Name: "No"},
			{Name: "Waktu Inspeksi"},
			{Name: "No. Inspeksi"},
			{Name: "Nama Pelanggan"},
			{Name: "Pelat Nomor"},
			{Name: "Merek Kendaraan"},
			{Name: "Model Kendaraan"},
			{Name: "Odometer", Style: TableFloatNumberValueStyle},
			{Name: "Posisi Ban"},
			{Name: "Nomor Seri Ban"},
			{Name: "RFID"},
			{Name: "Merek Ban"},
			{Name: "Ukuran Ban"},
			{Name: "Tebal Tapak Ban 1 (mm)", Style: TableFloatNumberValueStyle},
			{Name: "Tebal Tapak Ban 2 (mm)", Style: TableFloatNumberValueStyle},
			{Name: "Tebal Tapak Ban 3 (mm)", Style: TableFloatNumberValueStyle},
			{Name: "Tebal Tapak Ban 4 (mm)", Style: TableFloatNumberValueStyle},
			{Name: "Rata-Rata Tebal Tapak Ban (mm)", Style: TableFloatNumberValueStyle},
			{Name: "TUR (%)", Style: TableFloatNumberValueStyle},
			{Name: "Temperatur Ban (ºC)", Style: TableFloatNumberValueStyle},
			{Name: "Tekanan Ban (psi)", Style: TableFloatNumberValueStyle},
			{Name: "Diinspeksi Oleh"},
			{Name: "Catatan"},
			{Name: "Foto Ban ke-1", CustomDisplayLink: "Open", Style: TableHiperlinkStyle},
			{Name: "Foto Ban ke-2", CustomDisplayLink: "Open", Style: TableHiperlinkStyle},
			{Name: "Foto Ban ke-3", CustomDisplayLink: "Open", Style: TableHiperlinkStyle},
			{Name: "Source"},
			{Name: "ID Perangkat"},
			{Name: "Lokasi"},
			{Name: "Pin Lokasi", CustomDisplayLink: "Open maps", Style: TableHiperlinkStyle},
		},
		Rows: make([][]any, len(r.Items)),
	}

	if !hasCustomerNameCol {
		templ.Columns = append(templ.Columns[:3], templ.Columns[4:]...)
	}

	for i, item := range r.Items {
		templ.Rows[i] = []any{
			i + 1, //0
			item.InspectionDatetime.Format(inspectionTimeFormat), //1
			item.InspectionNo,              //2
			item.CustomerName,              //3
			item.AssetVehicleIdent,         //4
			item.AssetVehicleBrandName,     //5
			item.AssetVehicleModelName,     //6
			item.Odometer,                  //7
			item.TyrePosition,              //8
			item.AssetTyreSerialNumber,     //9
			item.AssetTyreRfid,             //10
			item.AssetTyreBrandName,        //11
			item.TyreSize,                  //12
			item.RTD1,                      //13
			item.RTD2,                      //14
			item.RTD3,                      //15
			item.RTD4,                      //16
			item.AverageRTD,                //17
			item.UtilizationRatePercentage, //18
			item.Temperature,               //19
			item.Pressure,                  //20
			item.InspectedBy,               //21
			item.Notes,                     //22
			item.Attachment1,               //23
			item.Attachment2,               //24
			item.Attachment3,               //25
			item.Source,                    //26
			item.DeviceID,                  //27
			item.Location,                  //28
			item.LocationPinLink,           //29
		}

		if !hasCustomerNameCol {
			templ.Rows[i] = append(templ.Rows[i][:3], templ.Rows[i][4:]...)
		}
	}

	return templ.ExportToExcelFileOldStyle(applyStylesAndHeaderOldStyleInspectionList)
}
