package helpers

import (
	"fmt"
	"math"
	"strconv"
)

func FloatHasPrecision(value float64) bool {
	return value != math.Trunc(value)
}

func FormatDecimalNumber(n float64) float64 {
	formattedString := fmt.Sprintf("%.1f", n)

	if n == math.Floor(n) {
		formattedString = fmt.Sprintf("%.0f", n)
	}

	formattedVal, _ := strconv.ParseFloat(formattedString, 64)

	return formattedVal
}

func TruncateFloat(n float64, precision int) float64 {
	factor := math.Pow(10, float64(precision))
	return math.Floor(n*factor) / factor
}
