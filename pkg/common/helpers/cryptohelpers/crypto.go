package cryptohelpers

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"os"
)

var bytes = []byte{38, 46, 57, 24, 85, 35, 24, 74, 87, 35, 88, 98, 66, 32, 14, 05}

func Encode(b []byte) string {
	return base64.StdEncoding.EncodeToString(b)
}

func Decode(s string) []byte {
	data, _ := base64.StdEncoding.DecodeString(s)
	return data
}

func DefaultEncrypt(text string) string {
	return Encrypt(text, os.Getenv("ENCRYPT_DECRYPT_SCERET_KEY"))
}

func Encrypt(text, secret string) string {
	block, err := aes.NewCipher([]byte(secret))
	if err != nil {
		panic(err)
	}

	plainText := []byte(text)
	cfb := cipher.NewCFBEncrypter(block, bytes)
	cipherText := make([]byte, len(plainText))
	cfb.XORKeyStream(cipherText, plainText)
	return Encode(cipherText)
}

func DefaultDecrypt(text string) string {
	return Decrypt(text, os.Getenv("ENCRYPT_DECRYPT_SCERET_KEY"))
}

func Decrypt(text, secret string) string {
	block, _ := aes.NewCipher([]byte(secret))
	cipherText := Decode(text)
	cfb := cipher.NewCFBDecrypter(block, bytes)
	plainText := make([]byte, len(cipherText))
	cfb.XORKeyStream(plainText, cipherText)
	return string(plainText)
}
