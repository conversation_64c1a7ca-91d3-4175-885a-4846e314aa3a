package constants

const (
	CLIENT_PACKAGE_FLEET_OPTIMAX      string = "FLEET_OPTIMAX"
	CLIENT_PACKAGE_TYRE_OPTIMAX       string = "TYRE_OPTIMAX"
	CLIENT_PACKAGE_TYRE_DEALER        string = "TYRE_DEALER"
	CLIENT_PACKAGE_TRACK_OPTIMAX      string = "TRACK_OPTIMAX"
	CLIENT_PACKAGE_WORKSHOP_OPTIMAX   string = "WORKSHOP_OPTIMAX"
	CLIENT_PACKAGE_DIGISPECT_PRO      string = "DIGISPECT_PRO"
	CLIENT_PACKAGE_DIGISPECT_PRO_PLUS string = "DIGISPECT_PRO_PLUS"
)

const (
	CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_4 string = "DIGISPECT_INSPECTION_FLOW_SCENARIO_4"
	CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_5 string = "DIGISPECT_INSPECTION_FLOW_SCENARIO_5"
	CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_6 string = "DIGISPECT_INSPECTION_FLOW_SCENARIO_6"
	CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_7 string = "DIGISPECT_INSPECTION_FLOW_SCENARIO_7"
)

const (
	CLIENT_PACKAGE_DIGISPECT_PRO_ALIAS      string = "PRO"
	CLIENT_PACKAGE_DIGISPECT_PRO_PLUS_ALIAS string = "PRO_PLUS"
)

func IsDigiSpectPackage(pkg string) bool {
	if pkg == CLIENT_PACKAGE_DIGISPECT_PRO || pkg == CLIENT_PACKAGE_DIGISPECT_PRO_PLUS {
		return true
	}

	return false
}

func DigiSpectPackageAlias(pkg string) string {
	if pkg == CLIENT_PACKAGE_DIGISPECT_PRO {
		return CLIENT_PACKAGE_DIGISPECT_PRO_ALIAS
	} else if pkg == CLIENT_PACKAGE_DIGISPECT_PRO_PLUS {
		return CLIENT_PACKAGE_DIGISPECT_PRO_ALIAS
	}

	return ""
}
