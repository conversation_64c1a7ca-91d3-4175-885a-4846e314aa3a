package dtos

type UserBulkUpload struct {
	ID                 string     `json:"id"`
	BulkUploadCode     string     `json:"bulk_upload_code"`
	BulkUpload         BulkUpload `json:"bulk_upload"`
	StatusCode         string     `json:"status_code"`
	OriginalFilePath   string     `json:"original_file_path"`
	ResultFilePath     string     `json:"result_file_path"`
	NumberOfSuccessRow int        `json:"number_of_success_row"`
	NumberOfFailedRow  int        `json:"number_of_failed_row"`
}

type BulkUpload struct {
	Code         string `json:"code"`
	Label        string `json:"label"`
	TemplatePath string `json:"template_path"`
	Description  string `json:"description"`
}
