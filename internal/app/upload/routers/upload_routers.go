package routers

import (
	"assetfindr/internal/app/upload/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterUploadRoutes(route *gin.Engine, uploadHandler handler.UploadHandler) *gin.Engine {

	bulkRoute := route.Group("/v1/uploads", middleware.TokenValidationMiddleware())
	{
		bulkRoute.GET("/bulk", uploadHandler.GetBulkUploadsList)
		bulkRoute.GET("/bulk/:id", uploadHandler.GetBulkUpload)
	}

	return route
}
