package handler

import (
	"assetfindr/internal/app/upload/usecase"
	"assetfindr/pkg/common/commonmodel"
	"net/http"

	"github.com/gin-gonic/gin"
)

type UploadHandler struct {
	uploadUsecase usecase.UploadUseCase
}

func NewUploadRepoHandler(uploadUsecase usecase.UploadUseCase) *UploadHandler {
	return &UploadHandler{
		uploadUsecase: uploadUsecase,
	}
}

func (h *UploadHandler) GetBulkUploadsList(c *gin.Context) {
	ctx := c.Request.Context()
	req := commonmodel.ListRequest{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.uploadUsecase.GetBulkUploadsList(ctx, req)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON><PERSON><PERSON>(http.StatusOK, resp)
}

func (h *UploadHandler) GetBulkUpload(c *gin.Context) {
	ctx := c.Request.Context()

	id := c.Param("id")
	resp, err := h.uploadUsecase.GetBulkUpload(ctx, id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}
