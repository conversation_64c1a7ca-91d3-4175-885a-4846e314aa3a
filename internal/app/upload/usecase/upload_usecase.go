package usecase

import (
	"assetfindr/internal/app/upload/dtos"
	"assetfindr/internal/app/upload/models"
	"assetfindr/internal/app/upload/repository"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
	"time"
)

type UploadUseCase struct {
	DB               database.DBUsecase
	UploadRepository repository.UploadRepository
}

func NewUploadUsecase(
	DB database.DBUsecase,
	UploadRepo repository.UploadRepository,
) UploadUseCase {
	return UploadUseCase{
		DB:               DB,
		UploadRepository: UploadRepo,
	}
}

func (uc *UploadUseCase) GetBulkUploadsList(ctx context.Context, req commonmodel.ListRequest) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	total, userBulkUploads, err := uc.UploadRepository.GetUserBulkUploadList(ctx, uc.DB.DB(), models.GetUserBulkUploadListParam{
		ListRequest: req,
		Cond: models.UserBulkUploadCondition{
			Where: models.UserBulkUploadWhere{ClientID: claim.GetLoggedInClientID()},
			Preload: models.UserBulkUploadPreload{
				BulkUpload: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	resp := make([]dtos.UserBulkUpload, 0, len(userBulkUploads))
	for _, userBulkUpload := range userBulkUploads {
		resp = append(resp, dtos.UserBulkUpload{
			ID:             userBulkUpload.ID,
			BulkUploadCode: userBulkUpload.BulkUploadCode,
			BulkUpload: dtos.BulkUpload{
				Code:         userBulkUpload.BulkUpload.Code,
				Label:        userBulkUpload.BulkUpload.Label,
				TemplatePath: userBulkUpload.BulkUpload.TemplatePath,
				Description:  userBulkUpload.BulkUpload.Description,
			},
			StatusCode:         userBulkUpload.StatusCode,
			OriginalFilePath:   userBulkUpload.OriginalFilePath,
			ResultFilePath:     userBulkUpload.ResultFilePath,
			NumberOfSuccessRow: userBulkUpload.NumberOfSuccessRow,
			NumberOfFailedRow:  userBulkUpload.NumberOfFailedRow,
		})
	}
	return &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         resp,
	}, nil
}

func (uc *UploadUseCase) GetBulkUpload(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	userBulkUpload, err := uc.UploadRepository.GetUserBulkUpload(ctx, uc.DB.DB(), models.UserBulkUploadCondition{
		Where: models.UserBulkUploadWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.UserBulkUploadPreload{
			BulkUpload: true,
		},
	})
	if err != nil {
		return nil, err
	}

	templatePath := userBulkUpload.BulkUpload.TemplatePath
	templatePathUrl, _ := helpers.GenerateCloudStorageSignedURL(userBulkUpload.BulkUpload.TemplatePath, time.Duration(24))
	if templatePathUrl != "" {
		templatePath = templatePathUrl
	}

	originalFilePath := userBulkUpload.ResultFilePath
	originalFilePathUrl, _ := helpers.GenerateCloudStorageSignedURL(userBulkUpload.OriginalFilePath, time.Duration(24))
	if originalFilePathUrl != "" {
		originalFilePath = originalFilePathUrl
	}

	resultFilePath := userBulkUpload.ResultFilePath
	resultFilePathUrl, _ := helpers.GenerateCloudStorageSignedURL(userBulkUpload.ResultFilePath, time.Duration(24))
	if resultFilePathUrl != "" {
		resultFilePath = resultFilePathUrl
	}

	resp := dtos.UserBulkUpload{
		ID:             userBulkUpload.ID,
		BulkUploadCode: userBulkUpload.BulkUploadCode,
		BulkUpload: dtos.BulkUpload{
			Code:         userBulkUpload.BulkUpload.Code,
			Label:        userBulkUpload.BulkUpload.Label,
			TemplatePath: templatePath,
			Description:  userBulkUpload.BulkUpload.Description,
		},
		StatusCode:         userBulkUpload.StatusCode,
		OriginalFilePath:   originalFilePath,
		ResultFilePath:     resultFilePath,
		NumberOfSuccessRow: userBulkUpload.NumberOfSuccessRow,
		NumberOfFailedRow:  userBulkUpload.NumberOfFailedRow,
	}
	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: userBulkUpload.ID,
		Data:        resp,
	}, nil
}
