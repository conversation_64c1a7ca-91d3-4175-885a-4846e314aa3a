package routers

import (
	"assetfindr/internal/app/log/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterLogRoutes(route *gin.Engine, logHandler *handler.LogHandler) *gin.Engine {
	logRoutes := route.Group("/v1/logs", middleware.TokenValidationMiddleware())
	{
		logRoutes.GET("", logHandler.GetLogs)
		logRoutes.GET("/:id", logHandler.GetLog)
		logRoutes.GET("/references", logHandler.GetLogReferences)
	}

	logV2Routes := route.Group("/v2/logs", middleware.TokenValidationMiddleware())
	{
		logV2Routes.GET("", logHandler.GetLogV2s)
		logV2Routes.GET("/references", logHandler.GetLogReferenceV2s)
	}

	logV2JobRoutes := route.Group("/v2/jobs/logs", middleware.APITokenMiddleware)
	{
		logV2JobRoutes.POST("/parse", logHandler.ParseLogs)
	}
	return route
}
