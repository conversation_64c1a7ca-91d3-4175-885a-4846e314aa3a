package usecase

import (
	assetModels "assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/content/models"
	"assetfindr/internal/app/log/constants"
	"context"
	"fmt"
)

var AssetParser = map[string]logParser{
	"name":                          {Label: "Name", Type: constants.LiteralValue},
	"brand_id":                      {Label: "Brand", Type: constants.FuncParser, Func: parseBrandName},
	"model_number":                  {Label: "Model Number", Type: constants.LiteralValue},
	"serial_number":                 {Label: "Serial Number", Type: constants.LiteralValue},
	"production_date":               {Label: "Production Date", Type: constants.LiteralValue},
	"asset_status_code":             {Label: "Asset Status", Type: constants.FuncParser, Func: parseAssetStatus},
	"cost":                          {Label: "Cost", Type: constants.LiteralValue},
	"location_id":                   {Label: "Location", Type: constants.<PERSON>c<PERSON>ars<PERSON>, Func: parseLocation},
	"ownership_category_code":       {Label: "Ownership Category", Type: constants.Func<PERSON>ars<PERSON>, Func: parseTitleCase},
	"status_inactive_total_time":    {Label: "Status Inactive Total Time", Type: constants.Undefined},
	"status_inactive_start_time":    {Label: "Status Inactive Start Time", Type: constants.Undefined},
	"reference_number":              {Label: "Plate Number", Type: constants.LiteralValue},
	"partner_owner_no":              {Label: "Partner Owner No", Type: constants.LiteralValue},
	"rfid":                          {Label: "Rfid", Type: constants.LiteralValue},
	"initial_condition_code":        {Label: "Initial Condition Code", Type: constants.FuncParser, Func: parseTitleCase},
	"partner_owner_name":            {Label: "Partner Owner Name", Type: constants.LiteralValue},
	"gps_imei":                      {Label: "GPS IMEI", Type: constants.LiteralValue},
	"handover_form_template_id":     {Label: "Handover Form Template", Type: constants.FuncParser, Func: parseTemplateID},
	"handover_need_inspection":      {Label: "Is Handover Need Inspection", Type: constants.LiteralValue},
	"custom_asset_category_id":      {Label: "Category", Type: constants.FuncParser, Func: parseAssetCustomCategory},
	"custom_asset_sub_category_id":  {Label: "Sub Category", Type: constants.FuncParser, Func: parseAssetCustomSubCategory},
	"use_tyre_optimax":              {Label: "Tyre Optimax", Type: constants.LiteralValue},
	"use_fleet_optimax":             {Label: "Fleet Optimax", Type: constants.LiteralValue},
	"downgrade_reason":              {Label: "Downgrade Reason", Type: constants.LiteralValue},
	"downgrade_tyre_optimax_reason": {Label: "Downgrade Tyre Optimax Reason", Type: constants.LiteralValue},
	"model_id":                      {Label: "Model", Type: constants.FuncParser, Func: parseAssetModel},
}

var AssetVehicle = map[string]logParser{
	"number_of_tyres":        {Label: "Number Of Tyres", Type: constants.LiteralValue},
	"number_of_spare_tyres":  {Label: "Number Of Spare Tyres", Type: constants.LiteralValue},
	"vehicle_km":             {Label: "Vehicle KM", Type: constants.LiteralValue},
	"vehicle_hm":             {Label: "Vehicle HM", Type: constants.LiteralValue},
	"use_kilometer":          {Label: "Use Kilometer", Type: constants.LiteralValue},
	"use_hourmeter":          {Label: "Use Hourmeter", Type: constants.LiteralValue},
	"max_rtd_diff_tolerance": {Label: "Max Rtd Diff Tolerance", Type: constants.LiteralValue},
}

var AssetTyre = map[string]logParser{
	"datetime_of_last_check":              {Label: "Datetime Of Last Check", Type: constants.LiteralValue},
	"average_rtd":                         {Label: "Average Rtd", Type: constants.LiteralValue},
	"utilization_rate_percentage":         {Label: "Utilization Rate Percentage", Type: constants.LiteralValue},
	"total_km":                            {Label: "Total Km", Type: constants.LiteralValue},
	"projected_life_km":                   {Label: "Projected Life Km", Type: constants.LiteralValue},
	"pressure":                            {Label: "Pressure", Type: constants.LiteralValue},
	"retread_number":                      {Label: "Retread Number", Type: constants.LiteralValue},
	"repaired_number":                     {Label: "Repaired Number", Type: constants.LiteralValue},
	"dot_code":                            {Label: "Dot Code", Type: constants.LiteralValue},
	"date_code":                           {Label: "Date Code", Type: constants.LiteralValue},
	"asset_id":                            {Label: "Asset", Type: constants.FuncParser, Func: parseAssetID},
	"tyre_id":                             {Label: "Tyre", Type: constants.FuncParser, Func: parseTyreID},
	"start_thread_depth":                  {Label: "Start Thread Depth", Type: constants.LiteralValue},
	"total_lifetime":                      {Label: "Total Lifetime", Type: constants.LiteralValue},
	"total_hm":                            {Label: "Total Hm", Type: constants.LiteralValue},
	"meter_calculation_code":              {Label: "Meter Calculation Code", Type: constants.LiteralValue},
	"average_rtd_last_updated_at":         {Label: "Average Rtd Last Updated At", Type: constants.LiteralValue},
	"pressure_last_updated_at":            {Label: "Pressure Last Updated At", Type: constants.LiteralValue},
	"temperature":                         {Label: "Temperature", Type: constants.LiteralValue},
	"temperature_last_updated_at":         {Label: "Temperature Last Updated At", Type: constants.LiteralValue},
	"pressure_last_updated_sensor_ref":    {Label: "Pressure Last Updated Sensor Ref", Type: constants.LiteralValue},
	"temperature_last_updated_sensor_ref": {Label: "Temperature Last Updated Sensor Ref", Type: constants.LiteralValue},
	"rtd1":                                {Label: "Rtd1", Type: constants.LiteralValue},
	"rtd2":                                {Label: "Rtd1", Type: constants.LiteralValue},
	"rtd3":                                {Label: "Rtd3", Type: constants.LiteralValue},
	"rtd4":                                {Label: "Rtd4", Type: constants.LiteralValue},
}

var Retread = map[string]logParser{
	"thread_sequence": {Label: "Retread Count", Type: constants.LiteralValue},
	"brand_name":      {Label: "Retread Brand", Type: constants.LiteralValue},
	"retread_type":    {Label: "Retread Type", Type: constants.LiteralValue},
	"original_td":     {Label: "Retread Original Tread Depth", Type: constants.LiteralValue},
	"cost":            {Label: "Retread Cost", Type: constants.LiteralValue},
	"notes":           {Label: "Comment", Type: constants.LiteralValue},
}

func parseAssetStatus(ctx context.Context, uc *LogUseCase, code interface{}) (interface{}, error) {
	codeString, ok := code.(string)
	if !ok {
		return nil, fmt.Errorf("code should be string")
	}

	assetStatus, err := uc.assetRepo.GetAssetStatusByCode(ctx, uc.DB.DB(), codeString)
	if err != nil {
		return nil, err
	}

	return assetStatus.Label, nil
}

func parseLocation(ctx context.Context, uc *LogUseCase, id interface{}) (interface{}, error) {
	idString, ok := id.(string)
	if !ok {
		return nil, fmt.Errorf("id should be string")
	}

	location, err := uc.locationRepo.GetLocation(ctx, uc.DB.DB(), assetModels.LocationCondition{
		Where: assetModels.LocationWhere{
			ID:             idString,
			WithOrmDeleted: true,
		},
	})
	if err != nil {
		return nil, err
	}

	return location.Name, nil
}

func parseAssetModel(ctx context.Context, uc *LogUseCase, id interface{}) (interface{}, error) {
	idString, ok := id.(string)
	if !ok {
		return nil, fmt.Errorf("id should be string")
	}

	location, err := uc.assetModelRepo.GetAssetModel(ctx, uc.DB.DB(), assetModels.AssetModelCondition{
		Where: assetModels.AssetModelWhere{
			ID: idString,
		},
	})
	if err != nil {
		return nil, err
	}

	return location.AssetModelName, nil
}

func parseAssetCustomCategory(ctx context.Context, uc *LogUseCase, id interface{}) (interface{}, error) {
	idString, ok := id.(string)
	if !ok {
		return nil, fmt.Errorf("id should be string")
	}

	assetCategory, err := uc.customAssetCategory.GetCustomAssetCategory(ctx, uc.DB.DB(), assetModels.CustomAssetCategoryCondition{
		Where: assetModels.CustomAssetCategoryWhere{
			ID:             idString,
			WithOrmDeleted: true,
		},
	})
	if err != nil {
		return nil, err
	}

	return assetCategory.Name, nil
}
func parseAssetCustomSubCategory(ctx context.Context, uc *LogUseCase, id interface{}) (interface{}, error) {
	idString, ok := id.(string)
	if !ok {
		return nil, fmt.Errorf("id should be string")
	}

	assetSubCategory, err := uc.customAssetCategory.GetCustomAssetSubCategory(ctx, uc.DB.DB(), assetModels.CustomAssetSubCategoryCondition{
		Where: assetModels.CustomAssetSubCategoryWhere{
			ID:             idString,
			WithOrmDeleted: true,
		},
	})
	if err != nil {
		return nil, err
	}

	return assetSubCategory.Name, nil
}

func parseTemplateID(ctx context.Context, uc *LogUseCase, id interface{}) (interface{}, error) {
	idString, ok := id.(string)
	if !ok {
		return nil, fmt.Errorf("id should be string")
	}

	templat, err := uc.formRepo.GetFormTemplate(ctx, uc.DB.DB(), models.FormTemplateCondition{
		Where: models.FormTemplateWhere{
			ID:             idString,
			WithOrmDeleted: true,
		},
	})
	if err != nil {
		return nil, err
	}

	return templat.Name, nil
}

type Tyre struct {
	IsObject           bool    `json:"isObject"`
	PatternType        string  `json:"Pattern/Type"`
	OriginalTd         float64 `json:"Original Tread Depth"`
	ConstructionType   string  `json:"Tube Type/Tubeless"`
	PlyRating          float64 `json:"Ply Rating"`
	LoadRating         string  `json:"Load Index(Single/Dual)"`
	SpeedIndex         string  `json:"Speed Symbol"`
	StarRating         float64 `json:"Star Rating"`
	TRACode            string  `json:"TRA Code"`
	SectionWidth       string  `json:"Aspect Ratio"`
	Construction       string  `json:"Construction"`
	RimDiameter        string  `json:"Rim Diameter"`
	RecommendedRimSize string  `json:"Recommended Rim Width"`
	TyreSize           string  `json:"Tyre Size"`
}

type Asset struct {
	IsObject      bool   `json:"isObject"`
	SerialNumber  string `json:"Serial Number"`
	Rfid          string `json:"RFID"`
	Status        string `json:"Status"`
	Brand         string `json:"Brand"`
	TyreCondition string `json:"Tyre Size"`
	CustomerName  string `json:"Customer Name,omitempty"`
}

func parseTyreID(ctx context.Context, uc *LogUseCase, id interface{}) (interface{}, error) {
	idString, ok := id.(string)
	if !ok {
		return nil, fmt.Errorf("id should be string")
	}

	tyre, err := uc.assetTyreRepo.GetTyre(ctx, uc.DB.DB(), assetModels.TyreCondition{
		Where: assetModels.TyreWhere{
			ID:             idString,
			WithOrmDeleted: true,
		},
	})
	if err != nil {
		return nil, err
	}

	return Tyre{
		IsObject:           true,
		PatternType:        tyre.PatternType,
		OriginalTd:         tyre.OriginalTd,
		ConstructionType:   tyre.ConstructionType,
		PlyRating:          tyre.PlyRating,
		LoadRating:         tyre.LoadRating,
		SpeedIndex:         tyre.SpeedIndex,
		StarRating:         tyre.StarRating,
		TRACode:            tyre.TRACode,
		SectionWidth:       tyre.SectionWidth,
		Construction:       tyre.Construction,
		RimDiameter:        tyre.RimDiameter,
		RecommendedRimSize: tyre.RecommendedRimSize,
		TyreSize:           tyre.GetTyreSize(),
	}, nil
}

func parseAssetID(ctx context.Context, uc *LogUseCase, id interface{}) (interface{}, error) {
	idString, ok := id.(string)
	if !ok {
		return nil, fmt.Errorf("id should be string")
	}

	asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModels.AssetCondition{
		Where: assetModels.AssetWhere{
			ID:             idString,
			WithOrmDeleted: true,
		},
		Preload: assetModels.AssetPreload{
			Brand: true,
		},
	})
	if err != nil {
		return nil, err
	}

	return Asset{
		IsObject:      true,
		SerialNumber:  asset.SerialNumber,
		Rfid:          asset.Rfid,
		Status:        asset.AssetStatusCode,
		Brand:         asset.Brand.BrandName,
		TyreCondition: asset.InitialConditionCode,
		CustomerName:  asset.PartnerOwnerName,
	}, nil
}
