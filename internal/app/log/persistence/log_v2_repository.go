package persistence

import (
	"assetfindr/internal/app/log/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

func enrichLogReferenceV2QueryWithWhere(query *gorm.DB, where models.LogReferenceV2Where) {
	query.Joins("JOIN log_logs ON log_logs.id = log_log_references.log_id")
	if where.ID != "" {
		query.Where("log_log_references.id = ?", where.ID)
	} // ID

	if where.ReferenceCode != "" {
		query.Where("log_log_references.reference_code = ?", where.ReferenceCode)
	}

	if where.ReferenceID != "" {
		query.Where("log_log_references.reference_id = ?", where.ReferenceID)
	}

	if where.ClientID != "" {
		query.Where("log_log_references.client_id = ?", where.ClientID)
	} // ClientID

	if where.OnlyParsedLog {
		query.Where("log_logs.is_parsed IS TRUE")
	} // OnlyParsedLog

	if where.HideBlankParsedLog {
		query.Where("log_logs.parsed_new_value != '{}' OR log_logs.parsed_previous_value != '{}'")
	} // OnlyParsedLog
}

func enrichLogReferenceV2QueryWithPreload(query *gorm.DB, where models.LogReferenceV2Preload) {
	if where.Log {
		query.Preload("Log")
	} // Log
}

func enrichLogV2QueryWithWhere(query *gorm.DB, where models.LogV2Where) {
	if where.ID != "" {
		query.Where("log_logs.id = ?", where.ID)
	} // ID

	if where.ClientID != "" {
		query.Where("log_logs.client_id = ?", where.ClientID)
	} // ClientID

	if where.IsParsed.Valid {
		query.Where("log_logs.is_parsed = ?", where.IsParsed.Bool)
	} // IsParsed

	if where.CategoryCode != "" {
		query.Where("log_logs.category_code = ?", where.CategoryCode)
	}

	if where.ActionBy != "" {
		query.Where("log_logs.created_by = ?", where.ActionBy)
	}

	if !where.StartDate.IsZero() {
		query.Where("log_logs.created_at >= ?", where.StartDate)
	}

	if !where.EndDate.IsZero() {
		query.Where("log_logs.created_at <= ?", where.EndDate)
	}

	if where.ReferenceID != "" || where.ReferenceCode != "" {
		query.Joins("JOIN log_log_references ON log_log_references.log_id = log_logs.id")

		if where.ReferenceID != "" {
			query.Where("log_log_references.reference_id = ?", where.ReferenceID)
		}

		if where.ReferenceCode != "" {
			query.Where("log_log_references.reference_code = ?", where.ReferenceCode)
		}
	}
}

func enrichLogV2QueryWithPreload(query *gorm.DB, preload models.LogV2Preload) {
	if preload.NonAssetLogReferenceV2 {
		query.Preload("LogReferenceV2s", func(db *gorm.DB) *gorm.DB {
			return db.Where("reference_code != 'ASSET'")
		})
	} // LogReferenceV2

	if preload.LogReferenceV2 {
		query.Preload("LogReferenceV2s")
	} // LogReferenceV2

	if preload.LogCategory {
		query.Preload("LogCategory")
	} // LogCategory
}

func (r *LogRepository) GetLogV2s(ctx context.Context, dB database.DBI, cond models.LogV2Condition) ([]models.LogV2, error) {
	logV2 := []models.LogV2{}
	query := dB.GetOrm().Model(&logV2)

	enrichLogV2QueryWithWhere(query, cond.Where)
	enrichLogV2QueryWithPreload(query, cond.Preload)
	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.Find(&logV2).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("log")
		}

		return nil, err
	}

	return logV2, nil
}

func (r *LogRepository) GetLogV2List(ctx context.Context, dB database.DBI, param models.GetLogV2ListParam) ([]models.LogV2, error) {
	logV2 := []models.LogV2{}
	query := dB.GetOrm().Model(&logV2)

	enrichLogV2QueryWithWhere(query, param.Cond.Where)
	enrichLogV2QueryWithPreload(query, param.Cond.Preload)
	if len(param.Cond.Columns) > 0 {
		query.Select(param.Cond.Columns)
	}

	query.Order("log_logs.created_at DESC")

	if param.NextCursor != "" {
		query.Where("log_logs.created_at < ?", param.NextCursor)
	}

	err := query.Limit(param.PageSize).Find(&logV2).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("log")
		}

		return nil, err
	}

	return logV2, nil
}

func (r *LogRepository) ParseLog(ctx context.Context, dB database.DBI, log *models.LogV2) error {
	return dB.GetTx().
		Where("id = ?", log.ID).
		Updates(&models.LogV2{
			ParsedPreviousValue: log.ParsedPreviousValue,
			ParsedNewValue:      log.ParsedNewValue,
			CategoryCode:        log.CategoryCode,
			IsParsed:            true,
			ParsedAt:            null.TimeFrom(time.Now().UTC()),
		}).Error
}

func (r *LogRepository) GetLogReferenceV2List(ctx context.Context, dB database.DBI, param models.GetLogReferenceV2ListParam) ([]models.LogReferenceV2, error) {
	locs := []models.LogReferenceV2{}
	query := dB.GetTx().Model(&locs)
	enrichLogReferenceV2QueryWithWhere(query, param.Cond.Where)
	enrichLogReferenceV2QueryWithPreload(query, param.Cond.Preload)

	query.Order("log_log_references.created_at DESC")

	if param.NextCursor != "" {
		query.Where("log_log_references.created_at < ?", param.NextCursor)
	}
	err := query.Limit(param.PageSize).Find(&locs).Error
	if err != nil {
		return nil, err
	}

	return locs, nil

}
