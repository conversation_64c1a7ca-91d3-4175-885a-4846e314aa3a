package repository

import (
	"assetfindr/internal/app/log/models"
	"assetfindr/internal/infrastructure/bq"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type LogRepository interface {
	GetLogReferenceList(ctx context.Context, bQ bq.BQI, req models.GetLogReferenceListParam) ([]models.LogReference, error)
	GetLog(ctx context.Context, bQ bq.BQI, id, clientID string) (*models.Log, error)
	GetLogs(ctx context.Context, bQ bq.BQI, ids []string, clientID string) ([]models.Log, error)

	// Log V2
	GetLogV2s(ctx context.Context, dB database.DBI, cond models.LogV2Condition) ([]models.LogV2, error)
	GetLogV2List(ctx context.Context, dB database.DBI, param models.GetLogV2ListParam) ([]models.LogV2, error)
	GetLogReferenceV2List(ctx context.Context, dB database.DBI, param models.GetLogReferenceV2ListParam) ([]models.LogReferenceV2, error)
	ParseLog(ctx context.Context, dB database.DBI, log *models.LogV2) error
}
