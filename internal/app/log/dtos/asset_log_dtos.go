package dtos

import "time"

type AssetLog struct {
	ID              string    `json:"id"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	UpdatedByUserID string    `json:"updated_by_user_id"`
	AssetID         string    `json:"asset_id"`
	PreviousValue   string    `json:"previous_value"`
	NewValue        string    `json:"new_value"`
	ClientID        string    `json:"client_id"`
	Type            string    `json:"type"`
}
