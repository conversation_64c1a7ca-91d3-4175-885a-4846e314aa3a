package dtos

import (
	"assetfindr/internal/app/log/models"
	"assetfindr/pkg/common/commonmodel"
	"time"
)

type LogReference struct {
	ID            string       `json:"id"`
	LogID         string       `json:"log_id"`
	ReferenceCode string       `json:"reference_code"`
	ReferenceID   string       `json:"reference_id"`
	ClientID      string       `json:"client_id"`
	CreatedAt     time.Time    `json:"created_at"`
	CreatedBy     string       `json:"created_by"`
	UserName      string       `json:"user_name"`
	Log           models.LogV2 `json:"log"`
}

type GetLogReferencesReq struct {
	commonmodel.ListCursorRequest
	ReferenceCode string `form:"reference_code"`
	ReferenceID   string `form:"reference_id"`
}
