package handler

import (
	"assetfindr/internal/app/log/dtos"
	"assetfindr/internal/app/log/usecase"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonmodel"
	"net/http"

	"github.com/gin-gonic/gin"
)

type LogHandler struct {
	logUseCase *usecase.LogUseCase
}

func NewLogHandler(
	logUseCase *usecase.LogUseCase,
) *LogHandler {
	return &LogHandler{
		logUseCase: logUseCase,
	}
}

func (h *LogHandler) GetLogReferences(c *gin.Context) {
	ctx := c.Request.Context()

	req := dtos.GetLogReferencesReq{}
	err := c.Bind<PERSON>y(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	resp, err := h.logUseCase.GetLogReferences(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.<PERSON>(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *LogHandler) GetLog(c *gin.Context) {
	ctx := c.Request.Context()

	id := c.Param("id")
	resp, err := h.logUseCase.GetLog(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *LogHandler) GetLogs(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.LogListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	resp, err := h.logUseCase.GetLogs(ctx, req.IDs)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *LogHandler) GetLogV2s(c *gin.Context) {
	ctx := c.Request.Context()

	req := dtos.GetLogsReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	resp, err := h.logUseCase.GetLogV2s(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *LogHandler) GetLogReferenceV2s(c *gin.Context) {
	ctx := c.Request.Context()

	req := dtos.GetLogReferencesReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	resp, err := h.logUseCase.GetLogReferenceV2s(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *LogHandler) ParseLogs(c *gin.Context) {
	ctx := c.Request.Context()
	go h.logUseCase.ParseLogs(ctx)
	c.JSON(http.StatusOK, &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	})
}
