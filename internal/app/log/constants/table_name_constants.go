package constants

const (
	TABLE_NAME_ASSET              string = "ams_assets"
	TABLE_NAME_ASSET_VEHICLE      string = "ams_asset_vehicles"
	TABLE_NAME_ASSET_TYRE         string = "ams_asset_tyres"
	TABLE_NAME_TICKET             string = "tks_tickets"
	TABLE_NAME_ASSET_COMPONENTS   string = "ams_asset_components"
	TABLE_NAME_ASSET_TRANSACTIONS string = "ams_asset_transactions"
	TABLE_NAME_ASSET_TYRES_TREAD  string = "ams_asset_tyres_treads"
	// TABLE_NAME_LINKED_ASSETS           string = "ams_linked_assets"
	// TABLE_NAME_ASSET_HANDOVER_REQUESTS string = "ams_asset_handover_requests"
	// TABLE_NAME_ASSET_ASSIGNMENTS       string = "ams_asset_assignments"
	// TABLE_NAME_ASSET_INSPECTION_TYRE string = "ams_asset_inspection_tyre"
)

type LogParserType int

const (
	Undefined LogParserType = iota
	Ignored
	LiteralValue
	FuncParser
)
