package routers

import (
	"assetfindr/internal/app/truphone/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterTruphoneRoutes(route *gin.Engine, truphoneHandler handler.TruphoneHandler) *gin.Engine {

	truphoneRoute := route.Group("/v1/truphone", middleware.APITokenMiddleware)
	{
		truphoneRoute.GET("/check-usage", truphoneHandler.CheckUsage)
		truphoneRoute.POST("/send-sms", truphoneHandler.SendSMS)
	}

	return route
}
