package usecase

import (
	integrationModels "assetfindr/internal/app/integration/models"
	integrationRepo "assetfindr/internal/app/integration/repository"
	notificationModels "assetfindr/internal/app/notification/models"
	notificationRepo "assetfindr/internal/app/notification/repository"
	"assetfindr/internal/app/truphone/constants"
	"assetfindr/internal/app/truphone/dtos"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"net/url"
	"strings"
	"time"
)

type TruphoneUseCase struct {
	DB              database.DBUsecase
	EmailRepository notificationRepo.EmailRepository
	IntegrationRepo integrationRepo.IntegrationRepository
}

func NewTruphoneUseCase(
	DB database.DBUsecase,
	EmailRepository notificationRepo.EmailRepository,
	IntegrationRepo integrationRepo.IntegrationRepository,
) TruphoneUseCase {
	return TruphoneUseCase{
		DB:              DB,
		EmailRepository: EmailRepository,
		IntegrationRepo: IntegrationRepo,
	}
}

func (uc *TruphoneUseCase) constructTruphoneMailBody(integrationNameMapByICCID map[string]string, usageArray []dtos.OnlineSimsUsageResponse) string {
	mailBody := ""
	mailItemPos := 1

	mailBody += `
		<table style="border-collapse: collapse;">
			<tr>
				<th style="border: 1px solid #dddddd;">No</th>
				<th style="border: 1px solid #dddddd;">ICCID</th>
				<th style="border: 1px solid #dddddd;">Installed At</th>
				<th style="border: 1px solid #dddddd;">Total Usage</th>
			</tr>`

	for _, usageData := range usageArray {
		rowStyle := "border: 1px solid #dddddd;"
		if usageData.IsDailyLimitExceeded {
			rowStyle = "border: 1px solid #dddddd; background-color: red;"
		}
		if usageData.Status == constants.TRUPHONE_STATUS_EXPIRING {
			rowStyle = "border: 1px solid #dddddd; background-color: yellow;"
		}

		installedAt := integrationNameMapByICCID[usageData.Iccid]
		if installedAt == "" {
			installedAt = "-"
		}

		totalUsage := fmt.Sprintf("%.2fMB", usageData.AllTimeUsage)

		mailBody += fmt.Sprintf(`
				<tr>
					<td style="%v">%v</td>
					<td style="%v">%v</td>
					<td style="%v">%v</td>
					<td style="%v">%v</td>
				</tr>
			`, rowStyle, mailItemPos, rowStyle, usageData.Iccid, rowStyle, installedAt, rowStyle, totalUsage)
		mailItemPos += 1
	}

	mailBody += `
		</table>
		`

	return mailBody
}

func (uc *TruphoneUseCase) constructTruphoneOnlineSimsUsageData(ctx context.Context, onlineSimsArr []interface{}) ([]string, []dtos.OnlineSimsUsageResponse, error) {
	var iccids []string
	var onlineSimsUsageResponseArray []dtos.OnlineSimsUsageResponse

	// GET TODAY
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	formattedToday := today.Format("2006-01-02 15:04:05")

	currentMonthFirstDay := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	currentMonthFirstDayFormatted := currentMonthFirstDay.Format("2006-01-02 15:04:05")
	currentMonthLastDay := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, now.Location()).Add(-time.Second)
	currentMonthLastDayFormatted := currentMonthLastDay.Format("2006-01-02 15:04:05")

	for _, onlineSim := range onlineSimsArr {
		if onlineSimObj, ok := onlineSim.(map[string]interface{}); ok {
			// GET SIM DETAIL
			iccid := onlineSimObj["iccid"].(string)
			iccids = append(iccids, iccid)

			simDetailEndpoint := strings.ReplaceAll(constants.TRUPHONE_SIM_DETAIL_ENDPOINT, "{iccid}", iccid)
			simDetail, err := uc.TruphoneGetAPI(ctx, simDetailEndpoint, nil)
			if err != nil {
				fmt.Println(fmt.Sprintf("Failed while fetching sim detail with iccid of %s", iccid))
				return nil, nil, err
			}

			var totalAllTimeUsage float64 = 0
			var totalMonthlyUsage float64 = 0
			var totalDailyUsage float64 = 0
			simCallDetailRecordsEndpoint := strings.ReplaceAll(constants.TRUPHONE_SIM_CALL_DETAIL_RECORDS_ENDPOINT, "{iccid}", iccid)

			if simDetailObj, ok := simDetail.(map[string]interface{}); ok {
				// GET SIM FIRST ACTIVATION DATE
				simDetailDates := simDetailObj["dates"].(map[string]interface{})
				firstActivationDate := simDetailDates["firstActivationDate"].(string)
				firstActivationDateTime, err := time.Parse(time.RFC3339, firstActivationDate)
				if err != nil {
					fmt.Println("Error parsing first activation date time:", err)
					return nil, nil, err
				}
				formattedFirstActivationDate := firstActivationDateTime.Format("2006-01-02 15:04:05")

				// GET SIM USAGE DURATION
				// ALL TIME
				hasMoreAllTimeUsages := true
				allTimeUsagePage := 1
				allTimeUsageQueryParam := map[string]interface{}{
					"type":           "DATA",
					"startDate_from": formattedFirstActivationDate,
					"page":           fmt.Sprintf("%d", allTimeUsagePage),
					"per_page":       "1000",
				}
				for hasMoreAllTimeUsages {
					allTimeUsages, err := uc.TruphoneGetAPI(ctx, simCallDetailRecordsEndpoint, allTimeUsageQueryParam)
					if err != nil {
						hasMoreAllTimeUsages = false
					} else {
						if allTimeUsagesArr, ok := allTimeUsages.([]interface{}); ok {
							for _, allTimeUsage := range allTimeUsagesArr {
								if allTimeUsageObj, ok := allTimeUsage.(map[string]interface{}); ok {
									allTimeUsageDuration := allTimeUsageObj["duration"].(float64)
									totalAllTimeUsage = totalAllTimeUsage + allTimeUsageDuration
								}
							}
						}

						allTimeUsagePage += 1
						allTimeUsageQueryParam["page"] = fmt.Sprintf("%d", allTimeUsagePage)
					}
				}

				// CURRENT MONTH
				hasMoreMonthlyUsages := true
				monthlyUsagePage := 1
				monthlyUsageQueryParam := map[string]interface{}{
					"type":           "DATA",
					"startDate_from": currentMonthFirstDayFormatted,
					"endDate_from":   currentMonthLastDayFormatted,
					"page":           fmt.Sprintf("%d", allTimeUsagePage),
					"per_page":       "1000",
				}
				for hasMoreMonthlyUsages {
					monthlyUsages, err := uc.TruphoneGetAPI(ctx, simCallDetailRecordsEndpoint, monthlyUsageQueryParam)
					if err != nil {
						hasMoreMonthlyUsages = false
					} else {
						if monthlyUsagesArr, ok := monthlyUsages.([]interface{}); ok {
							for _, monthlyUsage := range monthlyUsagesArr {
								if monthlyUsageObj, ok := monthlyUsage.(map[string]interface{}); ok {
									monthlyUsageDuration := monthlyUsageObj["duration"].(float64)
									totalMonthlyUsage = totalMonthlyUsage + monthlyUsageDuration
								}
							}
						}

						monthlyUsagePage += 1
						monthlyUsageQueryParam["page"] = fmt.Sprintf("%d", monthlyUsagePage)
					}
				}

				// TODAY
				hasMoreDailyUsages := true
				dailyUsagePage := 1
				dailyUsageQueryParam := map[string]interface{}{
					"type":           "DATA",
					"startDate_from": formattedToday,
					"page":           fmt.Sprintf("%d", dailyUsagePage),
					"per_page":       "1000",
				}
				for hasMoreDailyUsages {
					dailyUsages, err := uc.TruphoneGetAPI(ctx, simCallDetailRecordsEndpoint, dailyUsageQueryParam)
					if err != nil {
						hasMoreDailyUsages = false
					} else {
						if dailyUsagesArr, ok := dailyUsages.([]interface{}); ok {
							for _, dailyUsage := range dailyUsagesArr {
								if dailyUsageObj, ok := dailyUsage.(map[string]interface{}); ok {
									dailyUsageDuration := dailyUsageObj["duration"].(float64)
									totalDailyUsage = totalDailyUsage + dailyUsageDuration
								}
							}
						}

						dailyUsagePage += 1
						dailyUsageQueryParam["page"] = fmt.Sprintf("%d", dailyUsagePage)
					}
				}
			}

			if totalAllTimeUsage > 0 {
				totalAllTimeUsage = totalAllTimeUsage / 1024
			}
			if totalDailyUsage > 0 {
				totalDailyUsage = totalDailyUsage / 1024
			}
			if totalMonthlyUsage > 0 {
				totalMonthlyUsage = totalMonthlyUsage / 1024
			}
			isAllTimeLimitExceeded := false
			if totalAllTimeUsage > constants.TRUPHONE_ALL_TIME_LIMIT_IN_MB {
				isAllTimeLimitExceeded = true
			}
			isDailyLimitExceeded := false
			if totalDailyUsage > constants.TRUPHONE_DAILY_LIMIT_IN_MB {
				isDailyLimitExceeded = true
			}

			totalRatePlans := math.Ceil(totalAllTimeUsage / constants.TRUPHONE_RATE_PLAN_QUANTIFIER)
			quotaLeft := (totalRatePlans * constants.TRUPHONE_RATE_PLAN_QUANTIFIER) - totalAllTimeUsage
			status := constants.TRUPHONE_STATUS_OK
			if quotaLeft < constants.TRUPHONE_EXPIRING_THRESHOLD_IN_MB {
				status = constants.TRUPHONE_STATUS_EXPIRING
			}
			onlineSimsUsageResponseArray = append(onlineSimsUsageResponseArray, dtos.OnlineSimsUsageResponse{
				Iccid:                  iccid,
				AllTimeUsage:           totalAllTimeUsage,
				IsAllTimeLimitExceeded: isAllTimeLimitExceeded,
				CurrentPlanUsage:       totalMonthlyUsage,
				DailyUsage:             totalDailyUsage,
				IsDailyLimitExceeded:   isDailyLimitExceeded,
				TotalRatePlans:         totalRatePlans,
				QuotaLeft:              quotaLeft,
				Status:                 status,
			})
		}
	}

	return iccids, onlineSimsUsageResponseArray, nil
}

func (uc *TruphoneUseCase) TruphoneGetAPI(ctx context.Context, endpoint string, queryParams map[string]interface{}) (interface{}, error) {
	targetUrl := constants.TRUPHONE_BASE_URL + endpoint
	token := "Token " + constants.TRUPHONE_TOKEN
	parsedTargetUrl, err := url.Parse(targetUrl)
	if err != nil {
		fmt.Println("Error parsing truphone api URL:", err)
		return nil, err
	}

	if queryParams != nil {
		query := parsedTargetUrl.Query()
		for key, value := range queryParams {
			query.Set(key, fmt.Sprintf("%s", value))
		}
		parsedTargetUrl.RawQuery = query.Encode()
	}

	req, err := http.NewRequest("GET", parsedTargetUrl.String(), nil)
	if err != nil {
		fmt.Println("Error creating truphone api request:", err)
		return nil, err
	}
	req.Header.Set("Authorization", token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error fetching truphone api:", err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		errMsg := fmt.Sprintf("Request to truphone api failed with status code: %d\n", resp.StatusCode)
		return nil, errors.New(errMsg)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading truphone api response:", err)
		return nil, err
	}

	var respData interface{}
	err = json.Unmarshal(body, &respData)
	if err != nil {
		fmt.Println("Error unmarshalling truphone api response:", err)
		return nil, err
	}

	return respData, nil
}

func (uc *TruphoneUseCase) TruphonePostAPI(ctx context.Context, endpoint string, requestBody map[string]interface{}) (interface{}, error) {
	targetUrl := constants.TRUPHONE_BASE_URL + endpoint
	token := "Token " + constants.TRUPHONE_TOKEN
	parsedTargetUrl, err := url.Parse(targetUrl)
	if err != nil {
		fmt.Println("Error parsing truphone api URL:", err)
		return nil, err
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Println("Error marshalling JSON of truphone request body:", err)
		return nil, err
	}

	req, err := http.NewRequest("POST", parsedTargetUrl.String(), bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Println("Error creating truphone api request:", err)
		return nil, err
	}
	req.Header.Set("Authorization", token)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error fetching truphone api:", err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		errMsg := fmt.Sprintf("Request to truphone api failed with status code: %d\n", resp.StatusCode)
		return nil, errors.New(errMsg)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading truphone api response:", err)
		return nil, err
	}

	var respData interface{}
	err = json.Unmarshal(body, &respData)
	if err != nil {
		fmt.Println("Error unmarshalling truphone api response:", err)
		return nil, err
	}

	return respData, nil
}

func (uc *TruphoneUseCase) CheckUsage(ctx context.Context) (*commonmodel.DetailResponse, error) {
	mailBody := ""
	mailDestinations := []string{"<EMAIL>"}

	// GET ONLINE SESSION SIMS
	onlineSims, err := uc.TruphoneGetAPI(ctx, constants.TRUPHONE_ONGOING_SESSIONG_ENDPOINT, nil)
	if err != nil {
		fmt.Println("Failed while fetching truphone ongoing sessions")
		return nil, err
	}
	var onlineSimsArr []interface{}
	onlineSimsArr, ok := onlineSims.([]interface{})
	if !ok {
		fmt.Println("Failed parsing truphone online sims array")
		return nil, err
	}

	// GET USAGE DATA
	iccids, onlineSimsUsageResponseArray, err := uc.constructTruphoneOnlineSimsUsageData(ctx, onlineSimsArr)
	if err != nil {
		fmt.Printf("Error while constructing sim usage data: %v", err)
		return nil, err
	}

	// GET INTEGRATION DATA
	integrationNameMapByICCID := map[string]string{}
	integrations, err := uc.IntegrationRepo.GetIntegrations(ctx, uc.DB.DB(), integrationModels.IntegrationCondition{
		Where: integrationModels.IntegrationWhere{
			ICCIDs: iccids,
		},
	})
	if err != nil {
		fmt.Printf("Error while fetching integration data: %v", err)
		return nil, err
	}
	for _, integration := range integrations {
		integrationNameMapByICCID[integration.ICCID] = integration.Name
	}

	mailBody = uc.constructTruphoneMailBody(integrationNameMapByICCID, onlineSimsUsageResponseArray)
	if mailBody != "" {
		err := uc.EmailRepository.SendBasicEmail(ctx, notificationModels.BasicEmailParam{
			Destination: notificationModels.EmailDestination{
				ToAddresses: mailDestinations,
			},
			Subject:  constants.TRUPHONE_MAIL_ALERT_SUBJECT,
			MainBody: mailBody,
		})
		if err != nil {
			fmt.Printf("failed to send email notif %v", err)
		}
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Truphone Usage Data Fetched Successfully",
		ReferenceID: "",
		Data:        onlineSimsUsageResponseArray,
	}, nil
}

func (uc *TruphoneUseCase) SendSMS(ctx context.Context, req dtos.SendSMSRequest) (*commonmodel.DetailResponse, error) {
	truphoneSendSMSReq := map[string]interface{}{
		"iccid": []string{req.Iccid},
		"text":  req.Message,
	}

	truphoneSendSMSResp, err := uc.TruphonePostAPI(ctx, constants.TRUPHONE_SEND_SMS_ENDPOINT, truphoneSendSMSReq)
	if err != nil {
		fmt.Println("Failed while sending truphone sms")
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Truphone SMS Sent Successfully",
		ReferenceID: req.Iccid,
		Data:        truphoneSendSMSResp,
	}, nil
}
