package handler

import (
	"assetfindr/internal/app/truphone/dtos"
	"assetfindr/internal/app/truphone/usecase"
	"assetfindr/internal/errorhandler"
	"net/http"

	"github.com/gin-gonic/gin"
)

type TruphoneHandler struct {
	truphoneUseCase usecase.TruphoneUseCase
}

func NewTruphoneHandler(truphoneUseCase usecase.TruphoneUseCase) TruphoneHandler {
	return TruphoneHandler{
		truphoneUseCase: truphoneUseCase,
	}
}

func (h *TruphoneHandler) CheckUsage(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := h.truphoneUseCase.CheckUsage(ctx)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, resp)
}

func (h *TruphoneHandler) SendSMS(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.SendSMSRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.truphoneUseCase.SendSMS(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
