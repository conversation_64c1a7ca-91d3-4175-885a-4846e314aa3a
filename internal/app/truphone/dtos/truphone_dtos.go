package dtos

type SendSMSRequest struct {
	Iccid   string `json:"iccid" binding:"required"`
	Message string `json:"message" binding:"required"`
}

type OnlineSimsUsageResponse struct {
	Iccid                  string  `json:"iccid"`
	AllTimeUsage           float64 `json:"all_time_usage"`
	IsAllTimeLimitExceeded bool    `json:"is_all_time_limit_exceeded"`
	CurrentPlanUsage       float64 `json:"current_plan_usage"`
	DailyUsage             float64 `json:"daily_usage"`
	IsDailyLimitExceeded   bool    `json:"is_daily_limit_exceeded"`
	TotalRatePlans         float64 `json:"total_rate_plans"`
	QuotaLeft              float64 `json:"quota_left"`
	Status                 string  `json:"status"`
}
