package presistence

import (
	"assetfindr/internal/app/truphone/constants"
	"assetfindr/internal/app/truphone/repository"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
)

type truephoneRepository struct{}

func NewTruphoneRepository() repository.TruphoneRepository {
	return &truephoneRepository{}
}

func (r *truephoneRepository) SendSMS(ctx context.Context, iccid, text string) error {
	truphoneSendSMSReq := map[string]interface{}{
		"iccid": []string{iccid},
		"text":  text,
	}

	reqBody, err := json.Marshal(&truphoneSendSMSReq)
	if err != nil {
		return err
	}

	_, err = r.TruphonePostAPI(ctx, constants.TRUPHONE_SEND_SMS_ENDPOINT, reqBody)
	if err != nil {
		return err
	}

	return nil
}

func (r *truephoneRepository) TruphonePostAPI(ctx context.Context, endpoint string, requestBody []byte) ([]byte, error) {
	targetUrl := constants.TRUPHONE_BASE_URL + endpoint
	token := "Token " + constants.TRUPHONE_TOKEN
	parsedTargetUrl, err := url.Parse(targetUrl)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(http.MethodPost, parsedTargetUrl.String(), bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", token)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request to truphone api failed with status code: %d, resp: %s", resp.StatusCode, string(body))
	}

	return body, nil
}
