package usecase

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
)

func (uc *AssetInspectionUseCase) ChartCountSingleAndLinkedInspections(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartCountSingleAndLinkedInspections(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:      claim.GetLoggedInClientID(),
		StartDatetime: req.StartDatetime,
		EndDatetime:   req.EndDatetime,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartVehicleInspectionFrequency(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartVehicleInspectionFrequency(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:      claim.GetLoggedInClientID(),
		StartDatetime: req.StartDatetime,
		EndDatetime:   req.EndDatetime,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTyreInspectionPerDate(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionTyreRepository.ChartTyreInspectionPerDate(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:      claim.GetLoggedInClientID(),
		StartDatetime: req.StartDatetime,
		EndDatetime:   req.EndDatetime,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartVehicleInspectionPerDate(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionVehicleRepository.ChartVehicleInspectionPerDate(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:      claim.GetLoggedInClientID(),
		StartDatetime: req.StartDatetime,
		EndDatetime:   req.EndDatetime,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTop5TyreBrandBySize(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionTyreRepository.ChartTop5TyreBrandBySize(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:      claim.GetLoggedInClientID(),
		StartDatetime: req.StartDatetime,
		EndDatetime:   req.EndDatetime,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTop5VehicleBrands(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionVehicleRepository.ChartTop5VehicleBrands(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:      claim.GetLoggedInClientID(),
		StartDatetime: req.StartDatetime,
		EndDatetime:   req.EndDatetime,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}
