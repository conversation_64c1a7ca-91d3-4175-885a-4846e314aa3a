package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	userModels "assetfindr/internal/app/user-identity/models"
	intenalConstants "assetfindr/internal/constants"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/exceltmpl"
	"assetfindr/pkg/common/helpers/gmapshelpers"
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/xuri/excelize/v2"
)

func (uc *AssetInspectionUseCase) ExportAssetInspectionTyresV2(ctx context.Context, isTyreView bool, isFromDigiSpect bool, req dtos.ExportInspectionListReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	isViewAll := claim.IsHasPermission(
		constants.INSPECTION_PERMISSION_CATEGORY_TYREOPTIMAX,
		constants.INSPECTION_PERMISSION_VIEW_ALL_DIGISPECT_INSPECTIONS,
	)

	if req.IgnoreViewPermission {
		isViewAll = true
	}

	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
		Where: userModels.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
		Columns: []string{"client_packages", "client_alias"},
	})
	if err != nil {
		return nil, err
	}

	var maxStartDate time.Time
	inspectByUserID := ""
	if !isViewAll {
		inspectByUserID = claim.UserID
	}

	if !req.IgnoreViewPermission {
		maxStartDate, err = uc.getMaxStartDateInspectionDigispect(ctx, claim, client, isViewAll)
		if err != nil {
			return nil, err
		}

		if maxStartDate.IsZero() {
			maxStartDate = time.Now()
		}
	}

	inspectionTyres, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyresV2(ctx, uc.DB.DB(), models.AssetInspectionTyreCondition{
		Where: models.AssetInspectionTyreWhere{
			ClientID:            claim.GetLoggedInClientID(),
			StartDate:           req.StartDate,
			StartDate1:          maxStartDate,
			InspectByUserID:     inspectByUserID,
			EndDate:             req.EndDate,
			InspectionIDs:       req.InspectionIDs,
			DigiSpectOnlySource: isFromDigiSpect,
		},
		Preload: models.AssetInspectionTyrePreload{
			AssetInspection:                  true,
			AssetTyre:                        true,
			AssetTyreAsset:                   true,
			AssetTyreAssetBrand:              true,
			AssetTyreTyre:                    true,
			AssetInspectionVehicle:           true,
			AssetInspectionVehicleAsset:      true,
			AssetInspectionVehicleAssetBrand: true,
			AssetInspectionVehicleAssetModel: true,
		},
	})
	if err != nil {
		return nil, err
	}

	userIDs := []string{}
	attachmentSourceIDs := []string{}
	for i := range inspectionTyres {
		userIDs = append(userIDs, inspectionTyres[i].AssetInspection.InspectByUserID)
		attachmentSourceIDs = append(attachmentSourceIDs, inspectionTyres[i].ID)
		if inspectionTyres[i].AssetInspectionVehicle != nil {
			attachmentSourceIDs = append(attachmentSourceIDs, inspectionTyres[i].AssetInspectionVehicle.ID)
		}
	}
	mapUserName := map[string]string{}
	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for _, user := range users {
			mapUserName[user.ID] = user.GetName()
		}
	}

	mapAttachments := map[string][]string{}
	if len(attachmentSourceIDs) > 0 {
		attachments, err := uc.AttachmentUseCase.GetAttachments(ctx, storageDtos.GetAttachmentsReq{
			SourceReferenceIDs: attachmentSourceIDs,
			ReferenceCode:      storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET_INSPECTION,
		})
		if err != nil {
			return nil, err
		}

		for _, attachment := range attachments {
			mapAttachments[attachment.SourceReferenceID] = append(mapAttachments[attachment.SourceReferenceID], attachment.Id)
		}

		for k, v := range mapAttachments {
			var err error
			mapAttachments[k], err = uc.AttachmentUseCase.AttachmentPublicLinks(ctx, v, claim.GetLoggedInClientID(), client.ClientAlias)
			if err != nil {
				return nil, err
			}
		}
	}

	export := exceltmpl.ExportInspection{
		DowndladedUsername: claim.GetName(),
		Items:              make([]exceltmpl.ExportInspectionItem, len(inspectionTyres)),
	}

	for i, inspectionTyre := range inspectionTyres {
		// condition vehicle photo exist
		export.Items[i], err = uc.BuildExcelExportInspectionItem(
			ctx,
			inspectionTyre,
			mapUserName[inspectionTyre.AssetInspection.InspectByUserID],
			mapAttachments,
			client,
		)
		if err != nil {
			return nil, err
		}

	}

	sort.SliceStable(export.Items, func(i, j int) bool {
		if export.Items[i].InspectionNo == export.Items[j].InspectionNo {
			return export.Items[i].TyrePosition > export.Items[j].TyrePosition
		}

		return false
	})

	var file *excelize.File

	if client.HasPackage(
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_4,
	) {
		if isTyreView {
			file, err = export.ExportToExcelTyreViewScenario4(isTyreView)
		} else {
			file, err = export.ExportToExcelInspectionViewScenario4(isTyreView)
		}
	} else if client.HasPackage(
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_4,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_5,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_6,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_7,
	) {
		file, err = export.ExportToExcelFileWithCustomerData(isTyreView)
	} else {
		file, err = export.ExportToExcelFile(isTyreView)
	}
	if err != nil {
		return nil, err
	}

	signedUrl, xlsxHeader, err := uc.generateAssetInspectionExportSignedUrl(ctx, file)
	if err != nil {
		return nil, err
	}

	defer file.Close()

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: signedUrl,
		Data:        xlsxHeader,
	}, nil
}

func (uc *AssetInspectionUseCase) BuildExcelExportInspectionItem(ctx context.Context, inspectionTyre models.AssetInspectionTyre, inspectedBy string, mapAttachments map[string][]string, client *userModels.Client) (exceltmpl.ExportInspectionItem, error) {
	item := exceltmpl.ExportInspectionItem{
		InspectionDatetime:        inspectionTyre.AssetInspection.CreatedAt,
		InspectionNo:              inspectionTyre.AssetInspection.InspectionNumber,
		TyrePosition:              int(inspectionTyre.TyrePosition),
		AssetTyreSerialNumber:     inspectionTyre.CustomSerialNumber,
		AssetTyreRfid:             inspectionTyre.CustomRFID,
		AssetTyreBrandName:        inspectionTyre.CustomBrandName.String,
		TyreSize:                  inspectionTyre.CustomTyreSize,
		RTD1:                      inspectionTyre.RDT1,
		RTD2:                      inspectionTyre.RDT2,
		RTD3:                      inspectionTyre.RDT3,
		RTD4:                      inspectionTyre.RDT4,
		AverageRTD:                inspectionTyre.AverageRTD,
		UtilizationRatePercentage: inspectionTyre.UtilizationRatePercentage,
		Temperature:               inspectionTyre.Temperature.Float64,
		Pressure:                  inspectionTyre.Pressure,
		InspectedBy:               inspectedBy,
		Notes:                     inspectionTyre.Remark,
		DeviceID:                  inspectionTyre.DeviceID,
		Source:                    constants.GetInspectionExportSourceByCode(inspectionTyre.SourceTypeCode.String),

		Location: inspectionTyre.AssetInspection.LocationLabel,
		LocationPinLink: gmapshelpers.GmapsCoordinateUrl(
			inspectionTyre.AssetInspection.LocationLat.Float64,
			inspectionTyre.AssetInspection.LocationLong.Float64,
		),
	}

	if inspectionTyre.AssetTyreID != "" {
		item.AssetTyreSerialNumber = inspectionTyre.AssetTyre.Asset.SerialNumber
		item.AssetTyreRfid = inspectionTyre.AssetTyre.Asset.Rfid
		item.AssetTyreBrandName = inspectionTyre.AssetTyre.Asset.Brand.BrandName
		item.TyreSize = fmt.Sprintf("%s %s %s", inspectionTyre.AssetTyre.Tyre.SectionWidth, inspectionTyre.AssetTyre.Tyre.Construction, inspectionTyre.AssetTyre.Tyre.RimDiameter)
	}

	if inspectionTyre.AssetInspectionVehicle != nil {
		item.AssetVehicleIdent = inspectionTyre.AssetInspectionVehicle.CustomReferenceNumber.String
		if item.AssetVehicleIdent == "" {
			item.AssetVehicleIdent = inspectionTyre.AssetInspectionVehicle.CustomSerialNumber.String
		} else if inspectionTyre.AssetInspectionVehicle.CustomSerialNumber.String != "" {
			item.AssetVehicleIdent += "/" + inspectionTyre.AssetInspectionVehicle.CustomSerialNumber.String
		}
		item.AssetVehicleBrandName = inspectionTyre.AssetInspectionVehicle.CustomBrandName.String
		item.AssetVehicleModelName = inspectionTyre.AssetInspectionVehicle.CustomModelName.String
		item.VehicleKM = inspectionTyre.AssetInspectionVehicle.VehicleKM
		item.VehicleHM = inspectionTyre.AssetInspectionVehicle.VehicleHm
		item.Odometer = inspectionTyre.AssetInspectionVehicle.VehicleKM
		if inspectionTyre.AssetInspectionVehicle.VehicleHm != 0 {
			item.Odometer = float64(inspectionTyre.AssetInspectionVehicle.VehicleHm)
		}

		item.CustomerName = inspectionTyre.AssetInspectionVehicle.PartnerOwnerName

		// if inspectionTyre.AssetInspectionVehicle.AssetVehicleID != "" {
		// 	item.CustomerName = inspectionTyre.AssetInspectionVehicle.Asset.PartnerOwnerName
		// 	item.AssetVehicleIdent = inspectionTyre.AssetInspectionVehicle.Asset.ReferenceNumber
		// 	item.AssetVehicleBrandName = inspectionTyre.AssetInspectionVehicle.Asset.Brand.BrandName
		// 	item.AssetVehicleModelName = inspectionTyre.AssetInspectionVehicle.Asset.AssetModel.AssetModelName
		// }

	}

	for i, attachmentURL := range mapAttachments[inspectionTyre.ID] {
		if i == 0 {
			item.Attachment1 = attachmentURL
		}

		if i == 1 {
			item.Attachment2 = attachmentURL
		}

		if i == 2 {
			item.Attachment3 = attachmentURL
		}

		if i > 2 {
			break
		}
	}

	item.Source = "Manual"

	if inspectionTyre.AssetInspectionVehicle != nil {
		for i, attachmentURL := range mapAttachments[inspectionTyre.AssetInspectionVehicle.ID] {
			if i == 0 {
				item.VehilceInspectionAttachment1 = attachmentURL
			}

			if i == 1 {
				item.VehilceInspectionAttachment2 = attachmentURL
			}

			if i == 2 {
				item.VehilceInspectionAttachment3 = attachmentURL
			}

			if i > 2 {
				break
			}
		}

		if inspectionTyre.AssetInspectionVehicle.DigispectVehicle != nil {
			if inspectionTyre.AssetInspectionVehicle.DigispectVehicle.Photo.String != "" {
				photo := inspectionTyre.AssetInspectionVehicle.DigispectVehicle.Photo.String
				var err error
				item.VehiclePhoto, err = uc.AttachmentUseCase.PhotoPublicLink(ctx, photo, client.ID, client.ClientAlias)
				if err != nil {
					return exceltmpl.ExportInspectionItem{}, err
				}
			}
		}
	}

	return item, nil
}

func (uc *AssetInspectionUseCase) ExportAssetInspectionTyresInspectionList(ctx context.Context, isTyreView bool, req dtos.ExportInspectionListReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
		Where: userModels.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
		Columns: []string{"client_alias"},
	})
	if err != nil {
		return nil, err
	}

	inspectionTyres, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyresV2(ctx, uc.DB.DB(), models.AssetInspectionTyreCondition{
		Where: models.AssetInspectionTyreWhere{
			ClientID:               claim.GetLoggedInClientID(),
			StartDate:              req.StartDate,
			EndDate:                req.EndDate,
			IsSingleTyreInspection: isTyreView,
		},
		Preload: models.AssetInspectionTyrePreload{
			AssetInspection:                        true,
			AssetTyre:                              true,
			AssetTyreAsset:                         true,
			AssetTyreAssetBrand:                    true,
			AssetTyreTyre:                          true,
			AssetInspectionVehicle:                 true,
			AssetInspectionVehicleAsset:            true,
			AssetInspectionVehicleAssetBrand:       true,
			AssetInspectionVehicleAssetModel:       true,
			AssetInspectionVehicleDigispectVehicle: true,
		},
	})
	if err != nil {
		return nil, err
	}

	userIDs := []string{}
	attachmentSourceIDs := []string{}
	for i := range inspectionTyres {
		userIDs = append(userIDs, inspectionTyres[i].AssetInspection.InspectByUserID)
		attachmentSourceIDs = append(attachmentSourceIDs, inspectionTyres[i].ID)
	}
	mapUserName := map[string]string{}
	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for _, user := range users {
			mapUserName[user.ID] = user.GetName()
		}
	}

	mapAttachments := map[string][]string{}
	if len(attachmentSourceIDs) > 0 {
		attachments, err := uc.AttachmentUseCase.GetAttachments(ctx, storageDtos.GetAttachmentsReq{
			SourceReferenceIDs: attachmentSourceIDs,
			ReferenceCode:      storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET_INSPECTION,
		})
		if err != nil {
			return nil, err
		}

		for _, attachment := range attachments {
			mapAttachments[attachment.SourceReferenceID] = append(mapAttachments[attachment.SourceReferenceID], attachment.Id)
		}

		for k, v := range mapAttachments {
			var err error
			mapAttachments[k], err = uc.AttachmentUseCase.AttachmentPublicLinks(ctx, v, claim.GetLoggedInClientID(), client.ClientAlias)
			if err != nil {
				return nil, err
			}
		}
	}

	export := exceltmpl.ExportInspection{
		DowndladedUsername: claim.GetName(),
		Items:              make([]exceltmpl.ExportInspectionItem, len(inspectionTyres)),
	}

	for i, inspectionTyre := range inspectionTyres {
		export.Items[i], err = uc.BuildExcelExportInspectionItem(
			ctx,
			inspectionTyre,
			mapUserName[inspectionTyre.AssetInspection.InspectByUserID],
			mapAttachments,
			client,
		)
		if err != nil {
			return nil, err
		}

	}

	sort.SliceStable(export.Items, func(i, j int) bool {
		if export.Items[i].InspectionNo == export.Items[j].InspectionNo {
			return export.Items[i].TyrePosition > export.Items[j].TyrePosition
		}

		return false
	})

	var file *excelize.File
	if isTyreView {
		file, err = export.ExportToExcelTyreViewInspectionList()
	} else {
		file, err = export.ExportToExcelInspectionViewInspectionList()
	}
	if err != nil {
		return nil, err
	}

	signedUrl, xlsxHeader, err := uc.generateAssetInspectionExportSignedUrl(ctx, file)
	if err != nil {
		return nil, err
	}

	defer file.Close()

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: signedUrl,
		Data:        xlsxHeader,
	}, nil
}
