package usecase

import (
	approvalModels "assetfindr/internal/app/approval/models"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	userModels "assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"context"
)

func (uc *AssetTyreUseCase) GetAssetTyreScrappedList(ctx context.Context, req dtos.GetAssetTyreScrappedDisposedListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	totalRecords, assetTyres, err := uc.AssetTyreRepository.GetAssetTyreScrappedList(
		ctx, uc.DB.DB(),
		models.GetAssetTyreScrappedDisposedListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetTyreCondition{
				Where: models.AssetTyreWhere{
					ClientID:   claim.GetLoggedInClientID(),
					BrandIDs:   req.BrandIDs,
					TyreSizes:  req.TyreSizes,
					IsWorkshop: req.IsWorkshop,
				},
				Preload: models.AssetTyrePreload{
					Tyre:                  true,
					Asset:                 true,
					AssetBrand:            true,
					StatusRequestScrapped: true,
				},
			},
			StatusRequestReasonCodes:    req.StatusRequestReasonCodes,
			StatusRequestSubReasonCodes: req.StatusRequestSubReasonCodes,
			StatusRequestGradeCodes:     req.StatusRequestGradeCodes,
		},
	)
	if err != nil {
		return nil, err
	}

	assetStatusReqIDs := []string{}
	for i := range assetTyres {
		if assetTyres[i].StatusRequestScrapped != nil {
			assetStatusReqIDs = append(assetStatusReqIDs, assetTyres[i].StatusRequestScrapped.ID)
		}
	}

	mapApprovalRequests := map[string]approvalModels.ApprovalRequest{}
	userIDs := []string{}
	if len(assetStatusReqIDs) > 0 {
		approvalRequets, err := uc.approvalRepo.GetApprovalRequests(ctx, uc.DB.DB(), approvalModels.ApprovalRequestCondition{
			Where: approvalModels.ApprovalRequestWhere{
				AnyReferenceIDs: assetStatusReqIDs,
			},
			Preload: approvalModels.ApprovalRequestPreload{
				Approvals: true,
			},
		})
		if err != nil {
			return nil, err
		}
		for i := range approvalRequets {
			for j := range approvalRequets[i].Approvals {
				userIDs = append(userIDs, approvalRequets[i].Approvals[j].ApprovalUserID)
			}
			for k := range approvalRequets[i].ReferenceIDs {
				mapApprovalRequests[approvalRequets[i].ReferenceIDs[k]] = approvalRequets[i]
			}
		}
	}

	mapUserName := map[string]string{}
	if len(userIDs) > 0 {
		users, err := uc.userIdentityRepo.GetUsersV2(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for i := range users {
			mapUserName[users[i].ID] = users[i].GetName()
		}
	}

	respData := make([]dtos.AssetTyreScrappedDisposedResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		tyreUtilRate := helpers.CalculateTyreUtilRate(assetTyre.StartThreadDepth, assetTyre.AverageRTD)
		item := dtos.AssetTyreScrappedDisposedResp{
			ID:                  assetTyre.AssetID,
			Name:                assetTyre.Asset.Name,
			SerialNumber:        assetTyre.Asset.SerialNumber,
			AssetStatusCode:     assetTyre.Asset.AssetStatusCode,
			BrandID:             assetTyre.Asset.BrandID,
			BrandName:           assetTyre.Asset.Brand.BrandName,
			DateCode:            assetTyre.DateCode.String,
			DOTCode:             assetTyre.DOTCode.String,
			TyreID:              assetTyre.TyreID,
			Tyre:                dtos.BuildTyreResp(assetTyre.Tyre),
			Rfid:                assetTyre.Asset.Rfid,
			DatetimeOfLastCheck: assetTyre.DatetimeOfLastCheck,
			AverageRTD:          assetTyre.AverageRTD,
			TotalKM:             assetTyre.TotalKM,
			TotalHm:             calculationhelpers.Div100(assetTyre.TotalHm),
			TUR:                 tyreUtilRate,
			ProjectedLifeKM:     helpers.CalculateTyreProjectedLife(assetTyre.TotalKM, tyreUtilRate),
			ProjectedLifeHm:     helpers.CalculateTyreProjectedLifeHM(calculationhelpers.Div100(assetTyre.TotalHm), tyreUtilRate),
			RetreadNumber:       assetTyre.RetreadNumber,
			StartThreadDepth:    assetTyre.StartThreadDepth,
			TotalLifetime:       assetTyre.TotalLifetime,
			PartnerOwnerID:      assetTyre.Asset.PartnerOwnerID,
			PartnerOwnerNo:      assetTyre.Asset.PartnerOwnerNo,
			PartnerOwnerName:    assetTyre.Asset.PartnerOwnerName,
		}

		if assetTyre.StatusRequestScrapped != nil {
			item.StatusRequestScrapped = dtos.BuildAssetStatusRequestResp(assetTyre.StatusRequestScrapped)
			approvalReq, ok := mapApprovalRequests[assetTyre.StatusRequestScrapped.ID]
			if ok {
				item.StatusRequestScrapped.ApprovalRequest = dtos.BuildApprovalRequestResp(approvalReq, mapUserName)
			}
		}
		respData = append(respData, item)
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.ListRequest.PageSize,
		PageNo:       req.ListRequest.PageNo,
		Data:         respData,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreDisposedList(ctx context.Context, req dtos.GetAssetTyreScrappedDisposedListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	totalRecords, assetTyres, err := uc.AssetTyreRepository.GetAssetTyreDisposedList(
		ctx, uc.DB.DB(),
		models.GetAssetTyreScrappedDisposedListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetTyreCondition{
				Where: models.AssetTyreWhere{
					ClientID:   claim.GetLoggedInClientID(),
					BrandIDs:   req.BrandIDs,
					TyreSizes:  req.TyreSizes,
					IsWorkshop: req.IsWorkshop,
				},
				Preload: models.AssetTyrePreload{
					Tyre:                  true,
					Asset:                 true,
					AssetBrand:            true,
					StatusRequestScrapped: true,
					StatusRequestDisposed: true,
				},
			},
			StatusRequestReasonCodes:    req.StatusRequestReasonCodes,
			StatusRequestSubReasonCodes: req.StatusRequestSubReasonCodes,
			StatusRequestGradeCodes:     req.StatusRequestGradeCodes,
		},
	)
	if err != nil {
		return nil, err
	}

	assetStatusReqIDs := []string{}
	for i := range assetTyres {
		if assetTyres[i].StatusRequestScrapped != nil {
			assetStatusReqIDs = append(assetStatusReqIDs, assetTyres[i].StatusRequestScrapped.ID)
		}

		if assetTyres[i].StatusRequestDisposed != nil {
			assetStatusReqIDs = append(assetStatusReqIDs, assetTyres[i].StatusRequestDisposed.ID)
		}
	}

	mapApprovalRequests := map[string]approvalModels.ApprovalRequest{}
	userIDs := []string{}
	if len(assetStatusReqIDs) > 0 {
		approvalRequets, err := uc.approvalRepo.GetApprovalRequests(ctx, uc.DB.DB(), approvalModels.ApprovalRequestCondition{
			Where: approvalModels.ApprovalRequestWhere{
				AnyReferenceIDs: assetStatusReqIDs,
			},
			Preload: approvalModels.ApprovalRequestPreload{
				Approvals: true,
			},
		})
		if err != nil {
			return nil, err
		}
		for i := range approvalRequets {
			for j := range approvalRequets[i].Approvals {
				userIDs = append(userIDs, approvalRequets[i].Approvals[j].ApprovalUserID)
			}

			for k := range approvalRequets[i].ReferenceIDs {
				mapApprovalRequests[approvalRequets[i].ReferenceIDs[k]] = approvalRequets[i]
			}
		}
	}

	mapUserName := map[string]string{}
	if len(userIDs) > 0 {
		users, err := uc.userIdentityRepo.GetUsersV2(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for i := range users {
			mapUserName[users[i].ID] = users[i].GetName()
		}
	}

	respData := make([]dtos.AssetTyreScrappedDisposedResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		tyreUtilRate := helpers.CalculateTyreUtilRate(assetTyre.StartThreadDepth, assetTyre.AverageRTD)
		item := dtos.AssetTyreScrappedDisposedResp{
			ID:                  assetTyre.AssetID,
			Name:                assetTyre.Asset.Name,
			SerialNumber:        assetTyre.Asset.SerialNumber,
			AssetStatusCode:     assetTyre.Asset.AssetStatusCode,
			BrandID:             assetTyre.Asset.BrandID,
			BrandName:           assetTyre.Asset.Brand.BrandName,
			DateCode:            assetTyre.DateCode.String,
			DOTCode:             assetTyre.DOTCode.String,
			TyreID:              assetTyre.TyreID,
			Tyre:                dtos.BuildTyreResp(assetTyre.Tyre),
			Rfid:                assetTyre.Asset.Rfid,
			DatetimeOfLastCheck: assetTyre.DatetimeOfLastCheck,
			AverageRTD:          assetTyre.AverageRTD,
			TotalKM:             assetTyre.TotalKM,
			TotalHm:             calculationhelpers.Div100(assetTyre.TotalHm),
			TUR:                 tyreUtilRate,
			ProjectedLifeKM:     helpers.CalculateTyreProjectedLife(assetTyre.TotalKM, tyreUtilRate),
			ProjectedLifeHm:     helpers.CalculateTyreProjectedLifeHM(calculationhelpers.Div100(assetTyre.TotalHm), tyreUtilRate),
			RetreadNumber:       assetTyre.RetreadNumber,
			StartThreadDepth:    assetTyre.StartThreadDepth,
			TotalLifetime:       assetTyre.TotalLifetime,
			PartnerOwnerID:      assetTyre.Asset.PartnerOwnerID,
			PartnerOwnerNo:      assetTyre.Asset.PartnerOwnerNo,
			PartnerOwnerName:    assetTyre.Asset.PartnerOwnerName,
		}

		if assetTyre.StatusRequestScrapped != nil {
			item.StatusRequestScrapped = dtos.BuildAssetStatusRequestResp(assetTyre.StatusRequestScrapped)
			approvalReq, ok := mapApprovalRequests[assetTyre.StatusRequestScrapped.ID]
			if ok {
				item.StatusRequestScrapped.ApprovalRequest = dtos.BuildApprovalRequestResp(approvalReq, mapUserName)
			}
		}

		if assetTyre.StatusRequestDisposed != nil {
			item.StatusRequestDisposed = dtos.BuildAssetStatusRequestResp(assetTyre.StatusRequestDisposed)
			approvalReq, ok := mapApprovalRequests[assetTyre.StatusRequestDisposed.ID]
			if ok {
				item.StatusRequestDisposed.ApprovalRequest = dtos.BuildApprovalRequestResp(approvalReq, mapUserName)
			}
		}
		respData = append(respData, item)
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.ListRequest.PageSize,
		PageNo:       req.ListRequest.PageNo,
		Data:         respData,
	}, nil
}
