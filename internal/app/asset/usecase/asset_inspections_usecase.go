package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/app/asset/utils"
	contentDtos "assetfindr/internal/app/content/dtos"
	contentModel "assetfindr/internal/app/content/models"
	contentRepository "assetfindr/internal/app/content/repository"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	storageRepository "assetfindr/internal/app/storage/repository"
	ticketConstant "assetfindr/internal/app/task/constants"
	taskModels "assetfindr/internal/app/task/models"
	taskRepository "assetfindr/internal/app/task/repository"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	userModels "assetfindr/internal/app/user-identity/models"
	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
	internalConstants "assetfindr/internal/constants"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/pgtypehelpers"
	"assetfindr/pkg/common/helpers/timehelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"bytes"
	"encoding/json"
	"fmt"
	"html/template"
	"io/ioutil"
	"math"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	storageUsecase "assetfindr/internal/app/storage/usecase"

	"context"

	"github.com/SebastiaanKlippert/go-wkhtmltopdf"
	"github.com/jackc/pgtype"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"

	_ "image/jpeg"
	_ "image/png"
)

type AssetInspectionUseCase struct {
	DB                               database.DBUsecase
	AssetInspectionRepository        repository.AssetInspectionRepository
	AssetInspectionVehicleRepository repository.AssetInspectionVehicleRepository
	AssetInspectionTyreRepository    repository.AssetInspectionTyreRepository
	AssetRepository                  repository.AssetRepository
	UserRepository                   userIdentityRepository.UserRepository
	AttachmentUseCase                *storageUsecase.AttachmentUseCase
	AssetVehicleRepository           repository.AssetVehicleRepository
	AssetTyreRepository              repository.AssetTyreRepository
	assetVehicleUsecase              *AssetVehicleUseCase
	StorageRepository                storageRepository.StorageRepository
	ticketRepo                       taskRepository.TicketRepository
	AssetInspectionUtil              utils.AssetInspectionUtil
	notifUseCase                     notificationUsecase.NotificationUseCase
	assetAssignmentRepo              repository.AssetAssignmentRepository
	AssetLinkedUseCase               AssetLinkedUseCase
	formRepo                         contentRepository.FormRepository
	digispectRepo                    repository.DigispectRepository
}

func NewAssetInspectionUseCase(
	DB database.DBUsecase,
	assetInspectionRepository repository.AssetInspectionRepository,
	assetRepository repository.AssetRepository,
	assetInspectionVehicleRepository repository.AssetInspectionVehicleRepository,
	assetInspectionTyreRepository repository.AssetInspectionTyreRepository,
	userRepository userIdentityRepository.UserRepository,
	attachmentUseCase *storageUsecase.AttachmentUseCase,
	assetVehicleRepository repository.AssetVehicleRepository,
	assetTyreRepository repository.AssetTyreRepository,
	assetVehicleUsecase *AssetVehicleUseCase,
	storageRepository storageRepository.StorageRepository,
	ticketRepo taskRepository.TicketRepository,
	assetInspectionUtil utils.AssetInspectionUtil,
	NotificationUsecase notificationUsecase.NotificationUseCase,
	assetAssignmentRepo repository.AssetAssignmentRepository,
	AssetLinkedUseCase AssetLinkedUseCase,
	formRepo contentRepository.FormRepository,
	digispectRepo repository.DigispectRepository,
) *AssetInspectionUseCase {
	return &AssetInspectionUseCase{
		DB:                               DB,
		AssetInspectionRepository:        assetInspectionRepository,
		AssetInspectionVehicleRepository: assetInspectionVehicleRepository,
		AssetInspectionTyreRepository:    assetInspectionTyreRepository,
		AssetRepository:                  assetRepository,
		UserRepository:                   userRepository,
		AttachmentUseCase:                attachmentUseCase,
		AssetVehicleRepository:           assetVehicleRepository,
		AssetTyreRepository:              assetTyreRepository,
		assetVehicleUsecase:              assetVehicleUsecase,
		StorageRepository:                storageRepository,
		ticketRepo:                       ticketRepo,
		AssetInspectionUtil:              assetInspectionUtil,
		notifUseCase:                     NotificationUsecase,
		assetAssignmentRepo:              assetAssignmentRepo,
		AssetLinkedUseCase:               AssetLinkedUseCase,
		formRepo:                         formRepo,
		digispectRepo:                    digispectRepo,
	}
}

func (uc *AssetInspectionUseCase) generateHTMLTemplateImagePath(baseDir string, imageSrc string) (string, error) {
	imagePath, err := filepath.Abs(filepath.Join(baseDir, imageSrc))
	if err != nil {
		commonlogger.Errorf("Error resolving image path", err.Error())
		return "", err
	}

	return imagePath, nil
}

func (uc *AssetInspectionUseCase) validateInput(referenceId string, referenceCode string) error {
	if (referenceCode == "" && referenceId != "") || (referenceCode != "" && referenceId == "") {
		return errorhandler.ErrBadRequest("reference code and id must be empty or both filled")
	}
	return nil
}

func (uc *AssetInspectionUseCase) validateFilter(req dtos.GetInspectionListReq) error {
	err := uc.validateInput(req.ReferenceID, req.ReferenceCode)
	if err != nil {
		return err
	}
	if (req.AssetID != "" || req.InspectionID != "") && (req.ReferenceCode != "" || req.ReferenceID != "") {
		return errorhandler.ErrBadRequest("filter with reference code and reference id can not with asset id or inspection id")
	}
	if len(req.InspectionIDs) > 12 {
		return errorhandler.ErrBadRequest("inspection ids filter limit is set to 12 inspection list")
	}
	return nil
}

func (uc *AssetInspectionUseCase) CreateAssetInspectionVehicleTyre(
	ctx context.Context, req dtos.CreateAssetInspectionRequest,
) (*commonmodel.CreateResponse, error) {
	err := uc.validateInput(req.ReferenceID, req.ReferenceCode)
	if err != nil {
		return nil, err
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	assetInspection := models.AssetInspection{
		InspectByUserID:      req.InspectByUserID,
		ReferenceID:          req.ReferenceID,
		ReferenceCode:        req.ReferenceCode,
		LocationLat:          req.LocationLat,
		LocationLong:         req.LocationLong,
		LocationLabel:        req.LocationLabel,
		DisplayFormationCode: req.DisplayFormationCode,
		ClientID:             claim.GetLoggedInClientID(),
	}
	assetInspection.SetID()

	err = uc.AssetInspectionRepository.CreateAssetInspection(ctx, tx.DB(), &assetInspection)
	if err != nil {
		return nil, err
	}

	resp := &dtos.AssetInspectionVehicleTyreResp{
		ID:              assetInspection.ID,
		InspectByUserID: assetInspection.InspectByUserID,
		ReferenceID:     assetInspection.ReferenceID,
		ReferenceCode:   assetInspection.ReferenceCode,
	}

	err = uc.addInspectionChildren(ctx, assetInspection.ID, req, tx)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: resp.ID,
		Data:        resp,
	}, nil
}

func (uc *AssetInspectionUseCase) addInspectionChildren(
	ctx context.Context, inspectionID string, req dtos.CreateAssetInspectionRequest, tx database.DBUsecase,
) error {
	now := time.Now()

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	clientID := claim.GetLoggedInClientID()

	isHasTyreInspection := false

	for _, createDTO := range req.Inspections {
		if createDTO.AssetID == "" && createDTO.AssetCategoryCode == constants.ASSET_CATEGORY_VEHICLE_CODE {
			numOfTyres := null.Int{}
			numOfSpareTyres := null.Int{}
			if createDTO.AxleConfiguration.Status == pgtype.Present {
				numOfTyre, numOfSpareTyre := utils.CountAxleConfigurationTyres(models.AxleConfigurationNewToOld(createDTO.AxleConfiguration))
				numOfTyres = null.IntFrom(int64(numOfTyre))
				numOfSpareTyres = null.IntFrom(int64(numOfSpareTyre))
			}

			vehicleInspection := models.AssetInspectionVehicle{
				AssetInspectionID:      inspectionID,
				AssetVehicleID:         "",
				Remark:                 createDTO.Remark,
				AssetAssignmentID:      "",
				VehicleKM:              createDTO.VehicleKM,
				VehicleHm:              calculationhelpers.Multiply100(createDTO.VehicleHm),
				ClientID:               clientID,
				RequireSpooringVehicle: createDTO.RequireSpooringVehicle,
				FailedVisualChecking:   createDTO.FailedVisualChecking,
				RequireRotationTyre:    createDTO.RequireRotationTyre,
				TireTreadAndRimDamage:  createDTO.TireTreadAndRimDamage,
				AxleConfiguration:      createDTO.AxleConfiguration,
				MaxRtdDiffTolerance:    &createDTO.MaxRtdDiffTolerance,
				CustomBrandName:        createDTO.CustomBrandName,
				CustomModelName:        createDTO.CustomModelName,
				CustomReferenceNumber:  createDTO.CustomReferenceNumber,
				DigispectConfigID:      createDTO.DigispectConfigID,
				DeviceID:               createDTO.DeviceID,
				SourceTypeCode:         createDTO.SourceTypeCode,
				DigispectVehicleID:     createDTO.DigispectVehicleID,
				PartnerOwnerName:       createDTO.PartnerOwnerName,
				NumberOfTyres:          numOfTyres,
				NumberOfSpareTyres:     numOfSpareTyres,
			}
			vehicleInspection.SetID()

			err = uc.AssetInspectionVehicleRepository.CreateAssetInspectionVehicle(ctx, tx.DB(), &vehicleInspection)
			if err != nil {
				return err
			}

			_, err = uc.AttachmentUseCase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
				ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET_INSPECTION,
				SourceReferenceID: vehicleInspection.ID,
				TargetReferenceID: "",
				ClientID:          clientID,
				Photos:            createDTO.Photos,
			})
			if err != nil {
				return err
			}
			break
		}

		// TODO: Uncomment this line
		// if createDTO.AssetID == "" && createDTO.AssetCategoryCode == constants.ASSET_CATEGORY_TYRE_CODE {
		if createDTO.AssetID == "" {
			tyreInspection := models.AssetInspectionTyre{
				AssetInspectionID:       inspectionID,
				AssetTyreID:             "",
				Remark:                  createDTO.Remark,
				AssetAssignmentID:       "",
				RDT1:                    createDTO.RDT1,
				RDT2:                    createDTO.RDT2,
				RDT3:                    createDTO.RDT3,
				RDT4:                    createDTO.RDT4,
				Temperature:             createDTO.Temperature,
				TyrePosition:            createDTO.TyrePosition,
				AverageRTD:              createDTO.AverageRTD,
				Pressure:                createDTO.Pressure,
				ClientID:                claim.GetLoggedInClientID(),
				PressureStatusCode:      createDTO.PressureStatusCode,
				TyreKM:                  0,
				TyreHm:                  0,
				FailedVisualChecking:    createDTO.FailedVisualChecking,
				RequireRotationTyre:     createDTO.RequireRotationTyre,
				RequireSpooringVehicle:  createDTO.RequireSpooringVehicle,
				TireTreadAndRimDamage:   createDTO.TireTreadAndRimDamage,
				CustomSerialNumber:      createDTO.CustomSerialNumber,
				DeviceID:                createDTO.DeviceID,
				CustomBrandName:         createDTO.CustomBrandName,
				CustomTyreSize:          createDTO.CustomTyreSize,
				NumberOfInspectionPoint: createDTO.NumberOfInspectionPoint,
				PressureSensorRef:       createDTO.PressureSensorRef,
				TemperatureSensorRef:    createDTO.TemperatureSensorRef,
				CustomRFID:              createDTO.CustomRFID,
				DigispectConfigID:       createDTO.DigispectConfigID,
				SourceTypeCode:          createDTO.SourceTypeCode,
			}
			tyreInspection.SetID()
			err = uc.AssetInspectionTyreRepository.CreateAssetInspectionTyre(ctx, tx.DB(), &tyreInspection)
			if err != nil {
				return err
			}

			_, err = uc.AttachmentUseCase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
				ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET_INSPECTION,
				SourceReferenceID: tyreInspection.ID,
				TargetReferenceID: "",
				ClientID:          clientID,
				Photos:            createDTO.Photos,
			})
			if err != nil {
				return err
			}

			continue
		}

		if createDTO.AssetInspectionVehicleID != "" {
			err = uc.AssetInspectionVehicleRepository.UpdateAssetInspectionVehicle(
				ctx,
				tx.DB(),
				createDTO.AssetInspectionVehicleID,
				&models.AssetInspectionVehicle{
					RequireSpooringVehicle: createDTO.RequireSpooringVehicle,
					FailedVisualChecking:   createDTO.FailedVisualChecking,
					RequireRotationTyre:    createDTO.RequireRotationTyre,
					TireTreadAndRimDamage:  createDTO.TireTreadAndRimDamage,
					DeviceID:               createDTO.DeviceID,
					SourceTypeCode:         createDTO.SourceTypeCode,
				},
			)
			if err != nil {
				return err
			}

			continue
		}

		asset, err := uc.AssetRepository.GetAsset(ctx, tx.DB(), models.AssetCondition{
			Where: models.AssetWhere{
				ID: createDTO.AssetID,
			},
			Preload: models.AssetPreload{
				Brand:      true,
				AssetModel: true,
			},
		})

		if err != nil {
			return err
		}

		if asset.AssetCategoryCode == constants.ASSET_CATEGORY_VEHICLE_CODE {
			assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicleByID(ctx, tx.DB(), createDTO.AssetID)
			if err != nil {
				return err
			}

			if createDTO.VehicleKM > 0 || createDTO.VehicleHm > 0 {
				err = uc.assetVehicleUsecase.updateAssetVehicleMeter(ctx, tx, assetVehicle, dtos.UpdateVehicleMeterReq{
					VehicleKM: int(createDTO.VehicleKM),
					VehicleHm: createDTO.VehicleHm,
				})
				if err != nil {
					return err
				}
			}

			err = uc.AssetVehicleRepository.UpdateAssetVehicle(ctx, tx.DB(), &models.AssetVehicle{
				AssetID:         createDTO.AssetID,
				LastInspectedAt: null.TimeFrom(now),
			})
			if err != nil {
				return err
			}

			numOfTyres := null.Int{}
			numOfSpareTyres := null.Int{}
			if assetVehicle.AxleConfiguration.Status == pgtype.Present {
				numOfTyre, numOfSpareTyre := utils.CountAxleConfigurationTyres(models.AxleConfigurationNewToOld(createDTO.AxleConfiguration))
				numOfTyres = null.IntFrom(int64(numOfTyre))
				numOfSpareTyres = null.IntFrom(int64(numOfSpareTyre))
			}

			vehicleInspection := models.AssetInspectionVehicle{
				AssetInspectionID:      inspectionID,
				AssetVehicleID:         createDTO.AssetID,
				Remark:                 createDTO.Remark,
				AssetAssignmentID:      createDTO.AssetAssignmentID,
				VehicleKM:              createDTO.VehicleKM,
				VehicleHm:              calculationhelpers.Multiply100(createDTO.VehicleHm),
				ClientID:               clientID,
				RequireSpooringVehicle: createDTO.RequireSpooringVehicle,
				FailedVisualChecking:   createDTO.FailedVisualChecking,
				RequireRotationTyre:    createDTO.RequireRotationTyre,
				TireTreadAndRimDamage:  createDTO.TireTreadAndRimDamage,
				AxleConfiguration:      assetVehicle.AxleConfiguration,
				MaxRtdDiffTolerance:    assetVehicle.MaxRtdDiffTolerance,
				DigispectConfigID:      createDTO.DigispectConfigID,
				DeviceID:               createDTO.DeviceID,
				SourceTypeCode:         createDTO.SourceTypeCode,
				CustomBrandName:        null.NewString(asset.Brand.BrandName, asset.Brand.BrandName != ""),
				CustomModelName:        null.NewString(asset.AssetModel.AssetModelName, asset.AssetModel.AssetModelName != ""),
				CustomReferenceNumber:  null.NewString(asset.ReferenceNumber, asset.ReferenceNumber != ""),
				CustomSerialNumber:     null.NewString(asset.SerialNumber, asset.SerialNumber != ""),
				PartnerOwnerName:       asset.PartnerOwnerName,
				NumberOfTyres:          numOfTyres,
				NumberOfSpareTyres:     numOfSpareTyres,
			}

			if asset.PartnerOwnerID == "" {
				vehicleInspection.PartnerOwnerName = ""
			}
			vehicleInspection.SetID()

			err = uc.AssetInspectionVehicleRepository.CreateAssetInspectionVehicle(ctx, tx.DB(), &vehicleInspection)
			if err != nil {
				return err
			}

			_, err = uc.AttachmentUseCase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
				ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET_INSPECTION,
				SourceReferenceID: vehicleInspection.ID,
				TargetReferenceID: "",
				ClientID:          clientID,
				Photos:            createDTO.Photos,
			})
			if err != nil {
				return err
			}

			break
		} else if asset.AssetCategoryCode == constants.ASSET_CATEGORY_TYRE_CODE {
			assetTyre, err := uc.AssetTyreRepository.GetAssetTyre(ctx, tx.DB(), models.AssetTyreCondition{
				Where: models.AssetTyreWhere{
					AssetID: createDTO.AssetID,
				},
				Preload: models.AssetTyrePreload{
					Tyre: true,
				},
			})
			if err != nil {
				return err
			}

			updateTyre := &models.AssetTyre{
				AssetID:             createDTO.AssetID,
				DatetimeOfLastCheck: now,
			}

			if createDTO.AverageRTD > 0 {
				if assetTyre.PrevKmHmDataUnavailable.Bool && assetTyre.HasNotSetRTD.Bool {
					updateTyre.StartThreadDepth = createDTO.AverageRTD
				}
				updateTyre.AverageRTD = createDTO.AverageRTD
				updateTyre.Rtd1 = createDTO.RDT1
				updateTyre.Rtd2 = createDTO.RDT2
				updateTyre.Rtd3 = createDTO.RDT3
				updateTyre.Rtd4 = createDTO.RDT4
				updateTyre.AverageRtdLastUpdatedAt = null.TimeFrom(now)
				updateTyre.HasNotSetRTD = null.BoolFrom(false)
			}

			if createDTO.Pressure > 0 {
				updateTyre.Pressure = null.FloatFrom(createDTO.Pressure)
				updateTyre.PressureLastUpdatedAt = null.TimeFrom(now)
				updateTyre.PressureLastUpdatedSensorRef = createDTO.PressureSensorRef
			}

			if createDTO.Temperature.Valid {
				updateTyre.Temperature = createDTO.Temperature
				updateTyre.TemperatureLastUpdatedAt = null.TimeFrom(now)
				updateTyre.TemperatureLastUpdatedSensorRef = createDTO.TemperatureSensorRef
			}

			err = uc.AssetTyreRepository.UpdateAssetTyre(ctx, tx.DB(), updateTyre)
			if err != nil {
				return err
			}

			tyreInspection := models.AssetInspectionTyre{
				AssetInspectionID:       inspectionID,
				AssetTyreID:             createDTO.AssetID,
				Remark:                  createDTO.Remark,
				AssetAssignmentID:       createDTO.AssetAssignmentID,
				RDT1:                    createDTO.RDT1,
				RDT2:                    createDTO.RDT2,
				RDT3:                    createDTO.RDT3,
				RDT4:                    createDTO.RDT4,
				Temperature:             createDTO.Temperature,
				TyrePosition:            createDTO.TyrePosition,
				AverageRTD:              createDTO.AverageRTD,
				Pressure:                createDTO.Pressure,
				ClientID:                claim.GetLoggedInClientID(),
				PressureStatusCode:      createDTO.PressureStatusCode,
				TyreKM:                  assetTyre.TotalKM,
				TyreHm:                  assetTyre.TotalHm,
				FailedVisualChecking:    createDTO.FailedVisualChecking,
				RequireRotationTyre:     createDTO.RequireRotationTyre,
				RequireSpooringVehicle:  createDTO.RequireSpooringVehicle,
				CustomSerialNumber:      asset.SerialNumber,
				DeviceID:                createDTO.DeviceID,
				SourceTypeCode:          createDTO.SourceTypeCode,
				TireTreadAndRimDamage:   createDTO.TireTreadAndRimDamage,
				CustomBrandName:         null.NewString(asset.Brand.BrandName, asset.Brand.BrandName != ""),
				NumberOfInspectionPoint: createDTO.NumberOfInspectionPoint,
				PressureSensorRef:       createDTO.PressureSensorRef,
				TemperatureSensorRef:    createDTO.TemperatureSensorRef,
				CustomTyreSize:          assetTyre.Tyre.GetTyreSize(),
				CustomRFID:              asset.Rfid,
				DigispectConfigID:       createDTO.DigispectConfigID,
				StartThreadDepth:        assetTyre.StartThreadDepth,
			}
			tyreInspection.SetID()
			err = uc.AssetInspectionTyreRepository.CreateAssetInspectionTyre(ctx, tx.DB(), &tyreInspection)
			if err != nil {
				return err
			}

			isHasTyreInspection = true

			_, err = uc.AttachmentUseCase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
				ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET_INSPECTION,
				SourceReferenceID: tyreInspection.ID,
				TargetReferenceID: asset.ID,
				ClientID:          clientID,
				Photos:            createDTO.Photos,
			})
			if err != nil {
				return err
			}
		}
	}

	if isHasTyreInspection {
		go uc.notifyAfterAddInspectionChildern(ctx, inspectionID)
	}
	return nil
}

func (uc *AssetInspectionUseCase) CreateAssetInspectionChildTyres(
	ctx context.Context, inspectionID string, req dtos.CreateAssetInspectionRequest,
) (*commonmodel.CreateResponse, error) {
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	if req.LocationLabel != "" {
		assetInspection := models.AssetInspection{
			LocationLat:   req.LocationLat,
			LocationLong:  req.LocationLong,
			LocationLabel: req.LocationLabel,
		}

		err = uc.AssetInspectionRepository.UpdateAssetInspection(ctx, tx.DB(), inspectionID, &assetInspection)
		if err != nil {
			return nil, err
		}
	}

	err = uc.addInspectionChildren(ctx, inspectionID, req, tx)
	if err != nil {
		return nil, err
	}

	err = uc.calculateInspectionTyrePressureStatusAndRTDMismacth(ctx, inspectionID, tx)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: inspectionID,
		Data:        nil,
	}, nil
}

func (uc *AssetInspectionUseCase) getMaxStartDateInspectionDigispect(ctx context.Context, claim authhelpers.JwtTokenClaims, client *userIdentityModel.Client, isViewAll bool) (time.Time, error) {
	if client.HasPackage(internalConstants.CLIENT_PACKAGE_DIGISPECT_PRO_PLUS) {
		return timehelpers.TruncateDayLocal(time.Now().Add(-365 * 24 * time.Hour)), nil
	} else if client.HasPackage(internalConstants.CLIENT_PACKAGE_DIGISPECT_PRO) {
		return timehelpers.TruncateDayLocal(time.Now().Add(-90 * 24 * time.Hour)), nil
	}

	inspectByUserID := ""
	if !isViewAll {
		inspectByUserID = claim.UserID
	}

	// Adjust Permission Inspection
	inspections, err := uc.AssetInspectionRepository.GetAssetInspections(ctx, uc.DB.DB(), models.AssetInspectionCondition{
		Where: models.AssetInspectionWhere{
			Limit:               12,
			DigiSpectOnlySource: true,
			InspectByUserID:     inspectByUserID,
			ClientID:            claim.GetLoggedInClientID(),
		},
		Columns: []string{"ams_asset_inspections.created_at"},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return time.Time{}, err
	}

	if len(inspections) == 0 {
		return time.Time{}, nil
	}

	return inspections[len(inspections)-1].CreatedAt, nil
}

func (uc *AssetInspectionUseCase) isDigiSpectInspectionHasMoreData(ctx context.Context, claim authhelpers.JwtTokenClaims, maxStartDate time.Time, isViewAll bool) (null.Bool, error) {
	// Adjust Permission Inspection

	inspectByUserID := ""
	if !isViewAll {
		inspectByUserID = claim.UserID
	}

	_, err := uc.AssetInspectionRepository.GetAssetInspection(ctx, uc.DB.DB(), models.AssetInspectionCondition{
		Where: models.AssetInspectionWhere{
			EndDate1:            maxStartDate,
			DigiSpectOnlySource: true,
			InspectByUserID:     inspectByUserID,
			ClientID:            claim.GetLoggedInClientID(),
		},
		Columns: []string{"ams_asset_inspections.id"},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return null.Bool{}, err
	}

	return null.BoolFrom(err == nil), nil
}

func (uc *AssetInspectionUseCase) GetAssetInspectionList(ctx context.Context, req dtos.GetAssetInspectionListReq, isFromDigiSpect bool) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	isViewAll := claim.IsHasPermission(
		constants.INSPECTION_PERMISSION_CATEGORY_TYREOPTIMAX,
		constants.INSPECTION_PERMISSION_VIEW_ALL_DIGISPECT_INSPECTIONS,
	)

	if req.IgnoreViewPermission {
		isViewAll = true
	}

	maxStartDate := time.Time{}
	inspectByUserID := ""
	if isFromDigiSpect {
		if !isViewAll {
			inspectByUserID = claim.UserID
		}

		client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
			Where: userModels.ClientWhere{
				ID: claim.GetLoggedInClientID(),
			},
			Columns: []string{"client_packages"},
		})
		if err != nil {
			return nil, err
		}

		maxStartDate, err = uc.getMaxStartDateInspectionDigispect(ctx, claim, client, isViewAll)
		if err != nil {
			return nil, err
		}

		if maxStartDate.IsZero() {
			return &commonmodel.ListResponse{
				TotalRecords: 0,
				PageSize:     req.PageSize,
				PageNo:       req.PageNo,
				Data:         nil,
			}, nil
		}
	}

	includeInspectionDetail := false
	if req.IncludeInspectionDetail != nil && *req.IncludeInspectionDetail {
		includeInspectionDetail = true
	}

	total, inspections, err := uc.AssetInspectionRepository.GetAssetInspectionList(ctx, uc.DB.DB(), models.GetAssetInspectionListParam{
		ListRequest: req.ListRequest,
		Cond: models.AssetInspectionCondition{
			Where: models.AssetInspectionWhere{
				UserID:                   claim.UserID,
				ClientID:                 claim.GetLoggedInClientID(),
				StartDate:                req.StartDate,
				StartDate1:               maxStartDate,
				EndDate:                  req.EndDate,
				ReferenceID:              req.ReferenceID,
				InspectByUserID:          inspectByUserID,
				ReferenceCode:            req.ReferenceCode,
				ReferenceCodeExclude:     req.ReferenceCodeExclude,
				HasTyreOptimaxInspection: req.HasTyreOptimaxInspection,
				IsAssignToUserLogin:      req.IsAssignToUserLogin,
				HasVehicleInspection:     req.HasVehicleInspection,
				HasTyreInspection:        req.HasTyreInspection,
				TyreSourceCodes:          req.TyreSourceCodes,
				InspectByUserIDs:         req.InspectByUserIDs,
				DigiSpectOnlySource:      isFromDigiSpect,
			},
			Preload: models.AssetInspectionPreload{
				Reference:                    true,
				CompleteInspectionItemDetail: includeInspectionDetail,
				CountInspectionTyres:         includeInspectionDetail,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	ticketIDs := make([]string, 0, len(inspections))
	if includeInspectionDetail {
		for i := range inspections {
			ticketIDs = append(ticketIDs, inspections[i].ReferenceID)
		}
	}

	userIDs := []string{}
	for i := range inspections {
		userIDs = append(userIDs, inspections[i].InspectByUserID)
	}
	mapUserName := map[string]string{}
	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for _, user := range users {
			mapUserName[user.ID] = user.GetName()
		}
	}

	mapTickets := map[string]taskModels.Ticket{}
	if len(ticketIDs) > 0 {
		tickets, err := uc.ticketRepo.GetTicketsV2(ctx, uc.DB.DB(), taskModels.TicketCondition{
			Where: taskModels.TicketWhere{
				IDs: ticketIDs,
			},
			Columns: []string{},
			Preload: taskModels.TicketPreload{},
		})

		if err != nil {
			return nil, err
		}

		for i := range tickets {
			mapTickets[tickets[i].ID] = tickets[i]
		}
	}

	respItems := make([]dtos.AssetInspectionResp, 0, len(inspections))
	for i := range inspections {
		respItems = append(respItems, dtos.BuildAssetInspectionResp(inspections[i], mapUserName, mapTickets))
	}

	resp := &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respItems,
	}

	if isFromDigiSpect &&
		!claim.IsDigiSpectProPlus() &&
		(len(inspections) < resp.PageSize || resp.PageSize*resp.PageNo >= total) {
		var err error
		resp.IsHasMoreData, err = uc.isDigiSpectInspectionHasMoreData(ctx, claim, maxStartDate, isViewAll)
		if err != nil {
			return nil, err
		}
	}

	return resp, nil
}

func (uc *AssetInspectionUseCase) GetAssetInspectionTicketList(ctx context.Context, req dtos.GetAssetInspectionListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	total, inspections, err := uc.AssetInspectionRepository.GetAssetInspectionList(ctx, uc.DB.DB(), models.GetAssetInspectionListParam{
		ListRequest: req.ListRequest,
		Cond: models.AssetInspectionCondition{
			Where: models.AssetInspectionWhere{
				UserID:               claim.UserID,
				ClientID:             claim.GetLoggedInClientID(),
				StartDate:            req.StartDate,
				EndDate:              req.EndDate,
				ReferenceID:          req.ReferenceID,
				ReferenceCode:        req.ReferenceCode,
				StatusCodes:          req.StatusCodes,
				IsAssignToUserLogin:  req.IsAssignToUserLogin,
				HasVehicleInspection: req.HasVehicleInspection,
				HasTyreInspection:    req.HasTyreInspection,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	ticketIDs := make([]string, 0, len(inspections))
	for i := range inspections {
		ticketIDs = append(ticketIDs, inspections[i].ReferenceID)
	}

	assetIDs := make([]string, 0)
	mapTickets := map[string]taskModels.Ticket{}
	if len(ticketIDs) > 0 {
		tickets, err := uc.ticketRepo.GetTicketsV2(ctx, uc.DB.DB(), taskModels.TicketCondition{
			Where: taskModels.TicketWhere{
				IDs: ticketIDs,
			},
			Columns: []string{},
			Preload: taskModels.TicketPreload{},
		})
		if err != nil {
			return nil, err
		}

		for i := range tickets {
			mapTickets[tickets[i].ID] = tickets[i]
			assetIDs = append(assetIDs, tickets[i].ReferenceID)
		}
	}

	mapAssets := map[string]models.Asset{}
	if len(assetIDs) > 0 {
		assets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), models.AssetCondition{
			Where: models.AssetWhere{
				IDs: assetIDs,
			},
			Columns: []string{},
			Preload: models.AssetPreload{Brand: true},
		})
		if err != nil {
			return nil, err
		}

		for i := range assets {
			mapAssets[assets[i].ID] = assets[i]
		}
	}

	resp := []dtos.AssetInspectionTicketResp{}
	for i := range inspections {
		ticket := mapTickets[inspections[i].ReferenceID]
		asset := mapAssets[ticket.ReferenceID]
		if asset.PartnerOwnerName == "" {
			asset.PartnerOwnerName = asset.Name
		}
		resp = append(resp, dtos.BuildAssetInspectionTicketResp(inspections[i], ticket, asset))
	}

	return &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         resp,
	}, nil
}

func (uc *AssetInspectionUseCase) GetAssetInspectionVehicles(ctx context.Context, req dtos.GetInspectionListReq) (commonmodel.ListResponse, error) {
	var assetInspectionVehicleResponses commonmodel.ListResponse
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return assetInspectionVehicleResponses, err
	}
	err = uc.validateFilter(req)
	if err != nil {
		assetInspectionVehicleResponses = commonmodel.ListResponse{
			TotalRecords: 0,
			PageSize:     1,
			PageNo:       1,
			Data:         err,
		}
		return assetInspectionVehicleResponses, nil
	}

	totalRecords, vehiclesInspected, err := uc.AssetInspectionVehicleRepository.GetAssetInspectionVehicleList(
		ctx,
		uc.DB.DB(),
		models.GetAssetInspectionVehicleListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetInspectionVehicleCondition{
				Where: models.AssetInspectionVehicleWhere{
					ClientID:          claim.GetLoggedInClientID(),
					InspectionID:      req.InspectionID,
					AssetID:           req.AssetID,
					ReferenceID:       req.ReferenceID,
					ReferenceCode:     req.ReferenceCode,
					StartDate:         req.StartDate,
					EndDate:           req.EndDate,
					AssetIDs:          req.AssetIDs,
					InspectByUserIDs:  req.InspectByUserIDs,
					HasPartnerOwnerID: req.HasPartnerOwnerID,
				},
				Preload: models.AssetInspectionVehiclePreload{
					AssetInspection:     true,
					AssetVehicle:        true,
					AssetVehicleAsset:   true,
					AssetVehicleVehicle: true,
				},
			},
		},
	)
	if err != nil {
		return assetInspectionVehicleResponses, err
	}

	if len(vehiclesInspected) == 0 {
		return assetInspectionVehicleResponses, nil
	}

	var userIds []string
	var digispectVehicleIDs []string
	ticketIDs := make([]string, 0, len(vehiclesInspected))
	flowIDs := make([]string, 0)
	for i := range vehiclesInspected {
		if vehiclesInspected[i].DigispectVehicleID != "" {
			digispectVehicleIDs = append(digispectVehicleIDs, vehiclesInspected[i].DigispectVehicleID)
		}
		userIds = append(userIds, vehiclesInspected[i].AssetInspection.InspectByUserID)
		if vehiclesInspected[i].AssetInspection.ReferenceCode == constants.ASSET_INSPECTION_REFERENCE_CODE_WORK_ORDER {
			ticketIDs = append(ticketIDs, vehiclesInspected[i].AssetInspection.ReferenceID)
		} else if vehiclesInspected[i].AssetInspection.ReferenceCode == constants.ASSET_INSPECTION_REFERENCE_CODE_FLOW {
			flowIDs = append(flowIDs, vehiclesInspected[i].AssetInspection.ReferenceID)
		}
	}

	mapTickets := map[string]taskModels.Ticket{}
	if len(flowIDs) > 0 {
		flows, err := uc.formRepo.GetFlows(ctx, uc.DB.DB(), contentModel.FlowCondition{
			Where: contentModel.FlowWhere{
				IDs: flowIDs,
			},
		})
		if err != nil {
			return assetInspectionVehicleResponses, err
		}

		mapTicketIDToFlowID := map[string]string{}
		flowTicketIDs := []string{}
		for i := range flows {
			flowTicketIDs = append(flowTicketIDs, flows[i].ReferenceID)
			mapTicketIDToFlowID[flows[i].ReferenceID] = flows[i].ID
		}

		if len(flowTicketIDs) > 0 {
			tickets, err := uc.ticketRepo.GetTicketsV2(ctx, uc.DB.DB(), taskModels.TicketCondition{
				Where: taskModels.TicketWhere{
					IDs: flowTicketIDs,
				},
				Columns: []string{},
				Preload: taskModels.TicketPreload{},
			})

			if err != nil {
				return assetInspectionVehicleResponses, err
			}

			for i := range tickets {
				mapTickets[mapTicketIDToFlowID[tickets[i].ID]] = tickets[i]
			}
		}
	}

	if len(ticketIDs) > 0 {
		tickets, err := uc.ticketRepo.GetTicketsV2(ctx, uc.DB.DB(), taskModels.TicketCondition{
			Where: taskModels.TicketWhere{
				IDs: ticketIDs,
			},
			Columns: []string{},
			Preload: taskModels.TicketPreload{},
		})

		if err != nil {
			return assetInspectionVehicleResponses, err
		}

		for i := range tickets {
			mapTickets[tickets[i].ID] = tickets[i]
		}
	}

	usersMapById := map[string]userIdentityModel.User{}

	err = uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return assetInspectionVehicleResponses, err
	}

	mapDigispectVehiclePhoto := map[string]string{}
	if len(digispectVehicleIDs) > 0 {
		digispectVehicles, err := uc.digispectRepo.GetDigispectVehicles(ctx, uc.DB.DB(), models.DigispectVehicleCondition{
			Where: models.DigispectVehicleWhere{
				IDs:            digispectVehicleIDs,
				WithOrmDeleted: true,
			},
		})
		if err != nil {
			return assetInspectionVehicleResponses, err
		}
		for _, digispectVehicle := range digispectVehicles {
			if digispectVehicle.Photo.String != "" {
				signedUrl, err := uc.StorageRepository.GetFileSignedURL(ctx, digispectVehicle.Photo.String, time.Now().Add(24*time.Hour))
				if err == nil {
					mapDigispectVehiclePhoto[digispectVehicle.ID] = signedUrl
				} else {
					commonlogger.Warnf("err get attachment path: %s err: %v",
						digispectVehicle.Photo.String, err)
				}
			}
		}
	}

	response := make([]dtos.GetAssetInspectionVehiclesResponse, 0, len(vehiclesInspected))
	for _, assetInspectionVehicle := range vehiclesInspected {
		userId := assetInspectionVehicle.AssetInspection.InspectByUserID

		response = append(response, dtos.BuildGetAssetInspectionVehiclesResponseItem(
			assetInspectionVehicle,
			usersMapById[userId].GetName(),
			mapTickets[assetInspectionVehicle.AssetInspection.ReferenceID],
			mapDigispectVehiclePhoto[assetInspectionVehicle.DigispectVehicleID]))
	}

	assetInspectionVehicleResponses = commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         response,
	}

	return assetInspectionVehicleResponses, nil
}

func (uc *AssetInspectionUseCase) GetAssetInspectionTyres(ctx context.Context, req dtos.GetInspectionListReq, isFromDigiSpect bool) (commonmodel.ListResponse, error) {
	resp := commonmodel.ListResponse{
		PageSize: req.PageSize,
		PageNo:   req.PageNo,
	}
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return resp, err
	}

	isViewAll := claim.IsHasPermission(
		constants.INSPECTION_PERMISSION_CATEGORY_TYREOPTIMAX,
		constants.INSPECTION_PERMISSION_VIEW_ALL_DIGISPECT_INSPECTIONS,
	)

	if req.IgnoreViewPermission {
		isViewAll = true
	}

	maxStartDate := time.Time{}
	inspectByUserID := ""
	if isFromDigiSpect {
		if !isViewAll {
			inspectByUserID = claim.UserID
		}

		client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
			Where: userModels.ClientWhere{
				ID: claim.GetLoggedInClientID(),
			},
			Columns: []string{"client_packages"},
		})
		if err != nil {
			return resp, err
		}

		maxStartDate, err = uc.getMaxStartDateInspectionDigispect(ctx, claim, client, isViewAll)
		if err != nil {
			return resp, err
		}

		if maxStartDate.IsZero() {
			return resp, nil
		}
	} else {
		err = uc.validateFilter(req)
		if err != nil {
			resp = commonmodel.ListResponse{
				TotalRecords: 0,
				PageSize:     1,
				PageNo:       1,
				Data:         err,
			}
			return resp, nil
		}
	}

	totalCount, tyresInspected, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyreList(
		ctx,
		uc.DB.DB(),
		models.GetAssetInspectionTyreListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetInspectionTyreCondition{
				Where: models.AssetInspectionTyreWhere{
					StartDate:              req.StartDate,
					StartDate1:             maxStartDate,
					EndDate:                req.EndDate,
					ClientID:               claim.GetLoggedInClientID(),
					InspectionID:           req.InspectionID,
					AssetID:                req.AssetID,
					AssetIDs:               req.AssetIDs,
					InspectByUserIDs:       req.InspectByUserIDs,
					InspectByUserID:        inspectByUserID,
					ReferenceID:            req.ReferenceID,
					ReferenceCode:          req.ReferenceCode,
					Direction:              req.Direction,
					SortBy:                 req.SortBy,
					IsSingleTyreInspection: req.IsSingleTyreInspection,
					HasPartnerOwnerID:      req.HasPartnerOwnerID,
					DigiSpectOnlySource:    isFromDigiSpect,
				},
				Preload: models.AssetInspectionTyrePreload{
					AssetInspection:             true,
					AssetTyre:                   true,
					AssetTyreAsset:              true,
					AssetTyreTyre:               true,
					AssetInspectionVehicleAsset: true,
					AssetTyreAssetBrand:         true,
					DigispectConfig:             true,
				},
				Columns: []string{},
			},
		},
	)
	if err != nil {
		return resp, err
	}

	ticketIDs := make([]string, 0, len(tyresInspected))
	flowIDs := make([]string, 0)
	userIDs := []string{}
	for i := range tyresInspected {
		if tyresInspected[i].AssetInspection.ReferenceCode == constants.ASSET_INSPECTION_REFERENCE_CODE_WORK_ORDER {
			ticketIDs = append(ticketIDs, tyresInspected[i].AssetInspection.ReferenceID)
		} else if tyresInspected[i].AssetInspection.ReferenceCode == constants.ASSET_INSPECTION_REFERENCE_CODE_FLOW {
			flowIDs = append(flowIDs, tyresInspected[i].AssetInspection.ReferenceID)
		}
		userIDs = append(userIDs, tyresInspected[i].AssetInspection.InspectByUserID)
	}

	mapFlowIDToTickets := map[string]taskModels.Ticket{}
	if len(flowIDs) > 0 {
		flows, err := uc.formRepo.GetFlows(ctx, uc.DB.DB(), contentModel.FlowCondition{
			Where: contentModel.FlowWhere{
				IDs: flowIDs,
			},
		})
		if err != nil {
			return resp, err
		}

		mapTicketIDToFlowID := map[string]string{}
		flowTicketIDs := []string{}
		for i := range flows {
			flowTicketIDs = append(flowTicketIDs, flows[i].ReferenceID)
			mapTicketIDToFlowID[flows[i].ReferenceID] = flows[i].ID
		}

		if len(flowTicketIDs) > 0 {
			tickets, err := uc.ticketRepo.GetTicketsV2(ctx, uc.DB.DB(), taskModels.TicketCondition{
				Where: taskModels.TicketWhere{
					IDs: flowTicketIDs,
				},
				Columns: []string{},
				Preload: taskModels.TicketPreload{},
			})

			if err != nil {
				return resp, err
			}

			for i := range tickets {
				mapFlowIDToTickets[mapTicketIDToFlowID[tickets[i].ID]] = tickets[i]
			}
		}

	}

	mapTickets := map[string]taskModels.Ticket{}
	if len(ticketIDs) > 0 && !isFromDigiSpect {
		tickets, err := uc.ticketRepo.GetTicketsV2(ctx, uc.DB.DB(), taskModels.TicketCondition{
			Where: taskModels.TicketWhere{
				IDs: ticketIDs,
			},
			Columns: []string{},
			Preload: taskModels.TicketPreload{},
		})

		if err != nil {
			return resp, err
		}

		for i := range tickets {
			mapTickets[tickets[i].ID] = tickets[i]
		}
	}

	mapUserName := map[string]string{}
	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return resp, err
		}

		for _, user := range users {
			mapUserName[user.ID] = user.GetName()
		}
	}

	resp = commonmodel.ListResponse{
		TotalRecords: totalCount,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data: dtos.BuildGetAssetInspectionTyresResponse(
			tyresInspected, mapUserName, mapTickets,
			mapFlowIDToTickets,
		),
	}

	if isFromDigiSpect &&
		!claim.IsDigiSpectProPlus() &&
		(len(tyresInspected) < resp.PageSize || resp.PageSize*resp.PageNo >= totalCount) {
		var err error
		resp.IsHasMoreData, err = uc.isDigiSpectInspectionHasMoreData(ctx, claim, maxStartDate, isViewAll)
		if err != nil {
			return resp, err
		}
	}

	return resp, nil
}

func (uc *AssetInspectionUseCase) generateAssetInspectionExportSignedUrl(ctx context.Context, xlsx *excelize.File) (string, *multipart.FileHeader, error) {
	body := new(bytes.Buffer)
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile("file", "Riwayat_Inspeksi.xlsx")
	if err != nil {
		return "", nil, err
	}
	xlsxBuffer, err := xlsx.WriteToBuffer()
	if err != nil {
		return "", nil, err
	}
	fileContents, err := ioutil.ReadAll(xlsxBuffer)
	if err != nil {
		return "", nil, err
	}
	part.Write(fileContents)
	err = writer.Close()
	if err != nil {
		return "", nil, err
	}

	xlsxRequest, _ := http.NewRequest("POST", "", body)
	xlsxRequest.Header.Add("Content-Type", writer.FormDataContentType())
	xlsxFile, xlsxHeader, err := xlsxRequest.FormFile("file")
	if err != nil {
		return "", nil, err
	}
	defer xlsxFile.Close()

	xlsxHeader.Filename = storageConstants.TEMP_USER_EXPORT_PREFIX + "Riwayat_Inspeksi_" + helpers.GenerateSecureFileName(".xlsx")
	err = uc.StorageRepository.UploadFile(ctx, xlsxFile, xlsxHeader)
	if err != nil {
		return "", nil, err
	}

	signedUrl, err := uc.StorageRepository.GetFileSignedURL(ctx, xlsxHeader.Filename, time.Now().Add(24*time.Hour))
	if err != nil {
		return "", nil, err
	}

	return signedUrl, xlsxHeader, nil
}

func (uc *AssetInspectionUseCase) generateAssetInspectionExportPDFSignedUrl(ctx context.Context, pdfgBuffer *bytes.Buffer) (string, *multipart.FileHeader, error) {
	body := new(bytes.Buffer)
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile("file", "Rincian_Inspeksi.pdf")
	if err != nil {
		return "", nil, err
	}
	fileContents, err := ioutil.ReadAll(pdfgBuffer)
	if err != nil {
		return "", nil, err
	}
	part.Write(fileContents)
	err = writer.Close()
	if err != nil {
		return "", nil, err
	}

	pdfRequest, _ := http.NewRequest("POST", "", body)
	pdfRequest.Header.Add("Content-Type", writer.FormDataContentType())
	pdfFile, pdfHeader, err := pdfRequest.FormFile("file")
	if err != nil {
		return "", nil, err
	}
	defer pdfFile.Close()

	pdfHeader.Filename = storageConstants.TEMP_USER_EXPORT_PREFIX + "Rincian_Inspeksi_" + helpers.GenerateSecureFileName(".pdf")
	err = uc.StorageRepository.UploadFile(ctx, pdfFile, pdfHeader)
	if err != nil {
		return "", nil, err
	}

	signedUrl, err := uc.StorageRepository.GetFileSignedURL(ctx, pdfHeader.Filename, time.Now().Add(24*time.Hour))
	if err != nil {
		return "", nil, err
	}

	return signedUrl, pdfHeader, nil
}

func (uc *AssetInspectionUseCase) ExportAssetInspectionTyres(ctx context.Context, req dtos.GetInspectionListReq) (commonmodel.DetailResponse, error) {
	var generateExcelResponse commonmodel.DetailResponse
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return generateExcelResponse, err
	}
	err = uc.validateFilter(req)
	if err != nil {
		return generateExcelResponse, nil
	}

	var inspectionIDs []string
	if len(req.InspectionIDs) > 0 {
		inspectionIDs = req.InspectionIDs
	}

	if req.AssetID != "" || len(req.AssetID) > 0 {
		_, assetTyresInspected, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyreList(
			ctx,
			uc.DB.DB(),
			models.GetAssetInspectionTyreListParam{
				ListRequest: commonmodel.ListRequest{
					PageNo:   1,
					PageSize: 100,
				},
				Cond: models.AssetInspectionTyreCondition{
					Where: models.AssetInspectionTyreWhere{
						ClientID:          claim.GetLoggedInClientID(),
						AssetID:           req.AssetID,
						AssetIDs:          req.AssetIDs,
						HasPartnerOwnerID: req.HasPartnerOwnerID,
					},
					Preload: models.AssetInspectionTyrePreload{
						AssetInspection: true,
					},
					Columns: []string{},
				},
			},
		)
		if err != nil {
			return generateExcelResponse, err
		}

		for _, assetTyreInspection := range assetTyresInspected {
			if len(inspectionIDs) < 12 {
				hasMatch := false
				for _, inspectionID := range inspectionIDs {
					if assetTyreInspection.AssetInspectionID == inspectionID {
						hasMatch = true
					}
				}

				if !hasMatch {
					inspectionIDs = append(inspectionIDs, assetTyreInspection.AssetInspectionID)
				}
			}
		}
	}

	_, tyresInspected, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyreListExport(
		ctx,
		uc.DB.DB(),
		models.GetAssetInspectionTyreListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetInspectionTyreCondition{
				Where: models.AssetInspectionTyreWhere{
					ClientID:               claim.GetLoggedInClientID(),
					InspectionID:           req.InspectionID,
					InspectionIDs:          inspectionIDs,
					SortBy:                 "asset_inspection_id",
					HasPartnerOwnerID:      req.HasPartnerOwnerID,
					InspectByUserIDs:       req.InspectByUserIDs,
					ReferenceID:            req.ReferenceID,
					ReferenceCode:          req.ReferenceCode,
					IsSingleTyreInspection: req.IsSingleTyreInspection,
				},
				Preload: models.AssetInspectionTyrePreload{
					AssetInspection:             true,
					AssetTyre:                   true,
					AssetTyreAsset:              true,
					AssetTyreAssetBrand:         true,
					AssetTyreTyre:               true,
					AssetInspectionVehicle:      true,
					AssetInspectionVehicleAsset: true,
				},
				Columns: []string{},
			},
		},
	)
	if err != nil {
		return generateExcelResponse, err
	}

	if len(tyresInspected) == 0 {
		return generateExcelResponse, nil
	}

	var userIds []string
	var assetTyreIds []string
	var assetInspectionIds []string
	for _, inspection := range tyresInspected {
		userIds = append(userIds, inspection.AssetInspection.InspectByUserID)
		assetTyreIds = append(assetTyreIds, inspection.AssetTyreID)
		assetInspectionIds = append(assetInspectionIds, inspection.ID)
	}
	users := []userIdentityModel.User{}

	usersMapById := map[string]userIdentityModel.User{}

	err = uc.UserRepository.GetUsersByIds(ctx, uc.DB.DB(), &users, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return generateExcelResponse, err
	}
	for _, user := range users {
		usersMapById[user.ID] = user
	}

	assetTyres, err := uc.AssetTyreRepository.GetAssetTyres(ctx, uc.DB.DB(), models.AssetTyreCondition{
		Where: models.AssetTyreWhere{
			AssetIDs: assetTyreIds,
		},
		Preload: models.AssetTyrePreload{
			Tyre:       true,
			AssetBrand: true,
		},
	})
	if err != nil {
		commonlogger.Errorf("Error in getting asset tyres by asset tyre ids from asset tyre service", err)
		return generateExcelResponse, err
	}
	assetTyresMapById := map[string]models.AssetTyre{}
	for _, assetTyre := range assetTyres {
		assetTyresMapById[assetTyre.AssetID] = assetTyre
	}

	// FETCH ATTACHMENTS
	attachmentsMapByInspectionID := make(map[string][]storageDtos.GetAttachmentsResp, len(assetInspectionIds))
	attachments, err := uc.AttachmentUseCase.GetAttachments(ctx, storageDtos.GetAttachmentsReq{
		ReferenceCode:      "ASSET_INSPECTION",
		SourceReferenceIDs: assetInspectionIds,
	})
	if err != nil {
		return generateExcelResponse, err
	}
	for _, attachment := range attachments {
		if len(attachmentsMapByInspectionID[attachment.SourceReferenceID]) < 3 {
			attachmentsMapByInspectionID[attachment.SourceReferenceID] = append(attachmentsMapByInspectionID[attachment.SourceReferenceID], attachment)
		}
	}
	attachmentColumnTotal := 3

	newResponse := dtos.BuildGetAssetInspectionTyresExportResponse(tyresInspected, usersMapById, assetTyresMapById, map[string]models.AssetInspectionVehicle{})

	// START GENERATE EXCEL
	// DEFINE END COL
	endColumn := "P"
	addLogoColumn := "O"

	// INITIAL DECLARATION
	xlsx := excelize.NewFile()
	sheetName := "TyreOptimaX DigiSpect"
	xlsx.SetSheetName(xlsx.GetSheetName(0), sheetName)

	// SET HEADER BG
	headerStyle, err := xlsx.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
	})
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return generateExcelResponse, err
	}
	xlsx.SetCellStyle(sheetName, "A1", endColumn+"7", headerStyle)

	// SET HEADER TITLE AND IMAGE
	title := "Riwayat Inspeksi Ban"
	titleStyle, err := xlsx.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
		Font: &excelize.Font{
			Size: 27,
			Bold: true,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return generateExcelResponse, err
	}
	xlsx.MergeCell(sheetName, "A2", endColumn+"4")
	xlsx.SetCellStr(sheetName, "A2", title)
	xlsx.SetCellStyle(sheetName, "A2", "A2", titleStyle)
	if err := xlsx.AddPicture(sheetName, "A2", "./statics/tyre-optimax-digispect.png", &excelize.GraphicOptions{ScaleX: 0.3, ScaleY: 0.3}); err != nil {
		commonlogger.Errorf("Error in adding image to excel file", err)
		return generateExcelResponse, err
	}
	if err := xlsx.AddPicture(sheetName, addLogoColumn+"2", "./statics/logo-alt-1.png", &excelize.GraphicOptions{ScaleX: 0.3, ScaleY: 0.3}); err != nil {
		commonlogger.Errorf("Error in adding image to excel file", err)
		return generateExcelResponse, err
	}

	// SET DOWNLOAD DATE
	downloadDateStyle, err := xlsx.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
		Font: &excelize.Font{
			Size:  9,
			Color: "#1F1F1F",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	now := time.Now()
	username := claim.GetName()
	downloadDate := "downloaded by " + username + " at " + now.Format("02-Jan-2006 15:04")
	xlsx.MergeCell(sheetName, "A5", endColumn+"5")
	xlsx.SetCellStr(sheetName, "A5", downloadDate)
	xlsx.SetCellStyle(sheetName, "A5", "A5", downloadDateStyle)

	// SET TABLE TITLES
	tableTitleStyle, err := xlsx.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#185FFF"},
		},
		Font: &excelize.Font{
			Size:  10,
			Bold:  true,
			Color: "#FFFFFF",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "top",
			WrapText:   true,
		},
		Border: []excelize.Border{
			{
				Type:  "top",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "right",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 4,
			},
		},
	})
	tableTitles := []map[string]interface{}{
		{"column": "A8", "value": "No"},
		{"column": "B8", "value": "ID Perangkat"},
		{"column": "C8", "value": "No. Inspeksi"},
		{"column": "D8", "value": "Waktu Inspeksi"},
		{"column": "E8", "value": "Tebal Tapak Ban 1 (mm)"},
		{"column": "F8", "value": "Tebal Tapak Ban 2 (mm)"},
		{"column": "G8", "value": "Tebal Tapak Ban 3 (mm)"},
		{"column": "H8", "value": "Tebal Tapak Ban 4 (mm)"},
		{"column": "I8", "value": "Rata-Rata Tebal Tapak Ban (mm)"},
		{"column": "J8", "value": "TUR (%)"},
		{"column": "K8", "value": "Tekanan Ban (psi)"},
	}
	lastTableTitleColumnChar := 'K'
	lastTableTitleColumn := "K"
	for i := 0; i < attachmentColumnTotal; i++ {
		lastTableTitleColumnChar += 1
		lastTableTitleColumn = string(lastTableTitleColumnChar)

		attachmenTableTitle := map[string]interface{}{"column": lastTableTitleColumn + "8", "value": "Picture " + strconv.Itoa(i+1)}
		tableTitles = append(tableTitles, attachmenTableTitle)
	}
	// Diperiksa Oleh Title
	lastTableTitleColumnChar += 1
	lastTableTitleColumn = string(lastTableTitleColumnChar)
	tableTitles = append(tableTitles, map[string]interface{}{"column": lastTableTitleColumn + "8", "value": "Diperiksa Oleh"})
	// Catatan Title
	lastTableTitleColumnChar += 1
	lastTableTitleColumn = string(lastTableTitleColumnChar)
	tableTitles = append(tableTitles, map[string]interface{}{"column": lastTableTitleColumn + "8", "value": "Catatan"})

	for _, item := range tableTitles {
		title, _ := item["value"].(string)
		col, _ := item["column"].(string)
		xlsx.SetCellStr(sheetName, col, title)
	}
	lastCol, _ := tableTitles[len(tableTitles)-1]["column"].(string)
	xlsx.SetCellStyle(sheetName, "A8", lastCol, tableTitleStyle)

	// SET TABLE VALUES
	tableValueStyle, err := xlsx.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:  10,
			Color: "#000000",
		},
		Border: []excelize.Border{
			{
				Type:  "top",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "right",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 4,
			},
		},
	})
	tableHyperlinkValueStyle, err := xlsx.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:      10,
			Color:     "#1265BE",
			Underline: "single",
		},
		Border: []excelize.Border{
			{
				Type:  "top",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "right",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 4,
			},
		},
	})
	numFormat := "#,##0;-#,##0;-;@"
	tableNumberValueStyle, err := xlsx.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:  10,
			Color: "#000000",
		},
		Border: []excelize.Border{
			{
				Type:  "top",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "right",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 4,
			},
		},
		CustomNumFmt: &numFormat,
	})
	floatNumFormat := "#,##0.##;-#,##0.##;-;@"
	tableFloatNumberValueStyle, err := xlsx.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:  10,
			Color: "#000000",
		},
		Border: []excelize.Border{
			{
				Type:  "top",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "right",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 4,
			},
		},
		CustomNumFmt: &floatNumFormat,
	})

	sort.Slice(newResponse, func(prev, next int) bool {
		if newResponse[prev].AssetInspectionCreatedAt != newResponse[next].AssetInspectionCreatedAt {
			return newResponse[prev].AssetInspectionCreatedAt.After(newResponse[next].AssetInspectionCreatedAt)
		}
		if newResponse[prev].InspectionNumber != newResponse[next].InspectionNumber {
			return newResponse[prev].InspectionNumber > newResponse[next].InspectionNumber
		}
		if newResponse[prev].ReferenceNumber != newResponse[next].ReferenceNumber {
			return newResponse[prev].ReferenceNumber > newResponse[next].ReferenceNumber
		}
		return newResponse[prev].TyrePosition > newResponse[next].TyrePosition
	})

	xlsx.SetCellStyle(sheetName, "A9", endColumn+fmt.Sprint(8+len(newResponse)), tableValueStyle)
	xlsx.SetColWidth(sheetName, "L", "N", 20)
	xlsx.SetCellStyle(sheetName, "E9", fmt.Sprintf("K%d", 8+len(newResponse)), tableNumberValueStyle)
	for idx, item := range newResponse {
		xlsx.SetCellValue(sheetName, fmt.Sprintf("A%d", idx+9), idx+1)
		xlsx.SetCellValue(sheetName, fmt.Sprintf("B%d", idx+9), "-")
		if item.DeviceID != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf("B%d", idx+9), item.DeviceID)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("C%d", idx+9), "-")
		if item.InspectionNumber != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf("C%d", idx+9), item.InspectionNumber)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("D%d", idx+9), item.AssetInspectionCreatedAt.Format("02 January 2006 15:04:05"))
		xlsx.SetCellValue(sheetName, fmt.Sprintf("E%d", idx+9), 0)
		if item.RDT1 > 0 {
			formattedRDT1 := helpers.FormatDecimalNumber(item.RDT1)
			if helpers.FloatHasPrecision(formattedRDT1) {
				xlsx.SetCellStyle(sheetName, fmt.Sprintf("E%d", idx+9), fmt.Sprintf("E%d", idx+9), tableFloatNumberValueStyle)
			}
			xlsx.SetCellValue(sheetName, fmt.Sprintf("E%d", idx+9), formattedRDT1)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("F%d", idx+9), 0)
		if item.RDT2 > 0 {
			formattedRDT2 := helpers.FormatDecimalNumber(item.RDT2)
			if helpers.FloatHasPrecision(formattedRDT2) {
				xlsx.SetCellStyle(sheetName, fmt.Sprintf("F%d", idx+9), fmt.Sprintf("F%d", idx+9), tableFloatNumberValueStyle)
			}
			xlsx.SetCellValue(sheetName, fmt.Sprintf("F%d", idx+9), formattedRDT2)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("G%d", idx+9), 0)
		if item.RDT3 > 0 {
			formattedRDT3 := helpers.FormatDecimalNumber(item.RDT3)
			if helpers.FloatHasPrecision(formattedRDT3) {
				xlsx.SetCellStyle(sheetName, fmt.Sprintf("G%d", idx+9), fmt.Sprintf("G%d", idx+9), tableFloatNumberValueStyle)
			}
			xlsx.SetCellValue(sheetName, fmt.Sprintf("G%d", idx+9), formattedRDT3)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("H%d", idx+9), 0)
		if item.RDT4 > 0 {
			formattedRDT4 := helpers.FormatDecimalNumber(item.RDT4)
			if helpers.FloatHasPrecision(formattedRDT4) {
				xlsx.SetCellStyle(sheetName, fmt.Sprintf("H%d", idx+9), fmt.Sprintf("H%d", idx+9), tableFloatNumberValueStyle)
			}
			xlsx.SetCellValue(sheetName, fmt.Sprintf("H%d", idx+9), formattedRDT4)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("I%d", idx+9), 0)
		if item.AverageRTD > 0 {
			formattedAverageRTD := helpers.FormatDecimalNumber(item.AverageRTD)
			if helpers.FloatHasPrecision(formattedAverageRTD) {
				xlsx.SetCellStyle(sheetName, fmt.Sprintf("I%d", idx+9), fmt.Sprintf("I%d", idx+9), tableFloatNumberValueStyle)
			}
			xlsx.SetCellValue(sheetName, fmt.Sprintf("I%d", idx+9), formattedAverageRTD)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("J%d", idx+9), 0)
		if item.UtilizationRatePercentage > 0 {
			formattedTUR := helpers.FormatDecimalNumber(item.UtilizationRatePercentage)
			if helpers.FloatHasPrecision(formattedTUR) {
				xlsx.SetCellStyle(sheetName, fmt.Sprintf("J%d", idx+9), fmt.Sprintf("J%d", idx+9), tableFloatNumberValueStyle)
			}
			xlsx.SetCellValue(sheetName, fmt.Sprintf("J%d", idx+9), formattedTUR)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("K%d", idx+9), 0)
		if item.Pressure > 0 {
			xlsx.SetCellValue(sheetName, fmt.Sprintf("K%d", idx+9), item.Pressure)
		}
		currentRowEndColChar := 'K'
		currentRowEndCol := "K"
		xlsx.SetCellValue(sheetName, fmt.Sprintf("L%d", idx+9), "-")
		xlsx.SetCellValue(sheetName, fmt.Sprintf("M%d", idx+9), "-")
		xlsx.SetCellValue(sheetName, fmt.Sprintf("N%d", idx+9), "-")
		for _, attachment := range attachmentsMapByInspectionID[item.ID] {
			currentRowEndColChar += 1
			currentRowEndCol = string(currentRowEndColChar)

			xlsx.SetCellValue(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), attachment.Path)
			tooltip := "Open Image"
			xlsx.SetCellHyperLink(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), attachment.Path, "External", excelize.HyperlinkOpts{
				Tooltip: &tooltip,
			})
			xlsx.SetCellStyle(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), fmt.Sprintf(currentRowEndCol+"%d", idx+9), tableHyperlinkValueStyle)
		}
		// Diperiksa Oleh Value
		currentRowEndColChar = 'K' + 4
		currentRowEndCol = string(currentRowEndColChar)
		xlsx.SetCellValue(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), "-")
		if item.AssetInspectionInspectedByUserName != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), item.AssetInspectionInspectedByUserName)
		}
		// Catatan Value
		currentRowEndColChar += 1
		currentRowEndCol = string(currentRowEndColChar)
		xlsx.SetCellValue(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), "-")
		if item.Remark != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), item.Remark)
		}
	}

	signedUrl, xlsxHeader, err := uc.generateAssetInspectionExportSignedUrl(ctx, xlsx)
	if err != nil {
		commonlogger.Errorf("Error in generating exported excel download url", err)
		return generateExcelResponse, err
	}
	// END GENERATE EXCEL

	generateExcelResponse = commonmodel.DetailResponse{
		Success:     true,
		Message:     "asset tyre inspection list is successfully exported",
		ReferenceID: signedUrl,
		Data:        xlsxHeader,
	}

	return generateExcelResponse, nil
}

func (uc *AssetInspectionUseCase) ExportAssetInspectionTyresFull(ctx context.Context, req dtos.GetInspectionListReq) (commonmodel.DetailResponse, error) {
	var generateExcelResponse commonmodel.DetailResponse
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return generateExcelResponse, err
	}
	err = uc.validateFilter(req)
	if err != nil {
		return generateExcelResponse, nil
	}

	var inspectionIDs []string
	if len(req.InspectionIDs) > 0 {
		inspectionIDs = req.InspectionIDs
	}

	if req.AssetID != "" || len(req.AssetID) > 0 {
		_, assetTyresInspected, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyreList(
			ctx,
			uc.DB.DB(),
			models.GetAssetInspectionTyreListParam{
				ListRequest: commonmodel.ListRequest{
					PageNo:   1,
					PageSize: 100,
				},
				Cond: models.AssetInspectionTyreCondition{
					Where: models.AssetInspectionTyreWhere{
						ClientID:          claim.GetLoggedInClientID(),
						AssetID:           req.AssetID,
						AssetIDs:          req.AssetIDs,
						HasPartnerOwnerID: req.HasPartnerOwnerID,
					},
					Preload: models.AssetInspectionTyrePreload{
						AssetInspection: true,
					},
					Columns: []string{},
				},
			},
		)
		if err != nil {
			return generateExcelResponse, err
		}

		for _, assetTyreInspection := range assetTyresInspected {
			if len(inspectionIDs) < 12 {
				hasMatch := false
				for _, inspectionID := range inspectionIDs {
					if assetTyreInspection.AssetInspectionID == inspectionID {
						hasMatch = true
					}
				}

				if !hasMatch {
					inspectionIDs = append(inspectionIDs, assetTyreInspection.AssetInspectionID)
				}
			}
		}
	}

	_, tyresInspected, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyreListExport(
		ctx,
		uc.DB.DB(),
		models.GetAssetInspectionTyreListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetInspectionTyreCondition{
				Where: models.AssetInspectionTyreWhere{
					ClientID:               claim.GetLoggedInClientID(),
					InspectionID:           req.InspectionID,
					InspectionIDs:          inspectionIDs,
					SortBy:                 "asset_inspection_id",
					HasPartnerOwnerID:      req.HasPartnerOwnerID,
					InspectByUserIDs:       req.InspectByUserIDs,
					ReferenceID:            req.ReferenceID,
					ReferenceCode:          req.ReferenceCode,
					IsSingleTyreInspection: req.IsSingleTyreInspection,
				},
				Preload: models.AssetInspectionTyrePreload{
					AssetInspection:             true,
					AssetTyre:                   true,
					AssetTyreAsset:              true,
					AssetTyreAssetBrand:         true,
					AssetTyreTyre:               true,
					AssetInspectionVehicle:      true,
					AssetInspectionVehicleAsset: true,
				},
				Columns: []string{},
			},
		},
	)
	if err != nil {
		return generateExcelResponse, err
	}

	if len(tyresInspected) == 0 {
		return generateExcelResponse, nil
	}

	var userIds []string
	var inspectionTyreIDs []string
	for _, inspection := range tyresInspected {
		userIds = append(userIds, inspection.AssetInspection.InspectByUserID)
		inspectionTyreIDs = append(inspectionTyreIDs, inspection.ID)
	}
	users := []userIdentityModel.User{}

	usersMapById := map[string]userIdentityModel.User{}

	err = uc.UserRepository.GetUsersByIds(ctx, uc.DB.DB(), &users, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return generateExcelResponse, err
	}
	for _, user := range users {
		usersMapById[user.ID] = user
	}

	// FETCH ATTACHMENTS
	attachmentsMapByInspectionID := make(map[string][]storageDtos.GetAttachmentsResp, len(inspectionIDs))
	attachments, err := uc.AttachmentUseCase.GetAttachments(ctx, storageDtos.GetAttachmentsReq{
		ReferenceCode:      "ASSET_INSPECTION",
		SourceReferenceIDs: inspectionTyreIDs,
	})
	if err != nil {
		return generateExcelResponse, err
	}
	for _, attachment := range attachments {
		if len(attachmentsMapByInspectionID[attachment.SourceReferenceID]) < 3 {
			attachmentsMapByInspectionID[attachment.SourceReferenceID] = append(attachmentsMapByInspectionID[attachment.SourceReferenceID], attachment)
		}

	}
	attachmentColumnTotal := 3

	newResponse := dtos.BuildGetAssetInspectionTyresExportResponse(tyresInspected, usersMapById, map[string]models.AssetTyre{}, map[string]models.AssetInspectionVehicle{})

	// START GENERATE EXCEL
	// DEFINE END COL
	endColumn := "W"
	addLogoColumn := "V"

	// INITIAL DECLARATION
	xlsx := excelize.NewFile()
	sheetName := "TyreOptimaX DigiSpect"
	xlsx.SetSheetName(xlsx.GetSheetName(0), sheetName)

	// SET HEADER BG
	headerStyle, err := xlsx.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
	})
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return generateExcelResponse, err
	}
	xlsx.SetCellStyle(sheetName, "A1", endColumn+"7", headerStyle)

	// SET HEADER TITLE AND IMAGE
	title := "Riwayat Inspeksi Ban"
	titleStyle, err := xlsx.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
		Font: &excelize.Font{
			Size: 27,
			Bold: true,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return generateExcelResponse, err
	}
	xlsx.MergeCell(sheetName, "A2", endColumn+"4")
	xlsx.SetCellStr(sheetName, "A2", title)
	xlsx.SetCellStyle(sheetName, "A2", "A2", titleStyle)
	if err := xlsx.AddPicture(sheetName, "A2", "./statics/tyre-optimax-digispect.png", &excelize.GraphicOptions{ScaleX: 0.3, ScaleY: 0.3}); err != nil {
		commonlogger.Errorf("Error in adding image to excel file", err)
		return generateExcelResponse, err
	}
	if err := xlsx.AddPicture(sheetName, addLogoColumn+"2", "./statics/logo-alt-1.png", &excelize.GraphicOptions{ScaleX: 0.3, ScaleY: 0.3}); err != nil {
		commonlogger.Errorf("Error in adding image to excel file", err)
		return generateExcelResponse, err
	}

	// SET DOWNLOAD DATE
	downloadDateStyle, err := xlsx.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
		Font: &excelize.Font{
			Size:  9,
			Color: "#1F1F1F",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	now := time.Now()
	username := claim.GetName()
	downloadDate := "downloaded by " + username + " at " + now.Format("02-Jan-2006 15:04")
	xlsx.MergeCell(sheetName, "A5", endColumn+"5")
	xlsx.SetCellStr(sheetName, "A5", downloadDate)
	xlsx.SetCellStyle(sheetName, "A5", endColumn+"5", downloadDateStyle)

	// SET TABLE TITLES
	tableTitleStyle, err := xlsx.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#185FFF"},
		},
		Font: &excelize.Font{
			Size:  10,
			Bold:  true,
			Color: "#FFFFFF",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "top",
			WrapText:   true,
		},
		Border: []excelize.Border{
			{
				Type:  "top",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "right",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 4,
			},
		},
	})
	tableTitles := []map[string]interface{}{
		{"column": "A8", "value": "No"},
		{"column": "B8", "value": "Device ID"},
		{"column": "C8", "value": "No. Inpeksi"},
		{"column": "D8", "value": "Waktu Inspeksi"},
		{"column": "E8", "value": "Pelat Nomor"},
		{"column": "F8", "value": "Odometer"},
		{"column": "G8", "value": "Posisi Ban"},
		{"column": "H8", "value": "Nomor Seri Ban"},
		{"column": "I8", "value": "RFID"},
		{"column": "J8", "value": "Merek"},
		{"column": "K8", "value": "Ukuran"},
		{"column": "L8", "value": "Tebal Tapak Ban 1 (mm)"},
		{"column": "M8", "value": "Tebal Tapak Ban 2 (mm)"},
		{"column": "N8", "value": "Tebal Tapak Ban 3 (mm)"},
		{"column": "O8", "value": "Tebal Tapak Ban 4 (mm)"},
		{"column": "P8", "value": "Rata-rata Tebal Tapak Ban (mm)"},
		{"column": "Q8", "value": "TUR (%)"},
		{"column": "R8", "value": "Tekanan Ban (psi)"},
	}
	lastTableTitleColumnChar := 'R'
	lastTableTitleColumn := "R"
	for i := 0; i < attachmentColumnTotal; i++ {
		lastTableTitleColumnChar += 1
		lastTableTitleColumn = string(lastTableTitleColumnChar)

		attachmenTableTitle := map[string]interface{}{"column": lastTableTitleColumn + "8", "value": "Picture " + strconv.Itoa(i+1)}
		tableTitles = append(tableTitles, attachmenTableTitle)
	}
	// Diperiksa Oleh Title
	lastTableTitleColumnChar += 1
	lastTableTitleColumn = string(lastTableTitleColumnChar)
	tableTitles = append(tableTitles, map[string]interface{}{"column": lastTableTitleColumn + "8", "value": "Diperiksa oleh"})
	// Catatan Title
	lastTableTitleColumnChar += 1
	lastTableTitleColumn = string(lastTableTitleColumnChar)
	tableTitles = append(tableTitles, map[string]interface{}{"column": lastTableTitleColumn + "8", "value": "Catatan"})

	for _, item := range tableTitles {
		title, _ := item["value"].(string)
		col, _ := item["column"].(string)
		xlsx.SetCellStr(sheetName, col, title)
	}
	lastCol, _ := tableTitles[len(tableTitles)-1]["column"].(string)
	xlsx.SetCellStyle(sheetName, "A8", lastCol, tableTitleStyle)

	// SET TABLE VALUES
	tableValueStyle, err := xlsx.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:  10,
			Color: "#000000",
		},
		Border: []excelize.Border{
			{
				Type:  "top",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "right",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 4,
			},
		},
	})
	tableHyperlinkValueStyle, err := xlsx.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:      10,
			Color:     "#1265BE",
			Underline: "single",
		},
		Border: []excelize.Border{
			{
				Type:  "top",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "right",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 4,
			},
		},
	})
	numFormat := "#,##0;-#,##0;-;@"
	tableNumberValueStyle, err := xlsx.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:  10,
			Color: "#000000",
		},
		Border: []excelize.Border{
			{
				Type:  "top",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "right",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 4,
			},
		},
		CustomNumFmt: &numFormat,
	})
	floatNumFormat := "#,##0.##;-#,##0.##;-;@"
	tableFloatNumberValueStyle, err := xlsx.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:  10,
			Color: "#000000",
		},
		Border: []excelize.Border{
			{
				Type:  "top",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "right",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 4,
			},
		},
		CustomNumFmt: &floatNumFormat,
	})

	sort.Slice(newResponse, func(prev, next int) bool {
		if newResponse[prev].AssetInspectionCreatedAt != newResponse[next].AssetInspectionCreatedAt {
			return newResponse[prev].AssetInspectionCreatedAt.After(newResponse[next].AssetInspectionCreatedAt)
		}
		if newResponse[prev].InspectionNumber != newResponse[next].InspectionNumber {
			return newResponse[prev].InspectionNumber > newResponse[next].InspectionNumber
		}
		if newResponse[prev].ReferenceNumber != newResponse[next].ReferenceNumber {
			return newResponse[prev].ReferenceNumber > newResponse[next].ReferenceNumber
		}
		return newResponse[prev].TyrePosition > newResponse[next].TyrePosition
	})

	xlsx.SetCellStyle(sheetName, "A9", endColumn+fmt.Sprint(8+len(newResponse)), tableValueStyle)
	xlsx.SetColWidth(sheetName, "S", "U", 20)
	xlsx.SetCellStyle(sheetName, "F9", fmt.Sprintf("F%d", 8+len(newResponse)), tableNumberValueStyle)
	xlsx.SetColWidth(sheetName, "F", "F", 15)
	xlsx.SetCellStyle(sheetName, "L9", fmt.Sprintf("R%d", 8+len(newResponse)), tableNumberValueStyle)
	for idx, item := range newResponse {
		xlsx.SetCellValue(sheetName, fmt.Sprintf("A%d", idx+9), idx+1)
		xlsx.SetCellValue(sheetName, fmt.Sprintf("B%d", idx+9), "-")
		if item.DeviceID != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf("B%d", idx+9), item.DeviceID)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("C%d", idx+9), "-")
		if item.InspectionNumber != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf("C%d", idx+9), item.InspectionNumber)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("D%d", idx+9), item.AssetInspectionCreatedAt.Format("02 January 2006 15:04:05"))
		xlsx.SetCellValue(sheetName, fmt.Sprintf("E%d", idx+9), "-")
		if item.ReferenceNumber != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf("E%d", idx+9), item.ReferenceNumber)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("F%d", idx+9), 0)
		if item.VehicleKM > 0 {
			xlsx.SetCellValue(sheetName, fmt.Sprintf("F%d", idx+9), math.Round(item.VehicleKM))
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("G%d", idx+9), item.TyrePosition)
		xlsx.SetCellValue(sheetName, fmt.Sprintf("H%d", idx+9), "-")
		if item.SerialNumber != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf("H%d", idx+9), item.SerialNumber)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("I%d", idx+9), "-")
		if item.Rfid != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf("I%d", idx+9), item.Rfid)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("J%d", idx+9), "-")
		if item.BrandName != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf("J%d", idx+9), item.BrandName)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("K%d", idx+9), "-")
		if item.TyreSize != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf("K%d", idx+9), item.TyreSize)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("L%d", idx+9), 0)
		if item.RDT1 > 0 {
			formattedRDT1 := helpers.FormatDecimalNumber(item.RDT1)
			if helpers.FloatHasPrecision(formattedRDT1) {
				xlsx.SetCellStyle(sheetName, fmt.Sprintf("L%d", idx+9), fmt.Sprintf("L%d", idx+9), tableFloatNumberValueStyle)
			}
			xlsx.SetCellValue(sheetName, fmt.Sprintf("L%d", idx+9), formattedRDT1)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("M%d", idx+9), 0)
		if item.RDT2 > 0 {
			formattedRDT2 := helpers.FormatDecimalNumber(item.RDT2)
			if helpers.FloatHasPrecision(formattedRDT2) {
				xlsx.SetCellStyle(sheetName, fmt.Sprintf("M%d", idx+9), fmt.Sprintf("M%d", idx+9), tableFloatNumberValueStyle)
			}
			xlsx.SetCellValue(sheetName, fmt.Sprintf("M%d", idx+9), formattedRDT2)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("N%d", idx+9), 0)
		if item.RDT3 > 0 {
			formattedRDT3 := helpers.FormatDecimalNumber(item.RDT3)
			if helpers.FloatHasPrecision(formattedRDT3) {
				xlsx.SetCellStyle(sheetName, fmt.Sprintf("N%d", idx+9), fmt.Sprintf("N%d", idx+9), tableFloatNumberValueStyle)
			}
			xlsx.SetCellValue(sheetName, fmt.Sprintf("N%d", idx+9), formattedRDT3)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("O%d", idx+9), 0)
		if item.RDT4 > 0 {
			formattedRDT4 := helpers.FormatDecimalNumber(item.RDT4)
			if helpers.FloatHasPrecision(formattedRDT4) {
				xlsx.SetCellStyle(sheetName, fmt.Sprintf("O%d", idx+9), fmt.Sprintf("O%d", idx+9), tableFloatNumberValueStyle)
			}
			xlsx.SetCellValue(sheetName, fmt.Sprintf("O%d", idx+9), formattedRDT4)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("P%d", idx+9), "-")
		if item.AverageRTD > 0 {
			formattedAverageRTD := helpers.FormatDecimalNumber(item.AverageRTD)
			if helpers.FloatHasPrecision(formattedAverageRTD) {
				xlsx.SetCellStyle(sheetName, fmt.Sprintf("P%d", idx+9), fmt.Sprintf("P%d", idx+9), tableFloatNumberValueStyle)
			}
			xlsx.SetCellValue(sheetName, fmt.Sprintf("P%d", idx+9), formattedAverageRTD)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("Q%d", idx+9), "-")
		if item.UtilizationRatePercentage > 0 {
			formattedTUR := helpers.FormatDecimalNumber(item.UtilizationRatePercentage)
			if helpers.FloatHasPrecision(formattedTUR) {
				xlsx.SetCellStyle(sheetName, fmt.Sprintf("Q%d", idx+9), fmt.Sprintf("Q%d", idx+9), tableFloatNumberValueStyle)
			}
			xlsx.SetCellValue(sheetName, fmt.Sprintf("Q%d", idx+9), formattedTUR)
		}
		xlsx.SetCellValue(sheetName, fmt.Sprintf("R%d", idx+9), 0)
		if item.Pressure > 0 {
			xlsx.SetCellValue(sheetName, fmt.Sprintf("R%d", idx+9), item.Pressure)
		}
		currentRowEndColChar := 'R'
		currentRowEndCol := "R"
		xlsx.SetCellValue(sheetName, fmt.Sprintf("S%d", idx+9), "-")
		xlsx.SetCellValue(sheetName, fmt.Sprintf("T%d", idx+9), "-")
		xlsx.SetCellValue(sheetName, fmt.Sprintf("U%d", idx+9), "-")
		for _, attachment := range attachmentsMapByInspectionID[item.ID] {
			currentRowEndColChar += 1
			currentRowEndCol = string(currentRowEndColChar)

			xlsx.SetCellValue(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), attachment.Path)
			tooltip := "Open Image"
			xlsx.SetCellHyperLink(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), attachment.Path, "External", excelize.HyperlinkOpts{
				Tooltip: &tooltip,
			})
			xlsx.SetCellStyle(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), fmt.Sprintf(currentRowEndCol+"%d", idx+9), tableHyperlinkValueStyle)
		}
		// Diperiksa Oleh Value
		currentRowEndColChar = 'R' + 4
		currentRowEndCol = string(currentRowEndColChar)
		xlsx.SetCellValue(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), "-")
		if item.AssetInspectionInspectedByUserName != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), item.AssetInspectionInspectedByUserName)
		}
		// Catatan Value
		currentRowEndColChar += 1
		currentRowEndCol = string(currentRowEndColChar)
		xlsx.SetCellValue(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), "-")
		if item.Remark != "" {
			xlsx.SetCellValue(sheetName, fmt.Sprintf(currentRowEndCol+"%d", idx+9), item.Remark)
		}
	}

	signedUrl, xlsxHeader, err := uc.generateAssetInspectionExportSignedUrl(ctx, xlsx)
	if err != nil {
		commonlogger.Errorf("Error in generating exported excel download url", err)
		return generateExcelResponse, err
	}
	// END GENERATE EXCEL

	generateExcelResponse = commonmodel.DetailResponse{
		Success:     true,
		Message:     "asset tyre inspection list is successfully exported",
		ReferenceID: signedUrl,
		Data:        xlsxHeader,
	}

	return generateExcelResponse, nil
}

func (uc *AssetInspectionUseCase) UpsertAssetInspectionAssignment(ctx context.Context, req dtos.UpsertAssetInspectionAssignment) (*commonmodel.CreateResponse, error) {
	assetInspectionAssignment := &models.AssetInspectionAssignment{
		ModelV2:      commonmodel.ModelV2{},
		InspectionID: req.InspectionID,
		UserID:       req.UserID,
		UserName:     req.UserName,
		TypeCode:     req.TypeCode,
	}

	// // to get assInpectionAssignee before insert (notification purposes)
	// assInpectionAssignee, _ := uc.AssetInspectionRepository.GetAssetInspectionAssignments(ctx, uc.DB.DB(), models.AssetInspectionAssignmentCondition{
	// 	Where: models.AssetInspectionAssignmentWhere{
	// 		InspectionID: req.InspectionID,
	// 		TypeCode:     req.TypeCode,
	// 	},
	// })

	err := uc.AssetInspectionRepository.UpsertAssetInspectionAssignment(ctx, uc.DB.WithCtx(ctx).DB(), assetInspectionAssignment)
	if err != nil {
		return nil, err
	}

	// go uc.notifyWorkshopVehicleTyreAssignment(ctx, req, assInpectionAssignee)
	go uc.notifyWorkshopVehicleTyreAssignment(ctx, req)

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *AssetInspectionUseCase) notifyWorkshopVehicleTyreAssignment(
	ctx context.Context,
	req dtos.UpsertAssetInspectionAssignment,
	// prevInspect []models.AssetInspectionAssignment,
) {
	// claim, err := authhelpers.GetClaimFromCtx(ctx)
	// if err != nil {
	// 	commonlogger.Warnf("Error in getting claim at notification asset inspection assign", err)
	// 	return
	// }

	assetInspection, err := uc.AssetInspectionRepository.GetAssetInspection(ctx, uc.DB.DB(),
		models.AssetInspectionCondition{
			Where: models.AssetInspectionWhere{
				ID: req.InspectionID,
			},
		},
	)
	if err != nil {
		commonlogger.Warnf("Error in getting asset inspection by req asset inspection id", err)
		return
	}

	// assInpectionAssignee, err := uc.AssetInspectionRepository.GetAssetInspectionAssignments(ctx, uc.DB.DB(), models.AssetInspectionAssignmentCondition{
	// 	Where: models.AssetInspectionAssignmentWhere{
	// 		InspectionID: assetInspection.ID,
	// 		TypeCode:     req.TypeCode,
	// 	},
	// })
	// if err != nil {
	// 	commonlogger.Warnf("Error in getting asset inspection assignee by asset inspection id", err)
	// 	return
	// }

	ticket, err := uc.ticketRepo.GetTicketByID(ctx, uc.DB.DB(), assetInspection.ReferenceID)
	if err != nil {
		commonlogger.Warnf("Error in getting ticket by asset inspection reference id from asset inspection service", err)
		return
	}

	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			ID: ticket.AssignedToUserID.String,
		},
	})
	if err != nil {
		commonlogger.Warnf("error get user on notify update ticket status", err)
		return
	}

	platNo := ""
	asset := &models.Asset{}
	if ticket.ReferenceID != ticketConstant.GENERAL_TICKET_REFERENCE_ID {
		if ticket.TicketReferenceCode == ticketConstant.TICKET_ASSET_VEHICLE_REF {
			asset, err = uc.AssetRepository.GetAssetByID(ctx, uc.DB.DB(), ticket.ReferenceID)
			if err != nil {
				commonlogger.Warnf("Error in getting asset by asset id from asset service", err)
				return
			}

			if asset.ReferenceNumber == "" {
				platNo = asset.SerialNumber
			} else {
				platNo = asset.ReferenceNumber
			}
		}
	}

	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: ticket.ClientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return
	}

	isTyre := req.TypeCode == constants.ASSET_INSPECTION_VEHICLE_TYRE
	loc, _ := time.LoadLocation("Asia/Jakarta")

	href := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notifConstants.DESTINATION_TYPE_WORK_ORDER, ticket.ID)
	templateBod := tmplhelpers.AssetInspectionAssignee{
		UserAssigned:   user.GetName(),
		RedirectWOLink: tmplhelpers.ParseURLTemplate(href),
		CustomerName:   "Customer Name",
		IsVehicleTyre:  isTyre,
		PlatNo:         "-",
		TicketDesc:     "-",
		TicketNo:       ticket.TicketNumber,
		ScheduleDate:   ticket.ScheduleDatetime.In(loc).Format(timehelpers.RFC1123Notif),
	}
	if platNo != "" {
		templateBod.PlatNo = platNo
	}
	if ticket.Description != "" {
		templateBod.TicketDesc = ticket.Description
	}
	if asset.PartnerOwnerName != "" {
		templateBod.CustomerName = asset.PartnerOwnerName
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            req.UserID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_UPDATE,
		SourceReferenceID: ticket.ID,
		TargetReferenceID: ticket.ReferenceID,
		TargetURL:         href,
		MessageHeader:     templateBod.ConstructSubjectEmail(),
		MessageBody:       templateBod.ConstructBodyEmail(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templateBod.ConstrucPushNotifSubject(),
			Body:  templateBod.ConstrucPushNotifBody(),
		},
		ClientID:        ticket.ClientID,
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:  ticket.ID,
	}

	notifications := []notificationDtos.CreateNotificationItem{notifItem}

	// switch scenario {
	// case 1:
	// 	// previous assignee
	// 	notifItem.UserID = prevInspect[0].UserID
	// 	notifications = append(notifications, notifItem)
	// case 2:
	// 	// previous assignee
	// 	notifItem.UserID = prevInspect[0].UserID
	// 	notifications = append(notifications, notifItem)

	// 	// new assignee
	// 	notifItem.UserID = req.UserID
	// 	notifications = append(notifications, notifItem)
	// case 3:
	// 	// Work order assignee
	// 	notifItem.UserID = ticket.AssignedToUserID
	// 	notifications = append(notifications, notifItem)

	// 	// new assignee
	// 	notifItem.UserID = req.UserID
	// 	notifications = append(notifications, notifItem)
	// case 4:
	// 	// new assignee
	// 	notifItem.UserID = req.UserID
	// 	notifications = append(notifications, notifItem)

	// 	notifItem.UserID = ticket.AssignedToUserID
	// 	notifications = append(notifications, notifItem)
	// }

	// notifications = append(notifications, notifItem)

	_ = uc.notifUseCase.CreateNotification(contexthelpers.WithoutCancel(ctx), notificationDtos.CreateNotificationReq{
		Items:           notifications,
		SendToEmail:     true,
		SendToPushNotif: true,
	})

}

func (uc *AssetInspectionUseCase) GetAssetInspectionAssignmentsByInspectionID(ctx context.Context, inspectionID string) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	assetInspectionAssignments, err := uc.AssetInspectionRepository.GetAssetInspectionAssignments(ctx, uc.DB.DB(), models.AssetInspectionAssignmentCondition{
		Where: models.AssetInspectionAssignmentWhere{
			InspectionID: inspectionID,
			ClientID:     claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	resp := make([]dtos.AssetInspectionAssignment, 0, len(assetInspectionAssignments))
	for i := range assetInspectionAssignments {
		resp = append(resp, dtos.AssetInspectionAssignment{
			ID:           assetInspectionAssignments[i].ID,
			InspectionID: assetInspectionAssignments[i].InspectionID,
			UserID:       assetInspectionAssignments[i].UserID,
			UserName:     assetInspectionAssignments[i].UserName,
			TypeCode:     assetInspectionAssignments[i].TypeCode,
		})
	}
	return &commonmodel.ListResponse{
		TotalRecords: len(assetInspectionAssignments),
		PageSize:     len(assetInspectionAssignments),
		PageNo:       1,
		Data:         resp,
	}, nil
}

func (uc *AssetInspectionUseCase) UpdateInspectionStatus(ctx context.Context, id string, req dtos.UpdateInspectionStatusReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	inspection, err := uc.AssetInspectionRepository.GetAssetInspection(ctx, uc.DB.DB(), models.AssetInspectionCondition{
		Where: models.AssetInspectionWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	if inspection.StatusCode == req.StatusCode {
		return &commonmodel.UpdateResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: id,
			Data:        nil,
		}, nil
	}

	err = uc.AssetInspectionRepository.UpdateAssetInspection(ctx, uc.DB.WithCtx(ctx).DB(), id, &models.AssetInspection{
		StatusCode: req.StatusCode,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *AssetInspectionUseCase) ExportAssetInspectionTyresDetailPDF(ctx context.Context, id string, req dtos.GetInspectionListReq) (commonmodel.DetailResponse, error) {
	var generatePDFResponse commonmodel.DetailResponse

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return generatePDFResponse, err
	}
	err = uc.validateFilter(req)
	if err != nil {
		return generatePDFResponse, nil
	}

	// IMPORT IMAGES
	baseDir, err := os.Getwd()
	if err != nil {
		commonlogger.Errorf("Error in fetching base dir", err.Error())
		return generatePDFResponse, err
	}
	// ASSETFINDR LOGO
	assetfindrLogoImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/assetfindr-logo.png")
	if err != nil {
		return generatePDFResponse, err
	}
	// DIGISPECT LOGO
	digispectLogoImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/digispect-logo.png")
	if err != nil {
		return generatePDFResponse, err
	}
	// PRESSURE ENABLED IMAGE
	pressureEnabledImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/pressure-enabled.svg")
	if err != nil {
		return generatePDFResponse, err
	}
	// PRESSURE DISABLED IMAGE
	pressureDisabledImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/pressure-disabled.svg")
	if err != nil {
		return generatePDFResponse, err
	}

	// FETCH CLIENT PHOTO
	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
		Preload: userIdentityModel.ClientPreload{
			Status: true,
		},
	})
	if err != nil {
		return generatePDFResponse, err
	}
	clientPhoto := assetfindrLogoImgPath
	if client.Photo.String != "" {
		clientPhoto, _ = helpers.GenerateCloudStorageSignedURL(client.Photo.String, time.Duration(24))
	}
	secondaryClientPhoto := digispectLogoImgPath
	if client.SecondaryPhoto.String != "" {
		secondaryClientPhoto, _ = helpers.GenerateCloudStorageSignedURL(client.SecondaryPhoto.String, time.Duration(24))
	}

	// FETCH INSPECTION
	assetInspection, err := uc.AssetInspectionRepository.GetAssetInspection(
		ctx,
		uc.DB.DB(),
		models.AssetInspectionCondition{
			Where: models.AssetInspectionWhere{
				ID:       id,
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: models.AssetInspectionPreload{},
		},
	)
	if err != nil {
		return generatePDFResponse, err
	}

	// FETCH INSPECT USER
	inspectionAssignmentID := assetInspection.InspectByUserID
	inspectionUser := userIdentityModel.User{}
	var inspectionUserFullName string
	_ = uc.UserRepository.GetUserById(ctx, uc.DB.DB(), &inspectionUser, inspectionAssignmentID)
	if inspectionUser.FirstName != "" {
		inspectionUserFullName = inspectionUser.FirstName + " " + inspectionUser.LastName
	}

	// FETCH TYRE INSPECTION
	singleInspectionTyre := models.AssetInspectionTyre{}
	assetInspectionTyres := []models.AssetInspectionTyre{}
	_, assetInspectionTyres, err = uc.AssetInspectionTyreRepository.GetAssetInspectionTyreList(
		ctx,
		uc.DB.DB(),
		models.GetAssetInspectionTyreListParam{
			ListRequest: commonmodel.ListRequest{
				PageSize: 1,
				PageNo:   1,
			},
			Cond: models.AssetInspectionTyreCondition{
				Where: models.AssetInspectionTyreWhere{
					InspectionID: assetInspection.ID,
					ClientID:     claim.GetLoggedInClientID(),
				},
				Preload: models.AssetInspectionTyrePreload{
					AssetTyre:      true,
					AssetTyreTyre:  true,
					AssetTyreAsset: true,
				},
			},
		},
	)

	if len(assetInspectionTyres) == 1 {
		singleInspectionTyre = assetInspectionTyres[0]
	}

	// DEVICE ID
	deviceId := singleInspectionTyre.DeviceID

	// FETCH ASSET TYRE
	var assetTyre *models.AssetTyre
	if singleInspectionTyre.AssetTyreID != "" {
		assetTyre, err = uc.AssetTyreRepository.GetAssetTyreByID(ctx, uc.DB.DB(), singleInspectionTyre.AssetTyreID)
		if err != nil {
			return generatePDFResponse, err
		}
	}

	// CONSTRUCT ATTACHMENTS
	attachments, err := uc.AttachmentUseCase.GetAttachments(ctx, storageDtos.GetAttachmentsReq{
		ReferenceCode:     "ASSET_INSPECTION",
		SourceReferenceID: singleInspectionTyre.ID,
		TargetReferenceID: "",
	})
	if err != nil {
		return generatePDFResponse, err
	}

	// TYRE BASE SVG
	withOTD := false
	treadPoint := singleInspectionTyre.NumberOfInspectionPoint
	if treadPoint == 0 || treadPoint > 5 {
		treadPoint = 4
	}
	RDT1 := singleInspectionTyre.RDT1
	RDT2 := singleInspectionTyre.RDT2
	RDT3 := singleInspectionTyre.RDT3
	RDT4 := singleInspectionTyre.RDT4
	averageRTD := helpers.CalculateAverageRTD(RDT1, RDT2, RDT3, RDT4)
	averageRTD = helpers.TruncateFloat(averageRTD, 1)
	var OTD float64 = 0
	if assetTyre != nil {
		OTD = assetTyre.StartThreadDepth
		withOTD = true
	} else {
		OTD = RDT1
		if RDT2 > OTD {
			OTD = RDT2
		}
		if RDT3 > OTD {
			OTD = RDT3
		}
		if RDT4 > OTD {
			OTD = RDT4
		}
	}

	TUR := helpers.CalculateTyreUtilRate(OTD, averageRTD)

	tyreWaveSource := dtos.TyreWaveSource{
		NoOfInspectionPoint: treadPoint,
		RDT1:                RDT1,
		RDT2:                RDT2,
		RDT3:                RDT3,
		RDT4:                RDT4,
		OTD:                 OTD,
		ShowOTD:             withOTD,
	}
	tyreWaveSvg := uc.AssetInspectionUtil.GenerateTyreWaveSvg(tyreWaveSource)

	brandName := "-"
	tyreSize := "-"
	serialNo := "-"
	rfid := "-"
	if singleInspectionTyre.AssetTyreID != "" {
		serialNo = singleInspectionTyre.AssetTyre.Asset.SerialNumber
		brandName = assetTyre.Asset.Brand.BrandName
		tyreSize = assetTyre.Tyre.SectionWidth + " " + assetTyre.Tyre.Construction + " " + assetTyre.Tyre.RimDiameter
		rfid = singleInspectionTyre.AssetTyre.Asset.Rfid
	}

	if singleInspectionTyre.CustomSerialNumber != "" && serialNo == "-" {
		serialNo = singleInspectionTyre.CustomSerialNumber
	}
	if singleInspectionTyre.CustomBrandName.String != "" && brandName == "-" {
		brandName = singleInspectionTyre.CustomBrandName.String
	}
	if singleInspectionTyre.CustomTyreSize != "" && tyreSize == "-" {
		tyreSize = singleInspectionTyre.CustomTyreSize
	}
	if singleInspectionTyre.CustomRFID != "" && rfid == "-" {
		rfid = singleInspectionTyre.CustomRFID
	}

	if rfid == "-" {
		rfid = ""
	}

	// INSPECTION DATE
	inspectionDate := assetInspection.CreatedAt.Format("2 Jan 2006 15:04:05")

	// USE HTML TEMPLATING TO APPEND DATA
	type PageData struct {
		AssetInspection      *models.AssetInspection
		InspectionTyre       models.AssetInspectionTyre
		Attachments          []storageDtos.GetAttachmentsResp
		AverageRTD           float64
		BrandName            string
		TyreSize             string
		SerialNo             string
		InspectionDate       string
		TreadPoint           int
		Rfid                 string
		ClientPhoto          string
		SecondaryClientPhoto string
		PressureEnabledImg   string
		PressureDisabledImg  string
		TyreWaveSvg          dtos.TyreWaveSvgResponse
		RemainingTUR         float64
	}
	templateData := PageData{
		AssetInspection:      assetInspection,
		InspectionTyre:       singleInspectionTyre,
		Attachments:          attachments,
		AverageRTD:           averageRTD,
		BrandName:            brandName,
		TyreSize:             tyreSize,
		SerialNo:             serialNo,
		InspectionDate:       inspectionDate,
		TreadPoint:           treadPoint,
		Rfid:                 rfid,
		ClientPhoto:          clientPhoto,
		SecondaryClientPhoto: secondaryClientPhoto,
		PressureEnabledImg:   pressureEnabledImgPath,
		PressureDisabledImg:  pressureDisabledImgPath,
		TyreWaveSvg:          tyreWaveSvg,
		RemainingTUR:         100.0 - TUR,
	}
	templateHTML := template.Must(template.ParseFiles("./statics/print-template/digispect_inspection_single_tyre.html"))
	var templateBuff bytes.Buffer
	err = templateHTML.Execute(&templateBuff, &templateData)
	if err != nil {
		return generatePDFResponse, err
	}

	// GENERATE PDF
	newPage := wkhtmltopdf.NewPageReader(bytes.NewBuffer(templateBuff.Bytes()))
	newPage.EnableLocalFileAccess.Set(true)

	// START OF FOOTER
	footerInpectedBy := "Diinspeksi oleh -"
	if inspectionUserFullName != "" {
		footerInpectedBy = "Diinspeksi oleh " + inspectionUserFullName
	}
	footerDeviceID := ""
	if deviceId != "" {
		footerDeviceID = " • ID Perangkat " + deviceId
	}
	footerLocation := ""
	if assetInspection.LocationLabel != "" {
		footerLocation = " • " + assetInspection.LocationLabel
	}
	footerNote := footerInpectedBy + footerDeviceID + footerLocation
	newPage.FooterLeft.Set(footerNote)
	newPage.FooterRight.Set("Halaman [page]/[toPage]")
	newPage.FooterFontName.Set("Poppins")
	newPage.FooterFontSize.Set(6)
	// ASSETFINDR WEB FOOTER
	footerHTML := filepath.Join(baseDir, "statics/print-template/digispect_inspection_footer_watermark.html")
	footerPageURL := "file:///" + filepath.ToSlash(footerHTML)
	newPage.FooterHTML.Set(footerPageURL)
	// END OF FOOTER

	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		commonlogger.Errorf("Error in initializing pdf generator", err.Error())
		return generatePDFResponse, err
	}
	pdfg.Dpi.Set(768)
	pdfg.Orientation.Set(wkhtmltopdf.OrientationPortrait)
	pdfg.AddPage(newPage)
	pdfg.MarginBottom.Set(8)

	err = pdfg.Create()
	if err != nil {
		commonlogger.Errorf("Error in creating pdf", err.Error())
		return generatePDFResponse, err
	}

	pdfgBuffer := pdfg.Buffer()
	signedUrl, pdfHeader, err := uc.generateAssetInspectionExportPDFSignedUrl(ctx, pdfgBuffer)
	if err != nil {
		commonlogger.Errorf("Error in generating exported pdf download url", err)
		return generatePDFResponse, err
	}

	generatePDFResponse = commonmodel.DetailResponse{
		Success:     true,
		Message:     "asset tyre inspection detail is successfully exported",
		ReferenceID: signedUrl,
		Data:        pdfHeader,
	}

	return generatePDFResponse, nil
}

func (uc *AssetInspectionUseCase) ExportAssetInspectionTyresDetailPDFFull(ctx context.Context, id string, req dtos.GetInspectionListReq) (commonmodel.DetailResponse, error) {
	var generatePDFResponse commonmodel.DetailResponse

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return generatePDFResponse, err
	}
	err = uc.validateFilter(req)
	if err != nil {
		return generatePDFResponse, nil
	}

	// IMPORT IMAGES
	baseDir, err := os.Getwd()
	if err != nil {
		commonlogger.Errorf("Error in fetching base dir", err.Error())
		return generatePDFResponse, err
	}
	// ASSETFINDR LOGO
	assetfindrLogoImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/assetfindr-logo.png")
	if err != nil {
		return generatePDFResponse, err
	}
	// DIGISPECT LOGO
	digispectLogoImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/digispect-logo.png")
	if err != nil {
		return generatePDFResponse, err
	}
	// CHECK MARK
	checkImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/check.svg")
	if err != nil {
		return generatePDFResponse, err
	}
	// CROSS MARK
	crossImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/cross.svg")
	if err != nil {
		return generatePDFResponse, err
	}
	// PRESSURE ENABLED IMAGE
	pressureEnabledImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/pressure-enabled.svg")
	if err != nil {
		return generatePDFResponse, err
	}
	// PRESSURE DISABLED IMAGE
	pressureDisabledImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/pressure-disabled.svg")
	if err != nil {
		return generatePDFResponse, err
	}
	// TYRE ILLUSTRATION PLACEHOLDER
	tyreIllustrationPlaceholderImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/tyre-illustration-placeholder.svg")
	if err != nil {
		return generatePDFResponse, err
	}

	// FETCH CLIENT DATA
	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
		Preload: userIdentityModel.ClientPreload{
			Status: true,
		},
	})
	if err != nil {
		return generatePDFResponse, err
	}
	clientPhoto := assetfindrLogoImgPath
	if client.Photo.String != "" {
		clientPhoto, _ = helpers.GenerateCloudStorageSignedURL(client.Photo.String, time.Duration(24))
	}
	secondaryClientPhoto := digispectLogoImgPath
	if client.SecondaryPhoto.String != "" {
		secondaryClientPhoto, _ = helpers.GenerateCloudStorageSignedURL(client.SecondaryPhoto.String, time.Duration(24))
	}

	// FETCH INSPECTION
	assetInspection, err := uc.AssetInspectionRepository.GetAssetInspection(
		ctx,
		uc.DB.DB(),
		models.AssetInspectionCondition{
			Where: models.AssetInspectionWhere{
				ID:       id,
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: models.AssetInspectionPreload{},
		},
	)
	if err != nil {
		return generatePDFResponse, err
	}
	inspectionAssignmentID := assetInspection.InspectByUserID
	inspectionUser := userIdentityModel.User{}
	var inspectionUserFullName string
	_ = uc.UserRepository.GetUserById(ctx, uc.DB.DB(), &inspectionUser, inspectionAssignmentID)
	if inspectionUser.FirstName != "" {
		inspectionUserFullName = inspectionUser.FirstName + " " + inspectionUser.LastName
	}

	// FETCH TYRE INSPECTION
	assetInspectionTyres := []models.AssetInspectionTyre{}
	_, err = uc.AssetInspectionTyreRepository.GetAssetInspectionTyres(
		ctx,
		uc.DB.DB(),
		&assetInspectionTyres,
		commonmodel.ListRequest{
			SearchKeyword: claim.GetLoggedInClientID(),
			PageSize:      100,
			PageNo:        1,
		},
		assetInspection.ID,
	)
	if err != nil {
		return generatePDFResponse, err
	}

	// FETCH VEHICLE INSPECTION
	assetInspectionVehicle, err := uc.AssetInspectionVehicleRepository.GetAssetInspectionVehicle(ctx, uc.DB.DB(), models.AssetInspectionVehicleCondition{
		Where: models.AssetInspectionVehicleWhere{
			ClientID:     claim.GetLoggedInClientID(),
			InspectionID: assetInspection.ID,
		},
		Preload: models.AssetInspectionVehiclePreload{
			AssetVehicle:             true,
			AssetVehicleAsset:        true,
			AssetVehicleVehicle:      true,
			AssetVehicleVehicleBrand: true,
			Asset:                    true,
			AssetBrand:               true,
			AssetLocation:            true,
		},
	})
	if err != nil {
		return generatePDFResponse, err
	}

	// FETCH AND MAP ASSET TYRE BY ID
	var assetTyreIds []string
	var assetInspectionIds []string
	for _, inspection := range assetInspectionTyres {
		assetTyreIds = append(assetTyreIds, inspection.AssetTyreID)
		assetInspectionIds = append(assetInspectionIds, inspection.ID)
	}
	assetTyres := []models.AssetTyre{}
	assetTyresMapById := map[string]models.AssetTyre{}
	err = uc.AssetTyreRepository.GetAssetTyresByIds(ctx, uc.DB.DB(), &assetTyres, assetTyreIds)
	if err != nil {
		commonlogger.Errorf("Error in getting asset tyres by asset tyre ids from asset tyre service", err)
		return generatePDFResponse, err
	}
	for _, assetTyre := range assetTyres {
		assetTyresMapById[assetTyre.AssetID] = assetTyre
	}

	// FETCH ATTACHMENTS
	attachmentsMapByInspectionID := make(map[string][]storageDtos.GetAttachmentsResp, len(assetInspectionIds))
	if len(assetInspectionIds) > 0 {
		attachments, err := uc.AttachmentUseCase.GetAttachments(ctx, storageDtos.GetAttachmentsReq{
			ReferenceCode:      "ASSET_INSPECTION",
			SourceReferenceIDs: assetInspectionIds,
		})
		if err != nil {
			return generatePDFResponse, err
		}
		for _, attachment := range attachments {
			attachmentsMapByInspectionID[attachment.SourceReferenceID] = append(attachmentsMapByInspectionID[attachment.SourceReferenceID], attachment)
		}
	}

	// FETCH VEHICLE ATTACHMENTS
	hasSpareTyre := false
	spareTyreShowAttachmentPos := 0
	vehicleAttachments, err := uc.AttachmentUseCase.GetAttachments(ctx, storageDtos.GetAttachmentsReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET_INSPECTION,
		SourceReferenceID: assetInspectionVehicle.ID,
	})
	if err != nil {
		return generatePDFResponse, err
	}

	// REF AXLE CONFIG
	var refAxleConfiguration pgtype.JSONB
	if assetInspectionVehicle.AssetVehicle.AxleConfiguration.Status == pgtype.Present {
		refAxleConfiguration = assetInspectionVehicle.AssetVehicle.AxleConfiguration
	} else {
		refAxleConfiguration = assetInspectionVehicle.AxleConfiguration
	}

	// CONSTRUCT AXLE CONFIGURATIONS
	type axleConfiguration struct {
		value       string
		numOfTyres  int
		isSpareTyre bool
		name        string
		width       int
		height      int
	}
	axleConfigurations := map[string]axleConfiguration{
		"TWO_TYRES_WITH_STEERING_WHEEL": {
			value:       "TWO_TYRES_WITH_STEERING_WHEEL",
			numOfTyres:  2,
			isSpareTyre: false,
			name:        "axle.svg",
			width:       115,
			height:      65,
		},
		"TWO_TYRES": {
			value:       "TWO_TYRES",
			numOfTyres:  2,
			isSpareTyre: false,
			name:        "axle1.svg",
			width:       115,
			height:      65,
		},
		"TWO_TYRES_WITH_SPINDLE": {
			value:       "TWO_TYRES_WITH_SPINDLE",
			numOfTyres:  2,
			isSpareTyre: false,
			name:        "axle2.svg",
			width:       115,
			height:      65,
		},
		"FOUR_TYRES": {
			value:       "FOUR_TYRES",
			numOfTyres:  4,
			isSpareTyre: false,
			name:        "axle3.svg",
			width:       180,
			height:      65,
		},
		"FOUR_TYRES_WITH_SPINDLE": {
			value:       "FOUR_TYRES_WITH_SPINDLE",
			numOfTyres:  4,
			isSpareTyre: false,
			name:        "axle4.svg",
			width:       180,
			height:      65,
		},
		"ONE_SPARE_TYRE": {
			value:       "ONE_SPARE_TYRE",
			numOfTyres:  1,
			isSpareTyre: true,
			name:        "axle5.svg",
			width:       150,
			height:      40,
		},
	}

	remainingTyreDetailSectionCount := (len(assetInspectionTyres) + 1) % 4
	var remainingTyreDetailSectionNumbers []int
	if remainingTyreDetailSectionCount > 0 {
		remainingTyreDetailSectionCount = 4 - remainingTyreDetailSectionCount
	}
	for i := 0; i < remainingTyreDetailSectionCount; i += 1 {
		remainingTyreDetailSectionNumbers = append(remainingTyreDetailSectionNumbers, i)
	}

	// USE HTML TEMPLATING TO APPEND DATA
	type AssetInspectionTyreTempl struct {
		AssetInspectionTyre models.AssetInspectionTyre
		AssetTyre           models.AssetTyre
		Attachments         []storageDtos.GetAttachmentsResp
		AverageRTD          float64
		TUR                 float64
		RemainingTUR        float64
		TyreBaseSvg         template.HTML
		SerialNo            string
		BrandName           string
		TyreSize            string
		Remark              string
		TreadPoint          int
		Rfid                string
		TyreWaveSvg         dtos.TyreWaveSvgResponse
	}
	var assetInspectionTyreTemplData []AssetInspectionTyreTempl
	assetInspectionTyreTemplDataMap := map[string]AssetInspectionTyreTempl{}
	deviceId := ""
	var enabledTyrePositions []int
	for _, inspection := range assetInspectionTyres {
		if deviceId == "" {
			deviceId = inspection.DeviceID
		}

		remark := "-"
		if inspection.Remark != "" {
			remark = helpers.TruncateEnd(inspection.Remark, 48)
		}

		brandName := "-"
		tyreSize := "-"
		serialNo := "-"
		rfid := "-"
		if inspection.AssetTyreID != "" {
			serialNo = helpers.TruncateMiddle(inspection.AssetTyre.Asset.SerialNumber, 11)
			if assetTyresMapById[inspection.AssetTyreID].Asset.Brand.BrandName != "" {
				brandName = assetTyresMapById[inspection.AssetTyreID].Asset.Brand.BrandName
			}
			tyreSize = assetTyresMapById[inspection.AssetTyreID].Tyre.SectionWidth + " " + assetTyresMapById[inspection.AssetTyreID].Tyre.Construction + " " + assetTyresMapById[inspection.AssetTyreID].Tyre.RimDiameter
			rfid = inspection.AssetTyre.Asset.Rfid
		}

		if inspection.CustomSerialNumber != "" && serialNo == "-" {
			serialNo = helpers.TruncateMiddle(inspection.CustomSerialNumber, 11)
		}
		if inspection.CustomBrandName.String != "" && brandName == "-" {
			brandName = inspection.CustomBrandName.String
		}
		if inspection.CustomTyreSize != "" && tyreSize == "-" {
			tyreSize = inspection.CustomTyreSize
		}
		if inspection.CustomRFID != "" && rfid == "-" {
			rfid = inspection.CustomRFID
		}

		if rfid == "-" {
			rfid = ""
		}

		withTyreOptimax := true
		withOTD := false
		treadPoint := inspection.NumberOfInspectionPoint
		if treadPoint == 0 || treadPoint > 5 {
			treadPoint = 4
		}
		RDT1 := inspection.RDT1
		RDT2 := inspection.RDT2
		RDT3 := inspection.RDT3
		RDT4 := inspection.RDT4
		averageRTD := helpers.CalculateAverageRTD(RDT1, RDT2, RDT3, RDT4)
		averageRTD = helpers.TruncateFloat(averageRTD, 1)
		var OTD float64 = 0
		if inspection.AssetTyreID != "" {
			OTD = assetTyresMapById[inspection.AssetTyreID].StartThreadDepth
			withOTD = true
		} else {
			OTD = RDT1
			if RDT2 > OTD {
				OTD = RDT2
			}
			if RDT3 > OTD {
				OTD = RDT3
			}
			if RDT4 > OTD {
				OTD = RDT4
			}
		}

		TUR := helpers.CalculateTyreUtilRate(OTD, averageRTD)

		tyreBaseSvg := uc.AssetInspectionUtil.GenerateTyreBaseSvg(RDT1, RDT2, RDT3, RDT4, OTD, TUR, 0.44, withTyreOptimax, true, treadPoint, withOTD)

		tyreWaveSource := dtos.TyreWaveSource{
			NoOfInspectionPoint: treadPoint,
			RDT1:                RDT1,
			RDT2:                RDT2,
			RDT3:                RDT3,
			RDT4:                RDT4,
			OTD:                 OTD,
			ShowOTD:             withOTD,
		}
		tyreWaveSvg := uc.AssetInspectionUtil.GenerateTyreWaveSvg(tyreWaveSource)

		currentTemplateData := AssetInspectionTyreTempl{
			AssetInspectionTyre: inspection,
			AssetTyre:           assetTyresMapById[inspection.AssetTyreID],
			Attachments:         attachmentsMapByInspectionID[inspection.ID],
			AverageRTD:          averageRTD,
			TUR:                 TUR,
			RemainingTUR:        100 - TUR,
			TyreBaseSvg:         tyreBaseSvg,
			SerialNo:            serialNo,
			BrandName:           brandName,
			TyreSize:            tyreSize,
			Remark:              remark,
			TreadPoint:          treadPoint,
			Rfid:                rfid,
			TyreWaveSvg:         tyreWaveSvg,
		}
		assetInspectionTyreTemplData = append(assetInspectionTyreTemplData, currentTemplateData)
		assetInspectionTyreTemplDataMap[fmt.Sprintf("%.0f", inspection.TyrePosition)] = currentTemplateData
		enabledTyrePositions = append(enabledTyrePositions, int(inspection.TyrePosition))
	}

	// GENERATE PLACEHOLDER TYRE WAVE
	tyreWavePlaceholderSource := dtos.TyreWaveSource{
		NoOfInspectionPoint: 4,
		RDT1:                0,
		RDT2:                0,
		RDT3:                0,
		RDT4:                0,
		OTD:                 0,
		ShowOTD:             false,
	}
	tyreWavePlaceholderSvg := uc.AssetInspectionUtil.GenerateTyreWaveSvg(tyreWavePlaceholderSource)

	// GENERATE AXLE CONFIGURATIONS SVG
	var axleConfigurationItems []template.HTML
	tyreNumber := 1

	rAxleConfiguration := []models.AxleConfiguration{}
	err = refAxleConfiguration.AssignTo(&rAxleConfiguration)
	if err != nil {
		return generatePDFResponse, err
	}

	for _, assetAxleConfiguration := range rAxleConfiguration {
		axleConfigurationSvg, tyreNumberRes := uc.AssetInspectionUtil.GenerateAxleConfigurationSvg(assetAxleConfiguration.Axle, tyreNumber, 0.62, enabledTyrePositions)
		tyreNumber = tyreNumberRes

		axleConfigurationItems = append(axleConfigurationItems, axleConfigurationSvg)
	}

	axleConfigurationZoom := 1.0
	if len(axleConfigurationItems) >= 5 {
		axleConfigurationZoom = uc.AssetInspectionUtil.CalculateAxleConfigurationZoom(len(axleConfigurationItems))
	}

	// NEW LOGIC
	type TyreLayoutData struct {
		LayoutType          string // blank space, inspection section, axle configuration
		AssetInspectionTyre AssetInspectionTyreTempl
		TyreCount           int
	}
	blankTyreLayoutData := TyreLayoutData{
		LayoutType:          constants.INSPECTION_PRINT_TYRE_LAYOUT_TYPE_BLANK_SPACE,
		AssetInspectionTyre: AssetInspectionTyreTempl{},
		TyreCount:           0,
	}
	axleConfigTyreLayoutData := TyreLayoutData{
		LayoutType:          constants.INSPECTION_PRINT_TYRE_LAYOUT_TYPE_AXLE_CONFIGURATION,
		AssetInspectionTyre: AssetInspectionTyreTempl{},
		TyreCount:           0,
	}
	var tyreLayoutDataArr [][]TyreLayoutData
	tyreCount := 1
	runningTyreCount := 0
	spareTyreCount := 0
	totalInspectedTyres := 0
	totalInspectedRunningTyres := 0
	totalInspectedSpareTyres := 0

	rAxleConfiguration = []models.AxleConfiguration{}
	err = refAxleConfiguration.AssignTo(&rAxleConfiguration)
	if err != nil {
		return generatePDFResponse, err
	}

	for idx, assetAxleConfiguration := range rAxleConfiguration {
		currentConfig := axleConfigurations[assetAxleConfiguration.Axle]
		var currentRowTyreLayoutDataArr []TyreLayoutData
		for i := 0; i < 5; i++ {
			if currentConfig.numOfTyres == 2 {
				if idx == 0 && i == 2 {
					currentRowTyreLayoutDataArr = append(currentRowTyreLayoutDataArr, axleConfigTyreLayoutData)
				} else if idx != 0 && i == 2 {
					blankTyreLayoutData.LayoutType = constants.INSPECTION_PRINT_TYRE_LAYOUT_TYPE_AXLE_SPACER
					currentRowTyreLayoutDataArr = append(currentRowTyreLayoutDataArr, blankTyreLayoutData)
				} else if i == 0 || i == 4 {
					blankTyreLayoutData.LayoutType = constants.INSPECTION_PRINT_TYRE_LAYOUT_TYPE_AXLE_SIDE_SPACER
					currentRowTyreLayoutDataArr = append(currentRowTyreLayoutDataArr, blankTyreLayoutData)
				} else {
					currentTyre := assetInspectionTyreTemplDataMap[fmt.Sprintf("%d", tyreCount)]
					inspectionNewScenario2LayoutData := TyreLayoutData{
						LayoutType:          constants.INSPECTION_PRINT_TYRE_LAYOUT_TYPE_TYRE,
						AssetInspectionTyre: currentTyre,
						TyreCount:           tyreCount,
					}
					if currentTyre.AssetInspectionTyre.AssetInspectionID != "" {
						currentRowTyreLayoutDataArr = append(currentRowTyreLayoutDataArr, inspectionNewScenario2LayoutData)
						totalInspectedTyres++
						totalInspectedRunningTyres++
					} else {
						blankTyreLayoutData.LayoutType = constants.INSPECTION_PRINT_TYRE_LAYOUT_TYPE_TYRE_PLACEHOLDER
						blankTyreLayoutData.TyreCount = tyreCount
						currentRowTyreLayoutDataArr = append(currentRowTyreLayoutDataArr, blankTyreLayoutData)
					}
					tyreCount++
					runningTyreCount++
				}
			} else if currentConfig.numOfTyres == 4 {
				if i == 2 {
					blankTyreLayoutData.LayoutType = constants.INSPECTION_PRINT_TYRE_LAYOUT_TYPE_CARD_SPACER
					currentRowTyreLayoutDataArr = append(currentRowTyreLayoutDataArr, blankTyreLayoutData)
				} else {
					currentTyre := assetInspectionTyreTemplDataMap[fmt.Sprintf("%d", tyreCount)]
					inspectionNewScenario2LayoutData := TyreLayoutData{
						LayoutType:          constants.INSPECTION_PRINT_TYRE_LAYOUT_TYPE_TYRE,
						AssetInspectionTyre: currentTyre,
						TyreCount:           tyreCount,
					}
					if currentTyre.AssetInspectionTyre.AssetInspectionID != "" {
						currentRowTyreLayoutDataArr = append(currentRowTyreLayoutDataArr, inspectionNewScenario2LayoutData)
						totalInspectedTyres++
						totalInspectedRunningTyres++
					} else {
						blankTyreLayoutData.LayoutType = constants.INSPECTION_PRINT_TYRE_LAYOUT_TYPE_TYRE_PLACEHOLDER
						blankTyreLayoutData.TyreCount = tyreCount
						currentRowTyreLayoutDataArr = append(currentRowTyreLayoutDataArr, blankTyreLayoutData)
					}
					tyreCount++
					runningTyreCount++
				}
			} else {
				if i == 2 {
					spareTyreShowAttachmentPos = tyreCount
					currentTyre := assetInspectionTyreTemplDataMap[fmt.Sprintf("%d", tyreCount)]
					inspectionNewScenario2LayoutData := TyreLayoutData{
						LayoutType:          constants.INSPECTION_PRINT_TYRE_LAYOUT_TYPE_SPARE_TYRE,
						AssetInspectionTyre: currentTyre,
						TyreCount:           tyreCount,
					}
					if currentTyre.AssetInspectionTyre.AssetInspectionID != "" {
						currentRowTyreLayoutDataArr = append(currentRowTyreLayoutDataArr, inspectionNewScenario2LayoutData)
						totalInspectedTyres++
						totalInspectedSpareTyres++
					} else {
						blankTyreLayoutData.LayoutType = constants.INSPECTION_PRINT_TYRE_LAYOUT_TYPE_SPARE_TYRE_PLACEHOLDER
						blankTyreLayoutData.TyreCount = tyreCount
						currentRowTyreLayoutDataArr = append(currentRowTyreLayoutDataArr, blankTyreLayoutData)
					}
					tyreCount++
					spareTyreCount++
					hasSpareTyre = true
				}
			}
		}
		tyreLayoutDataArr = append(tyreLayoutDataArr, currentRowTyreLayoutDataArr)
	}

	// ODOMETER & CUSTOM VEHICLE INFO
	odometer := assetInspectionVehicle.VehicleKM
	odometerUnit := "KM"
	referenceNumber := ""
	brandModelLabel := ""

	if assetInspectionVehicle.CustomReferenceNumber.Valid {
		referenceNumber = assetInspectionVehicle.CustomReferenceNumber.String
	}
	if assetInspectionVehicle.CustomSerialNumber.Valid {
		if referenceNumber != "" {
			referenceNumber += "/"
		}
		referenceNumber += assetInspectionVehicle.CustomSerialNumber.String
	}

	if assetInspectionVehicle.VehicleKM == 0 && assetInspectionVehicle.VehicleHm > 0 {
		odometer = float64(assetInspectionVehicle.VehicleHm)
		odometerUnit = "HM"
	}
	if assetInspectionVehicle.CustomBrandName.String != "" {
		brandModelLabel += assetInspectionVehicle.CustomBrandName.String
	}
	if assetInspectionVehicle.CustomModelName.String != "" {
		if brandModelLabel != "" {
			brandModelLabel += " - "
		}
		brandModelLabel += assetInspectionVehicle.CustomModelName.String
	}

	ticketNumber := ""
	ticketID := assetInspection.ReferenceID
	if assetInspection.ReferenceCode == constants.ASSET_INSPECTION_REFERENCE_CODE_FLOW {
		flow, err := uc.formRepo.GetFlow(ctx, uc.DB.DB(), contentModel.FlowCondition{
			Where: contentModel.FlowWhere{
				ID: assetInspection.ReferenceID,
			},
		})
		if err != nil {
			return generatePDFResponse, err
		}

		ticketID = flow.ReferenceID
	}

	if ticketID != "" {
		ticket, err := uc.ticketRepo.GetTicket(ctx, uc.DB.DB(), taskModels.TicketCondition{
			Where: taskModels.TicketWhere{
				ID: ticketID,
			},
			Columns: []string{"ticket_number"},
		})
		if err == nil {
			ticketNumber = ticket.TicketNumber
		}
	}

	// ADDITIONAL INSPECTION
	showFollowupInspectionSection := false
	failedVisualChecking := false
	tireTreadAndRimDamage := false
	requireRotationTyre := false
	requireSpooringVehicle := false
	if assetInspectionVehicle.FailedVisualChecking.Valid && assetInspectionVehicle.FailedVisualChecking.Bool {
		failedVisualChecking = true
	}
	if assetInspectionVehicle.TireTreadAndRimDamage.Valid && assetInspectionVehicle.TireTreadAndRimDamage.Bool {
		tireTreadAndRimDamage = true
	}
	if assetInspectionVehicle.RequireRotationTyre.Valid && assetInspectionVehicle.RequireRotationTyre.Bool {
		requireRotationTyre = true
	}
	if assetInspectionVehicle.RequireSpooringVehicle.Valid && assetInspectionVehicle.RequireSpooringVehicle.Bool {
		requireSpooringVehicle = true
	}
	if failedVisualChecking || tireTreadAndRimDamage || requireRotationTyre || requireSpooringVehicle {
		showFollowupInspectionSection = true
	}

	type PageData struct {
		AxleConfigurationItems         []template.HTML
		AxleConfigurationZoom          float64
		AssetInspection                *models.AssetInspection
		AssetInspectionVehicle         models.AssetInspectionVehicle
		Asset                          models.Asset
		TicketNumber                   string
		TyreLayoutDataArr              [][]TyreLayoutData
		ClientPhoto                    string
		SecondaryClientPhoto           string
		Odometer                       float64
		OdometerUnit                   string
		ReferenceNumber                string
		BrandModelLabel                string
		CheckImg                       string
		CrossImg                       string
		PressureEnabledImg             string
		PressureDisabledImg            string
		TyreIllustrationPlaceholderImg string
		VehicleAttachments             []storageDtos.GetAttachmentsResp
		HasSpareTyre                   bool
		SpareTyreShowAttachmentPos     int
		FailedVisualChecking           bool
		TireTreadAndRimDamage          bool
		RequireRotationTyre            bool
		RequireSpooringVehicle         bool
		ShowFollowupInspectionSection  bool
		TyreWavePlaceholderSvg         dtos.TyreWaveSvgResponse
	}
	templateData := PageData{
		AxleConfigurationItems:         axleConfigurationItems,
		AxleConfigurationZoom:          axleConfigurationZoom,
		AssetInspection:                assetInspection,
		AssetInspectionVehicle:         *assetInspectionVehicle,
		TicketNumber:                   ticketNumber,
		Asset:                          assetInspectionVehicle.AssetVehicle.Asset,
		TyreLayoutDataArr:              tyreLayoutDataArr,
		ClientPhoto:                    clientPhoto,
		SecondaryClientPhoto:           secondaryClientPhoto,
		Odometer:                       odometer,
		OdometerUnit:                   odometerUnit,
		ReferenceNumber:                referenceNumber,
		BrandModelLabel:                brandModelLabel,
		CheckImg:                       checkImgPath,
		CrossImg:                       crossImgPath,
		PressureEnabledImg:             pressureEnabledImgPath,
		PressureDisabledImg:            pressureDisabledImgPath,
		TyreIllustrationPlaceholderImg: tyreIllustrationPlaceholderImgPath,
		VehicleAttachments:             vehicleAttachments,
		HasSpareTyre:                   hasSpareTyre,
		SpareTyreShowAttachmentPos:     spareTyreShowAttachmentPos,
		FailedVisualChecking:           failedVisualChecking,
		TireTreadAndRimDamage:          tireTreadAndRimDamage,
		RequireRotationTyre:            requireRotationTyre,
		RequireSpooringVehicle:         requireSpooringVehicle,
		ShowFollowupInspectionSection:  showFollowupInspectionSection,
		TyreWavePlaceholderSvg:         tyreWavePlaceholderSvg,
	}
	templateHTML := template.Must(template.ParseFiles("./statics/print-template/digispect_inspection_full.html"))
	var templateBuff bytes.Buffer
	err = templateHTML.Execute(&templateBuff, &templateData)
	if err != nil {
		return generatePDFResponse, err
	}

	// GENERATE PDF
	newPage := wkhtmltopdf.NewPageReader(bytes.NewBuffer(templateBuff.Bytes()))
	newPage.EnableLocalFileAccess.Set(true)

	// START OF FOOTER
	footerInpectedBy := "Diinspeksi oleh -"
	if inspectionUserFullName != "" {
		footerInpectedBy = "Diinspeksi oleh " + inspectionUserFullName
	}
	footerTotalInpectedTyre := fmt.Sprintf(" • %d Ban Diinspeksi", totalInspectedTyres)
	footerDeviceID := ""
	if deviceId != "" {
		footerDeviceID = " • ID Perangkat " + deviceId
	}
	footerLocation := ""
	if assetInspection.LocationLabel != "" {
		footerLocation = " • " + assetInspection.LocationLabel
	}
	footerNote := footerInpectedBy + footerTotalInpectedTyre + footerDeviceID + footerLocation
	newPage.FooterLeft.Set(footerNote)
	newPage.FooterRight.Set("Halaman [page]/[toPage]")
	newPage.FooterFontName.Set("Poppins")
	newPage.FooterFontSize.Set(6)
	// ASSETFINDR WEB FOOTER
	footerHTML := filepath.Join(baseDir, "statics/print-template/digispect_inspection_footer_watermark.html")
	footerPageURL := "file:///" + filepath.ToSlash(footerHTML)
	newPage.FooterHTML.Set(footerPageURL)
	// END OF FOOTER

	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		commonlogger.Errorf("Error in initializing pdf generator", err.Error())
		return generatePDFResponse, err
	}
	pdfg.Dpi.Set(768)
	pdfg.Orientation.Set(wkhtmltopdf.OrientationPortrait)
	pdfg.AddPage(newPage)
	pdfg.MarginBottom.Set(8)

	err = pdfg.Create()
	if err != nil {
		commonlogger.Errorf("Error in creating pdf", err.Error())
		return generatePDFResponse, err
	}

	pdfgBuffer := pdfg.Buffer()
	signedUrl, pdfHeader, err := uc.generateAssetInspectionExportPDFSignedUrl(ctx, pdfgBuffer)
	if err != nil {
		commonlogger.Errorf("Error in generating exported pdf download url", err)
		return generatePDFResponse, err
	}

	generatePDFResponse = commonmodel.DetailResponse{
		Success:     true,
		Message:     "asset tyre inspection detail is successfully exported",
		ReferenceID: signedUrl,
		Data:        pdfHeader,
	}

	return generatePDFResponse, nil
}

func (uc *AssetInspectionUseCase) PrintWorkshopInspectionHTML(ctx context.Context, id string, sections []string) (commonmodel.DetailResponse, error) {
	var printHTMLResponse commonmodel.DetailResponse
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return printHTMLResponse, err
	}

	includeVehicleSection := false
	includeTyreSection := false
	for _, section := range sections {
		switch strings.ToUpper(section) {
		case constants.WORKSHOP_PRINT_SECTION_VEHICLE:
			includeVehicleSection = true
		case constants.WORKSHOP_PRINT_SECTION_TYRE:
			includeTyreSection = true
		}
	}

	// IMPORT IMAGES
	baseDir, err := os.Getwd()
	if err != nil {
		commonlogger.Errorf("Error in fetching base dir", err.Error())
		return printHTMLResponse, err
	}
	// ASSETFINDR LOGO
	assetfindrLogoImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/assetfindr-logo.png")
	if err != nil {
		return printHTMLResponse, err
	}
	// CHECK MARK
	checkImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/check.svg")
	if err != nil {
		return printHTMLResponse, err
	}
	// CROSS MARK
	crossImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/cross.svg")
	if err != nil {
		return printHTMLResponse, err
	}
	// PRESSURE ENABLED IMAGE
	pressureEnabledImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/pressure-enabled.svg")
	if err != nil {
		return printHTMLResponse, err
	}
	// PRESSURE DISABLED IMAGE
	pressureDisabledImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/pressure-disabled.svg")
	if err != nil {
		return printHTMLResponse, err
	}
	// TYRE ILLUSTRATION PLACEHOLDER
	tyreIllustrationPlaceholderImgPath, err := uc.generateHTMLTemplateImagePath(baseDir, "statics/print-template/images/tyre-illustration-placeholder.svg")
	if err != nil {
		return printHTMLResponse, err
	}

	// FETCH CLIENT DATA
	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
		Preload: userIdentityModel.ClientPreload{
			Status: true,
		},
	})
	if err != nil {
		return printHTMLResponse, err
	}
	clientPhoto := assetfindrLogoImgPath
	if client.Photo.String != "" {
		clientPhoto, _ = helpers.GenerateCloudStorageSignedURL(client.Photo.String, time.Duration(24))
	}
	secondaryClientPhoto := ""
	if client.SecondaryPhoto.String != "" {
		secondaryClientPhoto, _ = helpers.GenerateCloudStorageSignedURL(client.SecondaryPhoto.String, time.Duration(24))
	}

	exportScenario := 5

	// FETCH FORMS
	formDtos := contentDtos.PrintHTMLForm{}
	if includeVehicleSection {
		form, err := uc.formRepo.GetForm(ctx, uc.DB.DB(), contentModel.FormCondition{
			Where: contentModel.FormWhere{
				ReferenceID: id,
				ClientID:    claim.GetLoggedInClientID(),
			},
			Preload: contentModel.FormPreload{
				FormFields: true,
			},
		})
		if err == nil {
			formDtos = contentDtos.PrintHTMLForm{
				ID:               form.ID,
				ReferenceID:      form.ReferenceID,
				FormCategoryCode: form.FormCategoryCode,
				FormFields:       make([]contentDtos.PrintHTMLFormField, 0, len(form.FormFields)),
				CreatedAt:        form.CreatedAt,
				StatusCode:       form.StatusCode,
			}
			for _, field := range form.FormFields {
				pgJSONBOptions := pgtypehelpers.HandleValue(field.Options)
				pgJSONBValue := pgtypehelpers.HandleValue(field.Value)
				FormFieldOptions := contentDtos.PrintHTMLFormFieldOptOrValueData{}
				FormFieldValue := contentDtos.PrintHTMLFormFieldOptOrValueData{}

				formFieldOptionJsonBytes, _ := pgJSONBOptions.MarshalJSON()
				_ = json.Unmarshal(formFieldOptionJsonBytes, &FormFieldOptions.Data)
				formFieldValueJsonBytes, _ := pgJSONBValue.MarshalJSON()
				_ = json.Unmarshal(formFieldValueJsonBytes, &FormFieldValue.Data)

				formDtos.FormFields = append(formDtos.FormFields, contentDtos.PrintHTMLFormField{
					ID:                field.ID,
					FormFieldTypeCode: field.FormFieldTypeCode,
					Options:           FormFieldOptions,
					Sequence:          field.Sequence,
					Label:             field.Label,
					Value:             FormFieldValue,
					IsMandatory:       field.IsMandatory,
				})
			}
		}
	}

	// HIDE VEHICLE SECTION IF NO FORM FIELDS AVAILABLE
	if len(formDtos.FormFields) == 0 {
		includeVehicleSection = false
	}

	// FETCH INSPECTION
	assetInspection, err := uc.AssetInspectionRepository.GetAssetInspection(
		ctx,
		uc.DB.DB(),
		models.AssetInspectionCondition{
			Where: models.AssetInspectionWhere{
				ID:       id,
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: models.AssetInspectionPreload{},
		},
	)
	if err != nil {
		return printHTMLResponse, err
	}
	inspectionAssignmentID := assetInspection.InspectByUserID
	inspectionUser := userIdentityModel.User{}
	var inspectionUserFullName string
	_ = uc.UserRepository.GetUserById(ctx, uc.DB.DB(), &inspectionUser, inspectionAssignmentID)
	if inspectionUser.FirstName != "" {
		inspectionUserFullName = inspectionUser.FirstName + " " + inspectionUser.LastName
	}

	// FETCH TYRE INSPECTION
	_, assetInspectionTyres, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyreList(
		ctx,
		uc.DB.DB(),
		models.GetAssetInspectionTyreListParam{
			ListRequest: commonmodel.ListRequest{
				PageSize: 100,
				PageNo:   1,
			},
			Cond: models.AssetInspectionTyreCondition{
				Where: models.AssetInspectionTyreWhere{
					ClientID:     claim.GetLoggedInClientID(),
					InspectionID: assetInspection.ID,
				},
				Preload: models.AssetInspectionTyrePreload{
					AssetInspection: true,
					AssetTyre:       true,
					AssetTyreAsset:  true,
				},
			},
		},
	)
	if err != nil {
		return printHTMLResponse, err
	}

	ticketNumber := ""
	ticketID := assetInspection.ReferenceID
	if assetInspection.ReferenceCode == constants.ASSET_INSPECTION_REFERENCE_CODE_FLOW {
		flow, err := uc.formRepo.GetFlow(ctx, uc.DB.DB(), contentModel.FlowCondition{
			Where: contentModel.FlowWhere{
				ID: assetInspection.ReferenceID,
			},
		})
		if err != nil {
			return printHTMLResponse, err
		}

		ticketID = flow.ReferenceID
	}

	var ticketAsset *models.Asset
	if ticketID != "" {
		ticket, err := uc.ticketRepo.GetTicket(ctx, uc.DB.DB(), taskModels.TicketCondition{
			Where: taskModels.TicketWhere{
				ID:       ticketID,
				ClientID: claim.GetLoggedInClientID(),
			},
			Columns: []string{"id", "ticket_number", "reference_id"},
		})
		if err == nil {
			ticketNumber = ticket.TicketNumber

			ticketAsset, err = uc.AssetRepository.GetAsset(ctx, uc.DB.DB(), models.AssetCondition{
				Where: models.AssetWhere{
					ID:       ticket.ReferenceID,
					ClientID: claim.GetLoggedInClientID(),
				},
				Preload: models.AssetPreload{
					Brand:      true,
					AssetModel: true,
				},
			})
			if err != nil {
				return printHTMLResponse, err
			}
		}
	}

	// TYRE AND VEHICLE INSPECTION
	var assetInspectionVehicle *models.AssetInspectionVehicle
	var assetInspectionVehicleData models.AssetInspectionVehicle
	assetTyresMapById := map[string]models.AssetTyre{}
	var assetTyreIds []string
	var assetInspectionIds []string
	totalInspectedTyres := 0
	var asset models.Asset

	// GENERATE PLACEHOLDER TYRE WAVE
	tyreWavePlaceholderSource := dtos.TyreWaveSource{
		NoOfInspectionPoint: 4,
		RDT1:                0,
		RDT2:                0,
		RDT3:                0,
		RDT4:                0,
		OTD:                 0,
		ShowOTD:             false,
	}
	tyreWavePlaceholderSvg := uc.AssetInspectionUtil.GenerateTyreWaveSvg(tyreWavePlaceholderSource)

	// AXLE
	var axleConfigurationItems []template.HTML
	axleConfigurationZoom := 1.0

	// STRUCTS
	type AssetInspectionTyreTempl struct {
		AssetInspectionTyre models.AssetInspectionTyre
		AssetTyre           models.AssetTyre
		Attachments         []storageDtos.GetAttachmentsResp
		AverageRTD          float64
		TUR                 float64
		RemainingTUR        float64
		TyreBaseSvg         template.HTML
		SerialNo            string
		BrandName           string
		TyreSize            string
		Remark              string
		TreadPoint          int
		Rfid                string
		TyreWaveSvg         dtos.TyreWaveSvgResponse
	}
	type PrintLayoutData struct {
		LayoutType          string // blank space, inspection section, axle configuration
		AssetInspectionTyre AssetInspectionTyreTempl
		TyreCount           int
	}

	// ODOMETER
	var odometer float64
	odometerUnit := "KM"
	referenceNumber := ""
	brandModelLabel := ""

	// TYRE LAYOUT
	var printLayoutDataArr [][]PrintLayoutData

	// FOLLOW UP INSPECTION
	showFollowupInspectionSection := false
	failedVisualChecking := false
	tireTreadAndRimDamage := false
	requireRotationTyre := false
	requireSpooringVehicle := false

	// FOOTER
	signatureTime := time.Now().Format("02 Jan 2006 03:04:05")
	footerNote := ""

	// ASSET INSPECTION VEHICLE
	assetInspectionVehicle, err = uc.AssetInspectionVehicleRepository.GetAssetInspectionVehicle(ctx, uc.DB.DB(), models.AssetInspectionVehicleCondition{
		Where: models.AssetInspectionVehicleWhere{
			ClientID:     claim.GetLoggedInClientID(),
			InspectionID: assetInspection.ID,
		},
		Preload: models.AssetInspectionVehiclePreload{
			Asset:                    true,
			AssetBrand:               true,
			AssetVehicle:             true,
			AssetVehicleAsset:        true,
			AssetVehicleVehicle:      true,
			AssetVehicleVehicleBrand: true,
			AssetLocation:            true,
		},
	})
	if err != nil && err.Error() != "INSPECTION_VEHICLE_NOT_FOUND" {
		return printHTMLResponse, err
	}

	if assetInspectionVehicle != nil {
		assetInspectionVehicleData = *assetInspectionVehicle
		asset = assetInspectionVehicle.AssetVehicle.Asset
	}

	if includeTyreSection {
		// FETCH AND MAP ASSET TYRE BY ID
		for _, inspectionTyre := range assetInspectionTyres {
			assetTyreIds = append(assetTyreIds, inspectionTyre.AssetTyreID)
			assetInspectionIds = append(assetInspectionIds, inspectionTyre.ID)
		}
		assetTyres := []models.AssetTyre{}
		err = uc.AssetTyreRepository.GetAssetTyresByIds(ctx, uc.DB.DB(), &assetTyres, assetTyreIds)
		if err != nil {
			commonlogger.Errorf("Error in getting asset tyres by asset tyre ids from asset tyre service", err)
			return printHTMLResponse, err
		}
		for _, assetTyre := range assetTyres {
			assetTyresMapById[assetTyre.AssetID] = assetTyre
		}

		// FETCH ATTACHMENTS
		attachmentsMapByInspectionID := make(map[string][]storageDtos.GetAttachmentsResp, len(assetInspectionIds))
		if len(assetInspectionIds) > 0 {
			attachments, err := uc.AttachmentUseCase.GetAttachments(ctx, storageDtos.GetAttachmentsReq{
				ReferenceCode:      "ASSET_INSPECTION",
				SourceReferenceIDs: assetInspectionIds,
			})
			if err != nil {
				return printHTMLResponse, err
			}
			for _, attachment := range attachments {
				attachmentsMapByInspectionID[attachment.SourceReferenceID] = append(attachmentsMapByInspectionID[attachment.SourceReferenceID], attachment)
			}
		}

		// REF AXLE CONFIG
		var refAxleConfiguration pgtype.JSONB
		if assetInspectionVehicle.AssetVehicle.AxleConfiguration.Status == pgtype.Present {
			refAxleConfiguration = assetInspectionVehicle.AssetVehicle.AxleConfiguration
		} else {
			refAxleConfiguration = assetInspectionVehicle.AxleConfiguration
		}

		// CONSTRUCT AXLE CONFIGURATIONS
		type axleConfiguration struct {
			value       string
			numOfTyres  int
			isSpareTyre bool
			name        string
			width       int
			height      int
		}
		axleConfigurations := map[string]axleConfiguration{
			"TWO_TYRES_WITH_STEERING_WHEEL": {
				value:       "TWO_TYRES_WITH_STEERING_WHEEL",
				numOfTyres:  2,
				isSpareTyre: false,
				name:        "axle.svg",
				width:       115,
				height:      65,
			},
			"TWO_TYRES": {
				value:       "TWO_TYRES",
				numOfTyres:  2,
				isSpareTyre: false,
				name:        "axle1.svg",
				width:       115,
				height:      65,
			},
			"TWO_TYRES_WITH_SPINDLE": {
				value:       "TWO_TYRES_WITH_SPINDLE",
				numOfTyres:  2,
				isSpareTyre: false,
				name:        "axle2.svg",
				width:       115,
				height:      65,
			},
			"FOUR_TYRES": {
				value:       "FOUR_TYRES",
				numOfTyres:  4,
				isSpareTyre: false,
				name:        "axle3.svg",
				width:       180,
				height:      65,
			},
			"FOUR_TYRES_WITH_SPINDLE": {
				value:       "FOUR_TYRES_WITH_SPINDLE",
				numOfTyres:  4,
				isSpareTyre: false,
				name:        "axle4.svg",
				width:       180,
				height:      65,
			},
			"ONE_SPARE_TYRE": {
				value:       "ONE_SPARE_TYRE",
				numOfTyres:  1,
				isSpareTyre: true,
				name:        "axle5.svg",
				width:       150,
				height:      40,
			},
		}

		remainingTyreDetailSectionCount := (len(assetInspectionTyres) + 1) % 4
		var remainingTyreDetailSectionNumbers []int
		if remainingTyreDetailSectionCount > 0 {
			remainingTyreDetailSectionCount = 4 - remainingTyreDetailSectionCount
		}
		for i := 0; i < remainingTyreDetailSectionCount; i += 1 {
			remainingTyreDetailSectionNumbers = append(remainingTyreDetailSectionNumbers, i)
		}

		// USE HTML TEMPLATING TO APPEND DATA
		var assetInspectionTyreTemplData []AssetInspectionTyreTempl
		assetInspectionTyreTemplDataMap := map[string]AssetInspectionTyreTempl{}
		deviceId := ""
		var enabledTyrePositions []int
		for _, inspection := range assetInspectionTyres {
			if deviceId == "" {
				deviceId = inspection.DeviceID
			}

			remark := "-"
			if inspection.Remark != "" {
				remark = helpers.TruncateEnd(inspection.Remark, 48)
			}

			brandName := "-"
			tyreSize := "-"
			serialNo := "-"
			rfid := "-"
			if inspection.AssetTyreID != "" {
				serialNo = helpers.TruncateMiddle(inspection.AssetTyre.Asset.SerialNumber, 11)
				if assetTyresMapById[inspection.AssetTyreID].Asset.Brand.BrandName != "" {
					brandName = assetTyresMapById[inspection.AssetTyreID].Asset.Brand.BrandName
				}
				tyreSize = assetTyresMapById[inspection.AssetTyreID].Tyre.SectionWidth + " " + assetTyresMapById[inspection.AssetTyreID].Tyre.Construction + " " + assetTyresMapById[inspection.AssetTyreID].Tyre.RimDiameter
				rfid = inspection.AssetTyre.Asset.Rfid
			}

			if inspection.CustomSerialNumber != "" && serialNo == "-" {
				serialNo = helpers.TruncateMiddle(inspection.CustomSerialNumber, 11)
			}
			if inspection.CustomBrandName.String != "" && brandName == "-" {
				brandName = inspection.CustomBrandName.String
			}
			if inspection.CustomTyreSize != "" && tyreSize == "-" {
				tyreSize = inspection.CustomTyreSize
			}
			if inspection.CustomRFID != "" && rfid == "-" {
				rfid = inspection.CustomRFID
			}

			if rfid == "-" {
				rfid = ""
			}

			withTyreOptimax := true
			if exportScenario == 4 || exportScenario == 5 {
				withTyreOptimax = false
			}
			withOTD := false

			treadPoint := inspection.NumberOfInspectionPoint
			if treadPoint == 0 || treadPoint > 5 {
				treadPoint = 4
			}
			RDT1 := inspection.RDT1
			RDT2 := inspection.RDT2
			RDT3 := inspection.RDT3
			RDT4 := inspection.RDT4
			averageRTD := helpers.CalculateAverageRTD(RDT1, RDT2, RDT3, RDT4)
			averageRTD = helpers.TruncateFloat(averageRTD, 1)
			var OTD float64 = 0
			if inspection.AssetTyreID != "" {
				OTD = assetTyresMapById[inspection.AssetTyreID].StartThreadDepth
				withOTD = true
			} else {
				OTD = RDT1
				if RDT2 > OTD {
					OTD = RDT2
				}
				if RDT3 > OTD {
					OTD = RDT3
				}
				if RDT4 > OTD {
					OTD = RDT4
				}
			}

			TUR := helpers.CalculateTyreUtilRate(OTD, averageRTD)

			tyreBaseSvg := uc.AssetInspectionUtil.GenerateTyreBaseSvg(RDT1, RDT2, RDT3, RDT4, OTD, TUR, 0.44, withTyreOptimax, true, treadPoint, withOTD)

			tyreWaveSource := dtos.TyreWaveSource{
				NoOfInspectionPoint: treadPoint,
				RDT1:                RDT1,
				RDT2:                RDT2,
				RDT3:                RDT3,
				RDT4:                RDT4,
				OTD:                 OTD,
				ShowOTD:             withOTD,
			}
			tyreWaveSvg := uc.AssetInspectionUtil.GenerateTyreWaveSvg(tyreWaveSource)

			currentTemplateData := AssetInspectionTyreTempl{
				AssetInspectionTyre: inspection,
				AssetTyre:           assetTyresMapById[inspection.AssetTyreID],
				Attachments:         attachmentsMapByInspectionID[inspection.ID],
				AverageRTD:          averageRTD,
				TUR:                 TUR,
				RemainingTUR:        100 - TUR,
				TyreBaseSvg:         tyreBaseSvg,
				SerialNo:            serialNo,
				BrandName:           brandName,
				TyreSize:            tyreSize,
				Remark:              remark,
				TreadPoint:          treadPoint,
				Rfid:                rfid,
				TyreWaveSvg:         tyreWaveSvg,
			}
			assetInspectionTyreTemplData = append(assetInspectionTyreTemplData, currentTemplateData)
			assetInspectionTyreTemplDataMap[fmt.Sprintf("%.0f", inspection.TyrePosition)] = currentTemplateData
			enabledTyrePositions = append(enabledTyrePositions, int(inspection.TyrePosition))
		}

		// GENERATE AXLE CONFIGURATIONS SVG
		tyreNumber := 1

		rAxleConfiguration := []models.AxleConfiguration{}
		err = refAxleConfiguration.AssignTo(&rAxleConfiguration)
		if err != nil {
			return printHTMLResponse, err
		}
		for _, assetAxleConfiguration := range rAxleConfiguration {
			axleConfigurationSvg, tyreNumberRes := uc.AssetInspectionUtil.GenerateAxleConfigurationSvg(assetAxleConfiguration.Axle, tyreNumber, 0.62, enabledTyrePositions)
			tyreNumber = tyreNumberRes

			axleConfigurationItems = append(axleConfigurationItems, axleConfigurationSvg)
		}

		if len(axleConfigurationItems) >= 5 {
			axleConfigurationZoom = uc.AssetInspectionUtil.CalculateAxleConfigurationZoom(len(axleConfigurationItems))
		}

		// NEW LOGIC
		blankPrintLayoutData := PrintLayoutData{
			LayoutType:          "BLANK_SPACE",
			AssetInspectionTyre: AssetInspectionTyreTempl{},
			TyreCount:           0,
		}
		axleConfigPrintLayoutData := PrintLayoutData{
			LayoutType:          "AXLE_CONFIGURATION",
			AssetInspectionTyre: AssetInspectionTyreTempl{},
			TyreCount:           0,
		}
		tyreCount := 1
		runningTyreCount := 0
		spareTyreCount := 0
		totalInspectedRunningTyres := 0
		totalInspectedSpareTyres := 0

		rAxleConfiguration = []models.AxleConfiguration{}
		err = refAxleConfiguration.AssignTo(&rAxleConfiguration)
		if err != nil {
			return printHTMLResponse, err
		}
		for idx, assetAxleConfiguration := range rAxleConfiguration {
			currentConfig := axleConfigurations[assetAxleConfiguration.Axle]
			var currentRowPrintLayoutDataArr []PrintLayoutData
			for i := 0; i < 5; i++ {
				if currentConfig.numOfTyres == 2 {
					if idx == 0 && i == 2 {
						currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, axleConfigPrintLayoutData)
					} else if idx != 0 && i == 2 {
						blankPrintLayoutData.LayoutType = "AXLE_SPACER"
						currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, blankPrintLayoutData)
					} else if i == 0 || i == 4 {
						blankPrintLayoutData.LayoutType = "AXLE_SIDE_SPACER"
						currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, blankPrintLayoutData)
					} else {
						currentTyre := assetInspectionTyreTemplDataMap[fmt.Sprintf("%d", tyreCount)]
						inspectionNewScenario2LayoutData := PrintLayoutData{
							LayoutType:          "TYRE",
							AssetInspectionTyre: currentTyre,
							TyreCount:           tyreCount,
						}
						if currentTyre.AssetInspectionTyre.AssetInspectionID != "" {
							currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, inspectionNewScenario2LayoutData)
							totalInspectedTyres++
							totalInspectedRunningTyres++
						} else {
							blankPrintLayoutData.LayoutType = "TYRE_PLACEHOLDER"
							blankPrintLayoutData.TyreCount = tyreCount
							currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, blankPrintLayoutData)
						}
						tyreCount++
						runningTyreCount++
					}
				} else if currentConfig.numOfTyres == 4 {
					if i == 2 {
						blankPrintLayoutData.LayoutType = "CARD_SPACER"
						currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, blankPrintLayoutData)
					} else {
						currentTyre := assetInspectionTyreTemplDataMap[fmt.Sprintf("%d", tyreCount)]
						inspectionNewScenario2LayoutData := PrintLayoutData{
							LayoutType:          "TYRE",
							AssetInspectionTyre: currentTyre,
							TyreCount:           tyreCount,
						}
						if currentTyre.AssetInspectionTyre.AssetInspectionID != "" {
							currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, inspectionNewScenario2LayoutData)
							totalInspectedTyres++
							totalInspectedRunningTyres++
						} else {
							blankPrintLayoutData.LayoutType = "TYRE_PLACEHOLDER"
							blankPrintLayoutData.TyreCount = tyreCount
							currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, blankPrintLayoutData)
						}
						tyreCount++
						runningTyreCount++
					}
				} else {
					if i == 2 {
						currentTyre := assetInspectionTyreTemplDataMap[fmt.Sprintf("%d", tyreCount)]
						inspectionNewScenario2LayoutData := PrintLayoutData{
							LayoutType:          "SPARE_TYRE",
							AssetInspectionTyre: currentTyre,
							TyreCount:           tyreCount,
						}
						if currentTyre.AssetInspectionTyre.AssetInspectionID != "" {
							currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, inspectionNewScenario2LayoutData)
							totalInspectedTyres++
							totalInspectedSpareTyres++
						} else {
							blankPrintLayoutData.LayoutType = "SPARE_TYRE_PLACEHOLDER"
							blankPrintLayoutData.TyreCount = tyreCount
							currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, blankPrintLayoutData)
						}
						tyreCount++
						spareTyreCount++
					}
				}
			}
			printLayoutDataArr = append(printLayoutDataArr, currentRowPrintLayoutDataArr)
		}

		// ADDITIONAL INSPECTION
		if assetInspectionVehicle.FailedVisualChecking.Valid && assetInspectionVehicle.FailedVisualChecking.Bool {
			failedVisualChecking = true
		}
		if assetInspectionVehicle.TireTreadAndRimDamage.Valid && assetInspectionVehicle.TireTreadAndRimDamage.Bool {
			tireTreadAndRimDamage = true
		}
		if assetInspectionVehicle.RequireRotationTyre.Valid && assetInspectionVehicle.RequireRotationTyre.Bool {
			requireRotationTyre = true
		}
		if assetInspectionVehicle.RequireSpooringVehicle.Valid && assetInspectionVehicle.RequireSpooringVehicle.Bool {
			requireSpooringVehicle = true
		}
		if failedVisualChecking || tireTreadAndRimDamage || requireRotationTyre || requireSpooringVehicle {
			showFollowupInspectionSection = true
		}

		if totalInspectedTyres > 0 {
			footerInpectedBy := "Diinspeksi oleh -"
			if inspectionUserFullName != "" {
				footerInpectedBy = "Diinspeksi oleh " + inspectionUserFullName
			}
			footerTotalInpectedTyre := fmt.Sprintf(" • %d Ban Diinspeksi", totalInspectedTyres)
			footerDeviceID := ""
			if deviceId != "" {
				footerDeviceID = " • ID Perangkat " + deviceId
			}
			footerLocation := ""
			if assetInspection.LocationLabel != "" {
				footerLocation = " • " + assetInspection.LocationLabel
			}
			footerNote = footerInpectedBy + footerTotalInpectedTyre + footerDeviceID + footerLocation
		}
	}

	// ODOMETER & CUSTOM VEHICLE INFO
	if assetInspectionVehicle != nil {
		odometer = assetInspectionVehicle.VehicleKM
		odometerUnit = "KM"
		referenceNumber = ""
		brandModelLabel = ""

		if assetInspectionVehicle.CustomReferenceNumber.Valid {
			referenceNumber = assetInspectionVehicle.CustomReferenceNumber.String
		}
		if assetInspectionVehicle.CustomSerialNumber.Valid {
			if referenceNumber != "" {
				referenceNumber += "/"
			}
			referenceNumber += assetInspectionVehicle.CustomSerialNumber.String
		}

		if assetInspectionVehicle.VehicleKM == 0 && assetInspectionVehicle.VehicleHm > 0 {
			odometer = float64(assetInspectionVehicle.VehicleHm)
			odometerUnit = "HM"
		}
		if assetInspectionVehicle.CustomBrandName.String != "" {
			brandModelLabel += assetInspectionVehicle.CustomBrandName.String
		}
		if assetInspectionVehicle.CustomModelName.String != "" {
			if brandModelLabel != "" {
				brandModelLabel += " - "
			}
			brandModelLabel += assetInspectionVehicle.CustomModelName.String
		}
	} else {
		referenceNumber = ""
		if ticketAsset.ReferenceNumber != "" {
			referenceNumber += ticketAsset.ReferenceNumber
		}
		if ticketAsset.SerialNumber != "" {
			if ticketAsset.ReferenceNumber != "" {
				referenceNumber += "/"
			}

			referenceNumber += ticketAsset.SerialNumber
		}

		brandModelLabel = ""
		if ticketAsset.Brand.BrandName != "" {
			brandModelLabel += ticketAsset.Brand.BrandName
		}
		if ticketAsset.AssetModel.AssetModelName != "" {
			if brandModelLabel != "" {
				brandModelLabel += " - "
			}
			brandModelLabel += ticketAsset.AssetModel.AssetModelName
		}
	}

	type PageData struct {
		TicketAsset                    models.Asset
		AxleConfigurationItems         []template.HTML
		AxleConfigurationZoom          float64
		AssetInspection                *models.AssetInspection
		AssetInspectionVehicle         models.AssetInspectionVehicle
		Asset                          models.Asset
		TicketNumber                   string
		PrintLayoutDataArr             [][]PrintLayoutData
		ClientPhoto                    string
		SecondaryClientPhoto           string
		Odometer                       float64
		OdometerUnit                   string
		ReferenceNumber                string
		BrandModelLabel                string
		CheckImg                       string
		CrossImg                       string
		PressureEnabledImg             string
		PressureDisabledImg            string
		TyreIllustrationPlaceholderImg string
		FailedVisualChecking           bool
		TireTreadAndRimDamage          bool
		RequireRotationTyre            bool
		RequireSpooringVehicle         bool
		ShowFollowupInspectionSection  bool
		TyreWavePlaceholderSvg         dtos.TyreWaveSvgResponse
		Form                           contentDtos.PrintHTMLForm
		SignatureTime                  string
		FooterNote                     string
		IncludeVehicleSection          bool
		IncludeTyreSection             bool
		TotalInspectedTyres            int
	}
	templateData := PageData{
		TicketAsset:                    *ticketAsset,
		AxleConfigurationItems:         axleConfigurationItems,
		AxleConfigurationZoom:          axleConfigurationZoom,
		AssetInspection:                assetInspection,
		AssetInspectionVehicle:         assetInspectionVehicleData,
		Asset:                          asset,
		TicketNumber:                   ticketNumber,
		PrintLayoutDataArr:             printLayoutDataArr,
		ClientPhoto:                    clientPhoto,
		SecondaryClientPhoto:           secondaryClientPhoto,
		Odometer:                       odometer,
		OdometerUnit:                   odometerUnit,
		ReferenceNumber:                referenceNumber,
		BrandModelLabel:                brandModelLabel,
		CheckImg:                       checkImgPath,
		CrossImg:                       crossImgPath,
		PressureEnabledImg:             pressureEnabledImgPath,
		PressureDisabledImg:            pressureDisabledImgPath,
		TyreIllustrationPlaceholderImg: tyreIllustrationPlaceholderImgPath,
		FailedVisualChecking:           failedVisualChecking,
		TireTreadAndRimDamage:          tireTreadAndRimDamage,
		RequireRotationTyre:            requireRotationTyre,
		RequireSpooringVehicle:         requireSpooringVehicle,
		ShowFollowupInspectionSection:  showFollowupInspectionSection,
		TyreWavePlaceholderSvg:         tyreWavePlaceholderSvg,
		Form:                           formDtos,
		SignatureTime:                  signatureTime,
		FooterNote:                     footerNote,
		IncludeVehicleSection:          includeVehicleSection,
		IncludeTyreSection:             includeTyreSection,
		TotalInspectedTyres:            totalInspectedTyres,
	}
	templateHTML := template.Must(template.ParseFiles("./statics/print-template/workshop_inspection_print_layout.html"))
	var templateBuff bytes.Buffer
	err = templateHTML.Execute(&templateBuff, &templateData)
	if err != nil {
		return printHTMLResponse, err
	}

	templateHTMLString := templateBuff.String()

	printHTMLResponse = commonmodel.DetailResponse{
		Success:     true,
		Message:     "Workshop inspection HTML is successfully printed",
		ReferenceID: id,
		Data:        templateHTMLString,
	}

	return printHTMLResponse, nil
}
