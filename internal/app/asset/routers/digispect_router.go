package routers

import (
	"assetfindr/internal/app/asset/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterDigispectRoutes(route *gin.Engine, DigispectHandler *handler.DigispectHandler) *gin.Engine {

	DigispectRoutes := route.Group("/v1/digispect", middleware.TokenValidationMiddleware())
	{
		DigispectRoutes.GET("/config", DigispectHandler.GetDigispectConfigList)
		DigispectRoutes.POST("/config", DigispectHandler.CreateDigispectConfig)
		DigispectRoutes.PUT("/config/:id", DigispectHandler.UpdateDigispectConfig)

		DigispectRoutes.GET("/brand", DigispectHandler.GetDigispectBrandConfigList)
		DigispectRoutes.POST("/brand", DigispectHandler.CreateDigispectBrand)
		DigispectRoutes.PUT("/brand/:id", DigispectHandler.UpdateDigispectBrandsConfig)

	}
	digispectPublicRoutes := route.Group("/v1/digispect")
	{
		digispectPublicRoutes.GET("/packages", DigispectHandler.GetDigispectPackages)
	}

	digispectVehicleRoutes := route.Group("/v1/digispect/vehicles", middleware.TokenValidationMiddleware())
	{
		digispectVehicleRoutes.POST("", DigispectHandler.CreateDigispectVehicle)
		digispectVehicleRoutes.PUT("/:id", DigispectHandler.UpdateDigispectVehicle)
		digispectVehicleRoutes.PUT("/:id/axle", DigispectHandler.UpdateDigispectVehicleAxleConfiguration)
		digispectVehicleRoutes.DELETE("/:id", DigispectHandler.DeleteDigispectVehicle)
		digispectVehicleRoutes.GET("", DigispectHandler.GetDigispectVehicleList)
		digispectVehicleRoutes.GET("/customers", DigispectHandler.GetDigispectVehicleCustomers)
	}
	return route
}
