package persistence

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"

	"gorm.io/gorm"
)

type AssetInspectionVehicleRepository struct {
}

func NewAssetInspectionVehicleRepository() repository.AssetInspectionVehicleRepository {
	return &AssetInspectionVehicleRepository{}
}

func enrichAssetInspectionVehicleQueryWithWhere(query *gorm.DB, where models.AssetInspectionVehicleWhere) {
	if where.ClientID != "" {
		query.Where("ams_asset_inspection_vehicle.client_id = ?", where.ClientID)
	} // ClientID

	if where.StartDate != "" {
		query.Where("ams_asset_inspection_vehicle.created_at >= ?", where.StartDate)
	} // StartDate

	if where.EndDate != "" {
		query.Where("ams_asset_inspection_vehicle.created_at < ?", where.EndDate)
	} // EndDate

	if where.ReferenceCode != "" {
		query.Where("ams_asset_inspections.reference_code = ? AND ams_asset_inspections.reference_id = ?", where.ReferenceCode, where.ReferenceID)
	} // ReferenceCode

	if where.InspectionID != "" {
		query.Where("ams_asset_inspection_vehicle.asset_inspection_id = ?", where.InspectionID)
	} // InspectionID

	if where.AssetID != "" {
		query.Where("ams_asset_inspection_vehicle.asset_vehicle_id = ?", where.AssetID)
	} // AssetID

	if len(where.AssetIDs) > 0 {
		query.Where("ams_asset_inspection_vehicle.asset_vehicle_id IN ?", where.AssetIDs)
	} // AssetIDs

	if len(where.InspectByUserIDs) > 0 {
		query.Where("ams_asset_inspections.inspect_by_user_id IN ?", where.InspectByUserIDs)
	} // InspectByUserIDs

	if where.HasPartnerOwnerID {
		query.Where("ams_assets.partner_owner_id IS NOT NULL")
	}

	if len(where.DigispectConfIDs) > 0 {
		query.Where("ams_asset_inspection_vehicle.digispect_config_id IN ?", where.DigispectConfIDs)
	}

}

func enrichAssetInspectionVehicleQueryWithPreload(query *gorm.DB, preload models.AssetInspectionVehiclePreload) {
	if preload.AssetInspection {
		query.Preload("AssetInspection")
	}

	if preload.AssetVehicle {
		query.Preload("AssetVehicle")
	}

	if preload.Asset {
		query.Preload("Asset")
	} // Asset

	if preload.Asset {
		query.Preload("Asset.Brand")
	} // Asset Brand

	if preload.AssetLocation {
		query.Preload("Asset.Location")
	} // Asset Location

	if preload.AssetVehicleAsset {
		query.Preload("AssetVehicle.Asset")
	}

	if preload.AssetVehicleVehicle {
		query.Preload("AssetVehicle.Vehicle")
	}

	if preload.AssetInspectionTyres {
		query.Preload("AssetInspectionTyres")
	} // AssetInspectionTyres

	if preload.AssetInspectionTyresAssetTyre {
		query.Preload("AssetInspectionTyres.AssetTyre")
	} // AssetInspectionTyresAssetTyre

	if preload.AssetVehicleVehicleBrand {
		query.Preload("AssetVehicle.Vehicle.Brand")
	}

	if preload.DigispectVehicle {
		query.Preload("DigispectVehicle")
	}
}

func (r *AssetInspectionVehicleRepository) GetAssetInspectionVehicleList(ctx context.Context, dB database.DBI, param models.GetAssetInspectionVehicleListParam) (int, []models.AssetInspectionVehicle, error) {
	var totalRecords int64
	assetInspectionVehicles := []models.AssetInspectionVehicle{}
	query := dB.GetTx().Model(&assetInspectionVehicles)

	query.
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id").
		Joins("LEFT JOIN ams_assets ON ams_assets.id=ams_asset_inspection_vehicle.asset_vehicle_id")

	enrichAssetInspectionVehicleQueryWithWhere(query, param.Cond.Where)
	enrichAssetInspectionVehicleQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_asset_inspections.inspection_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assetInspectionVehicles, nil
	}

	query.Order("ams_asset_inspection_vehicle.updated_at DESC")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assetInspectionVehicles).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assetInspectionVehicles, nil
}

func (r *AssetInspectionVehicleRepository) GetAssetInspectionVehicle(ctx context.Context, dB database.DBI, cond models.AssetInspectionVehicleCondition) (*models.AssetInspectionVehicle, error) {
	assetInspectionVehicle := models.AssetInspectionVehicle{}
	query := dB.GetTx().Model(&assetInspectionVehicle)

	query.
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id").
		Joins("LEFT JOIN ams_assets ON ams_assets.id=ams_asset_inspection_vehicle.asset_vehicle_id")

	enrichAssetInspectionVehicleQueryWithWhere(query, cond.Where)
	enrichAssetInspectionVehicleQueryWithPreload(query, cond.Preload)

	err := query.First(&assetInspectionVehicle).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("inspection vehicle")
		}
		return nil, err
	}

	return &assetInspectionVehicle, nil
}

func (r *AssetInspectionVehicleRepository) CreateAssetInspectionVehicle(ctx context.Context, dB database.DBI, assetInspectionVehicle *models.AssetInspectionVehicle) error {
	return dB.GetTx().Create(assetInspectionVehicle).Error
}

func (r *AssetInspectionVehicleRepository) UpdateAssetInspectionVehicle(ctx context.Context, dB database.DBI, id string, assetInspectionVehicle *models.AssetInspectionVehicle) error {
	return dB.GetTx().
		Model(&models.AssetInspectionVehicle{}).
		Where("id = ?", id).
		Updates(assetInspectionVehicle).
		Error
}

func (r *AssetInspectionVehicleRepository) GetAssetInspectionVehiclesByIds(ctx context.Context, dB database.DBI, vehicles *[]models.AssetInspectionVehicle, ids []string) error {
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{})
	if err := query.Where("asset_inspection_id IN ?", ids).Preload("AssetVehicle").Preload("AssetVehicle.Asset").Find(&vehicles).Error; err != nil {
		return err
	}

	return nil
}

func (r *AssetInspectionVehicleRepository) GetLatestAssetInspectionVehicle(ctx context.Context, dB database.DBI, cond models.AssetInspectionVehicleCondition) (*models.AssetInspectionVehicle, error) {
	assetInspectionVehicle := models.AssetInspectionVehicle{}
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{})

	query.
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id").
		Joins("LEFT JOIN ams_assets ON ams_assets.id=ams_asset_inspection_vehicle.asset_vehicle_id")

	enrichAssetInspectionVehicleQueryWithWhere(query, cond.Where)
	enrichAssetInspectionVehicleQueryWithPreload(query, cond.Preload)

	query.Order("ams_asset_inspection_vehicle.created_at DESC")
	err := query.First(&assetInspectionVehicle).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("ASSET_INSPECTION_VEHICLE")
		}

		return nil, err
	}

	return &assetInspectionVehicle, nil
}

func (r *AssetInspectionVehicleRepository) ChartVehicleInspectionPerDate(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	query.Select(
		"DATE(ams_asset_inspections.created_at) AS name",
		"COUNT(DISTINCT ams_asset_inspection_vehicle.custom_reference_number) AS y",
	)

	query.Group("DATE(ams_asset_inspections.created_at)")
	query.Order("name ASC")

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (r *AssetInspectionVehicleRepository) ChartTop5VehicleBrands(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	// Filter out empty brand names
	query.Where("ams_asset_inspection_vehicle.custom_brand_name IS NOT NULL AND ams_asset_inspection_vehicle.custom_brand_name != ''")

	// Group by brand name, count occurrences
	query.Select(
		"ams_asset_inspection_vehicle.custom_brand_name AS name",
		"COUNT(*) AS y",
	)

	query.Group("ams_asset_inspection_vehicle.custom_brand_name")
	query.Order("y DESC")
	query.Limit(5)

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}
