package persistence

import (
	"assetfindr/internal/app/asset/constants"
	assetDtos "assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"

	taskDtos "assetfindr/internal/app/task/dtos"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"
	"encoding/json"
	"strings"

	// "context"

	"github.com/lib/pq"
	"gorm.io/gorm"
)

type AssetRepository struct {
}

func NewAssetRepository() repository.AssetRepository {
	return &AssetRepository{}
}

func (r *AssetRepository) GetAssetVehicles(ctx context.Context, dB database.DBI, assetVechicles *[]models.AssetVehicle, pageSize int, pageNo int, searchKeyword string) (int, error) {
	var totalRecords int64
	query := dB.GetTx().Model(&models.AssetVehicle{})

	if searchKeyword != "" {
		query = query.Joins("INNER JOIN ams_assets ON ams_asset_vehicles.asset_id = ams_assets.id").
			Where("LOWER(ams_assets.name) LIKE LOWER(?)", "%"+searchKeyword+"%").
			Preload("Asset", func(db *gorm.DB) *gorm.DB {
				return db.Where("LOWER(name) LIKE LOWER(?)", "%"+searchKeyword+"%")
			})
	}

	if err := query.Model(&models.AssetVehicle{}).Count(&totalRecords).Error; err != nil {
		return 0, err
	}

	offset := (pageNo - 1) * pageSize

	query = query.Offset(offset).Limit(pageSize)

	if err := query.Preload("AssetVehicleBodyType").Preload("Asset.Brand").Preload("Asset.AssetStatus").Find(&assetVechicles).Error; err != nil {
		return 0, err
	}

	return int(totalRecords), nil
}

func (ar *AssetRepository) GetAssetByID(ctx context.Context, dB database.DBI, id string) (*models.Asset, error) {
	var asset models.Asset
	err := dB.GetTx().Model(&asset).
		Where("ams_assets.id = ?", id).
		Preload("Brand").
		Preload("AssetStatus").
		First(&asset).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset")
		}
		return nil, err
	}
	return &asset, nil
}

func enrichAssetQueryWithWhere(query *gorm.DB, where models.AssetWhere) {
	if where.ID != "" {
		query.Where("ams_assets.id = ?", where.ID)
	}

	if where.ExcludedID != "" {
		query.Where("ams_assets.id != ?", where.ExcludedID)
	} // ExcludedID

	if where.ClientID != "" {
		query.Where("ams_assets.client_id = ?", where.ClientID)
	}

	if where.NonViewAllAssetCondition != nil {
		query.Joins(
			`LEFT JOIN ams_asset_assignments ON 
		ams_assets.id = ams_asset_assignments.asset_id
		AND ams_asset_assignments.unassigned_date_time IS NULL`)

		newQuery := query.Session(&gorm.Session{NewDB: true}).Unscoped().
			Where("ams_assets.created_by = ?", where.NonViewAllAssetCondition.UserID).
			Or("ams_asset_assignments.user_id = ?", where.NonViewAllAssetCondition.UserID)

		query.Where(newQuery)
	}

	if where.CreatedBy != "" {
		query.Where("ams_assets.created_by = ?", where.CreatedBy)
	}

	if len(where.SerialNumbers) > 0 {
		query.Where("ams_assets.serial_number IN ?", where.SerialNumbers)
	}

	if where.LowerSerialNumber != "" {
		query.Where("LOWER(ams_assets.serial_number) = ?", strings.ToLower(where.LowerSerialNumber))
	}

	if where.LowerRfid != "" {
		query.Where("LOWER(ams_assets.rfid) = ?", strings.ToLower(where.LowerRfid))
	}

	if len(where.Statuses) > 0 {
		query.Where("ams_assets.asset_status_code IN ?", where.Statuses)
	}
	if len(where.ExcludedStatuses) > 0 {
		query.Where("ams_assets.asset_status_code NOT IN ?", where.ExcludedStatuses)
	}

	if len(where.IDs) > 0 {
		query.Where("ams_assets.id IN ?", where.IDs)
	}
	if where.WithOrmDeleted {
		query.Unscoped()
	}

	if where.PartnerOwnerID != "" {
		query.Where("ams_assets.partner_owner_id = ?", where.PartnerOwnerID)
	}

	if len(where.Categories) > 0 {
		query.Where("ams_assets.asset_category_code IN ?", where.Categories)
	}

	if len(where.ExcludedCategories) > 0 {
		query.Where("ams_assets.asset_category_code NOT IN ?", where.ExcludedCategories)
	}

	if where.IsWorkshop {
		query.Where("(ams_assets.is_workshop IS TRUE AND ams_assets.asset_category_code = 'TYRE') OR (ams_assets.asset_category_code != 'TYRE')")
	} // IsWorkshop

	if len(where.SubCategories) > 0 {
		query.Where("ams_assets.sub_category_code IN ?", where.SubCategories)
	}

	if len(where.LocationIds) > 0 {
		query.Where("ams_assets.location_id IN ?", where.LocationIds)
	}

	if where.CreateOn != "" {
		query.Where("ams_assets.created_at::date = Cast(? as Date)", where.CreateOn)
	}

	if where.CreatedStartDate != "" {
		query.Where("ams_assets.created_at::date >= Cast(? as Date)", where.CreatedStartDate)
	}

	if where.CreatedEndDate != "" {
		query.Where("ams_assets.created_at::date <= Cast(? as Date)", where.CreatedEndDate)
	}

	if len(where.AssetIds) > 0 {
		query.Where("ams_assets.id IN (?)", where.AssetIds)
	}

	if where.BrandID != "" {
		query.Where("brand_id = ?", where.BrandID)
	}

	if where.AssignUserID != "" {
		query.Joins(
			`JOIN ams_asset_assignments ON 
			ams_assets.id = ams_asset_assignments.asset_id AND user_id = ? 
			AND ams_asset_assignments.unassigned_date_time IS NULL`,
			where.AssignUserID,
		)
	}

	if len(where.Rfids) > 0 {
		query.Where("ams_assets.rfid IN ?", where.Rfids)
	}

	if where.Rfid != "" {
		query.Where("rfid = ?", where.Rfid)
	}

	if where.CustomAssetCategoryID != "" {
		query.Where("ams_assets.custom_asset_category_id = ?", where.CustomAssetCategoryID)
	}

	if len(where.CustomAssetCategoryIDs) > 0 {
		query.Where("ams_assets.custom_asset_category_id IN ?", where.CustomAssetCategoryIDs)
	}

	if len(where.CustomAssetSubCategoryIDs) > 0 {
		query.Where("ams_assets.custom_asset_sub_category_id IN ?", where.CustomAssetSubCategoryIDs)
	}

	if where.LocationID != "" {
		query.Where("ams_assets.location_id = ?", where.LocationID)
	}

	if where.NotLinkedToAssetID != "" {
		subQuery1 := query.
			Session(&gorm.Session{NewDB: true}).
			Model(&models.AssetLinked{}).
			Select("child_asset_id").
			Where("parent_asset_id = ?", where.NotLinkedToAssetID).
			Where("unlinked_datetime IS NULL")

		subQuery2 := query.
			Session(&gorm.Session{NewDB: true}).
			Model(&models.AssetLinked{}).
			Select("parent_asset_id").
			Where("child_asset_id = ?", where.NotLinkedToAssetID).
			Where("unlinked_datetime IS NULL")

		query.Where("ams_assets.id NOT IN (?) AND ams_assets.id NOT IN (?)", subQuery1, subQuery2)
	}

	if len(where.OptimaxStatuses) > 0 {
		optimaxCategories := []string{constants.ASSET_CATEGORY_VEHICLE_CODE, constants.ASSET_CATEGORY_EQUIPMENT_CODE}

		subQuery := query.Session(&gorm.Session{NewDB: true}).Unscoped().
			Where("1 = 0")

		for _, optimaxStatus := range where.OptimaxStatuses {
			if optimaxStatus == constants.ASSET_OPTIMAX_STATUS_OPTIMAX {
				subQuery.Or("ams_assets.use_fleet_optimax = ?", true)
			}
			if optimaxStatus == constants.ASSET_OPTIMAX_STATUS_OPTIMAX_READY {
				subQuery.Or("ams_assets.asset_category_code IN (?) AND (ams_assets.use_fleet_optimax IS DISTINCT FROM true)", optimaxCategories)
			}
			if optimaxStatus == constants.ASSET_OPTIMAX_STATUS_NOT_FOR_OPTIMAX {
				subQuery.Or("ams_assets.asset_category_code NOT IN (?)", optimaxCategories)
			}
		}

		query.Where(subQuery)
	}

	if where.ModelID != "" {
		query.Where("ams_assets.model_id = ?", where.ModelID)
	}
	if len(where.ModelIDs) > 0 {
		query.Where("ams_assets.model_id IN ?", where.ModelIDs)
	}
}

func enrichAssetQueryWithPreload(query *gorm.DB, preload models.AssetPreload) {
	if preload.Brand {
		query.Preload("Brand")
	} // Brand

	if preload.AssetCategory {
		query.Preload("AssetCategory")
	} // AssetCategory

	if preload.SubCategory {
		query.Preload("SubCategory")
	} // SubCategory

	if preload.OwnershipCategory {
		query.Preload("OwnershipCategory")
	} // OwnershipCategory

	if preload.Location {
		query.Preload("Location")
	} // Location

	if preload.AssetStatus {
		query.Preload("AssetStatus")
	} // AssetStatus

	if preload.AssetTyre {
		query.Preload("AssetTyre")
	} // AssetTyre

	if preload.CustomAssetCategory {
		query.Preload("CustomAssetCategory")
	} // CustomAssetCategory

	if preload.CustomAssetSubCategory {
		query.Preload("CustomAssetSubCategory")
	} // CustomAssetSubCategory

	if preload.AssetModel {
		query.Preload("AssetModel")
	} // AssetModel

	if preload.AssetAssignment {
		query.Preload("AssetAssignment", func(db *gorm.DB) *gorm.DB {
			return db.Where("unassigned_date_time IS NULL")
		})
	}

	if preload.AssetTyreTyre {
		query.Preload("AssetTyre.Tyre")
	} // AssetTyreTyre

	if preload.AssetTyreWithLinkedParent {
		query.Preload(
			"AssetTyre.AssetLinked", "unlinked_datetime IS NULL",
		).
			Preload("AssetTyre.AssetLinked.AssetParent").
			Preload("AssetTyre.Tyre")
	} // AssetTyre

}

func (r *AssetRepository) GetAssetManagement(ctx context.Context, dB database.DBI, cond models.AssetCondition) (*models.Asset, error) {
	asset := models.Asset{}
	query := dB.GetOrm().Model(&asset)

	enrichAssetQueryWithWhere(query, cond.Where)
	enrichAssetQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&asset).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset")
		}

		return nil, err
	}

	return &asset, nil
}

func (r *AssetRepository) GetAssetManagementList(ctx context.Context, dB database.DBI, param models.GetAssetListParam) (int, []models.Asset, error) {
	var totalRecords int64
	assets := []models.Asset{}
	query := dB.GetTx().Model(&assets)

	enrichAssetQueryWithWhere(query, param.Cond.Where)
	enrichAssetQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Joins("LEFT JOIN ams_asset_models ON ams_assets.model_id = ams_asset_models.id").
			Joins("LEFT JOIN ams_brands ON ams_assets.brand_id = ams_brands.id")

		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_assets.name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_asset_models.asset_model_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_brands.brand_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	query.Where("asset_category_code != ?", constants.ASSET_CATEGORY_TYRE_CODE)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("ams_assets.updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assets, nil

}

func (ar *AssetRepository) GetAsset(ctx context.Context, dB database.DBI, condition models.AssetCondition) (*models.Asset, error) {
	var asset models.Asset
	query := dB.GetTx().Model(&asset)

	enrichAssetQueryWithWhere(query, condition.Where)
	enrichAssetQueryWithPreload(query, condition.Preload)

	err := query.First(&asset).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset")
		}
		return nil, err
	}

	return &asset, nil
}

func (ar *AssetRepository) GetAssets(ctx context.Context, dB database.DBI, condition models.AssetCondition) ([]models.Asset, error) {
	var assets []models.Asset
	query := dB.GetTx().Model(&assets)

	enrichAssetQueryWithWhere(query, condition.Where)
	enrichAssetQueryWithPreload(query, condition.Preload)

	if len(condition.Columns) > 0 {
		query.Select(condition.Columns)
	}

	err := query.Find(&assets).Error
	if err != nil {
		return nil, err
	}

	return assets, nil
}

func (ar *AssetRepository) GetAssetDataInformation(ctx context.Context, dB database.DBI, assetId string) ([]byte, error) {
	asset, err := ar.GetAsset(ctx, dB, models.AssetCondition{
		Where: models.AssetWhere{ID: assetId},
		Preload: models.AssetPreload{
			AssetTyre:              true,
			Brand:                  true,
			AssetCategory:          true,
			SubCategory:            true,
			CustomAssetCategory:    true,
			CustomAssetSubCategory: true,
			Location:               true,
		},
	})
	if err != nil {
		return nil, err
	}

	var assetVehicleLinked *assetDtos.AssetVehicleLinked
	if asset.AssetCategoryCode == "TYRE" {
		assetTyre := &models.AssetTyre{}
		query := dB.GetTx().Model(assetTyre)
		enrichAssetTyreQueryWithWhere(query, models.AssetTyreWhere{
			AssetID: assetId,
		})
		enrichAssetTyreQueryWithPreload(query, models.AssetTyrePreload{
			Tyre:               true,
			AssetLinkedVehicle: true,
		})

		err := query.First(assetTyre).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, errorhandler.ErrDataNotFound("asset tyre")
			}

			return nil, err
		}

		asset.AssetTyre = assetTyre

		if assetTyre.AssetLinked != nil {
			assetVehicleLinked = &assetDtos.AssetVehicleLinked{
				ID:           assetTyre.AssetLinked.ParentAssetID,
				SerialNumber: assetTyre.AssetLinked.ParentAsset.Asset.SerialNumber,
				Name:         assetTyre.AssetLinked.ParentAsset.Asset.Name,
			}
		}
	}

	assetDataInformation := taskDtos.AssetDataInformation{}
	assetDataInformation.BuildAssetDataInformtion(asset, assetVehicleLinked)

	dataInformation, err := json.Marshal(assetDataInformation)
	if err != nil {
		return nil, errorhandler.ErrBadRequest("marshall data information error")
	}

	return dataInformation, nil
}

func (ar *AssetRepository) GetAssetsByIDs(ctx context.Context, dB database.DBI, ids []string) ([]models.Asset, error) {
	var assets []models.Asset
	err := dB.GetTx().
		Where("ams_assets.id IN ?", ids).
		Preload("Brand").
		Preload("AssetStatus").
		Find(&assets).Error

	if err != nil {
		return nil, err
	}
	return assets, nil
}

func (ar *AssetRepository) CreateAsset(ctx context.Context, dB database.DBI, asset *models.Asset) error {
	return dB.GetTx().Create(asset).Error
}

func (ar *AssetRepository) IsSerialNumberExist(ctx context.Context, dB database.DBI, serialNumber string, clientID string) (bool, error) {
	var count int64
	if err := dB.GetTx().
		Model(&models.Asset{}).
		Where("serial_number = ?", serialNumber).
		Where("client_id = ?", clientID).
		Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

func (ar *AssetRepository) IsRegistrationNumberExist(ctx context.Context, dB database.DBI, registrationNumber string) (bool, error) {
	var count int64
	if err := dB.GetTx().Model(&models.AssetVehicle{}).Where("registration_number = ?", registrationNumber).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

func (ar *AssetRepository) IsEngineNumberExist(ctx context.Context, dB database.DBI, engineNumber string) (bool, error) {
	var count int64
	if err := dB.GetTx().Model(&models.AssetVehicle{}).Where("engine_number = ?", engineNumber).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

func (ar *AssetRepository) GetAssetBrands(ctx context.Context, dB database.DBI, brands *[]models.Brand, brandTags []string) error {
	query := dB.GetTx().Model(brands)
	if len(brandTags) > 0 {
		query.Where("brand_tags @> ?::varchar[]", pq.Array(brandTags))
	}

	return query.Find(brands).Error
}

func (ar *AssetRepository) UpdateAsset(ctx context.Context, dB database.DBI, asset *models.Asset) error {
	return dB.GetTx().Model(asset).Updates(asset).Error
}

func (ar *AssetRepository) UpdateAssetForUpdateWithNull(ctx context.Context, dB database.DBI, id string, asset *models.AssetForUpdateWithNull) error {
	return dB.GetTx().Where("id = ?", id).Updates(asset).Error
}

func (ar *AssetRepository) UpdateAssetVehicle(ctx context.Context, dB database.DBI, vehicle *models.AssetVehicle) error {
	return dB.GetTx().Model(vehicle).Updates(*vehicle).Error
}

func (ar *AssetRepository) GetAssetVehicle(ctx context.Context, dB database.DBI, condition models.AssetVehicleCondition) (*models.AssetVehicle, error) {
	assetVehicle := models.AssetVehicle{}
	query := dB.GetTx().Model(&assetVehicle)

	if condition.Where.AssetID != "" {
		query.Where("asset_id = ?", condition.Where.AssetID)
	}

	if len(condition.Columns) > 0 {
		query = query.Select(condition.Columns)
	}

	err := query.Find(&assetVehicle).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset vehicle")
		}
		return nil, err
	}

	return &assetVehicle, nil
}

func (ar *AssetRepository) GetAssetList(ctx context.Context, dB database.DBI, param models.GetAssetListParam) (int, []models.Asset, error) {
	var totalRecords int64
	assets := []models.Asset{}
	query := dB.GetTx().Model(&assets)

	if param.Cond.Where.ClientID != "" {
		query.Where("ams_assets.client_id = ?", param.Cond.Where.ClientID)
	}

	if len(param.Cond.Where.Statuses) > 0 {
		query.Where("ams_assets.asset_status_code IN ?", param.Cond.Where.Statuses)
	}

	if param.SearchKeyword != "" {
		query.Where(query.Session(&gorm.Session{NewDB: true}).
			Unscoped().
			Where("LOWER(ams_assets.name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
			Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
			Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assets, nil
	}

	query.Order("ams_assets.updated_at DESC")

	query.Preload("Brand").Preload("AssetStatus")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assets, nil
}

func (ar *AssetRepository) GetAssetSelectionsList(ctx context.Context, dB database.DBI, param models.GetAssetListParam) (int, []models.Asset, error) {
	var totalRecords int64
	assets := []models.Asset{}
	query := dB.GetTx().Model(&assets)
	enrichAssetQueryWithWhere(query, param.Cond.Where)

	query.Joins("AssetVehicle.AssetVehicleBodyType").Joins("AssetTyre.Tyre")

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_assets.name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(registration_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(pattern_type) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assets, nil
	}

	// LEGACY
	query.Preload("ChildAssetLinked", func(db *gorm.DB) *gorm.DB {
		return db.Where("unlinked_datetime IS NULL")
	}).Preload("ChildAssetLinked.AssetLinkedAssetVehicleTyre").
		Preload("ChildAssetLinked.ParentAsset.Asset")

	query.Preload("CurrentLinkedVehicleTyre", func(db *gorm.DB) *gorm.DB {
		return db.Where("unlinked_meter_state_id IS NULL")
	}).Preload("CurrentLinkedVehicleTyre.ParentAsset")

	query.Preload("Brand").Preload("AssetStatus").Preload("AssetCategory").Preload("SubCategory")
	query.Preload("AssetVehicle.AssetVehicleBodyType").Preload("AssetTyre.Tyre")

	query.Order("ams_assets.updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assets, nil
}

func (ar *AssetRepository) UpdateAssetStatusCode(ctx context.Context, dB database.DBI, id string, assetStatusCode string) error {
	return dB.GetTx().Where("id = ?", id).Updates(&models.Asset{AssetStatusCode: assetStatusCode}).Error
}

func (ar *AssetRepository) UpdateAssetStatusByIDs(ctx context.Context, dB database.DBI, ids []string, assetStatusCode string) error {
	return dB.GetTx().Model(&models.Asset{}).
		Where("id IN ?", ids).
		Update("asset_status_code", assetStatusCode).
		Error
}

func enrichAssetCategoryQueryWithPreload(query *gorm.DB, preload models.AssetCategoryPreload) {
}

func enrichAssetCategoryQueryWithWhere(query *gorm.DB, where models.AssetCategoryWhere) {
	if !where.IsShowAll {
		query.Where("is_general = TRUE")
	}

	if where.Code != "" {
		query.Where("code = ?", where.Code)
	}
}

func (r *AssetRepository) GetAssetCategory(ctx context.Context, dB database.DBI, cond models.AssetCategoryCondition) (*models.AssetCategory, error) {
	assetCategory := &models.AssetCategory{}
	query := dB.GetOrm().Model(&models.AssetCategory{})

	enrichAssetCategoryQueryWithWhere(query, cond.Where)
	enrichAssetCategoryQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&assetCategory).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset category")
		}

		return nil, err
	}

	return assetCategory, nil
}

func (r *AssetRepository) GetAssetCategories(ctx context.Context, dB database.DBI, cond models.AssetCategoryCondition) ([]models.AssetCategory, error) {
	assetCategories := []models.AssetCategory{}
	query := dB.GetOrm().Model(&assetCategories)

	enrichAssetCategoryQueryWithWhere(query, cond.Where)
	enrichAssetCategoryQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.Find(&assetCategories).Error
	if err != nil {
		return nil, err
	}

	return assetCategories, nil
}

func enrichAssetSubCategoryQueryWithPreload(query *gorm.DB, preload models.AssetSubCategoryPreload) {
}

func enrichAssetSubCategoryQueryWithWhere(query *gorm.DB, where models.AssetSubCategoryWhere) {
	if where.Code != "" {
		query.Where("code = ?", where.Code)
	}
}

func (r *AssetRepository) GetAssetSubCategory(ctx context.Context, dB database.DBI, cond models.AssetSubCategoryCondition) (*models.AssetSubCategory, error) {
	assetCategory := &models.AssetSubCategory{}
	query := dB.GetOrm().Model(&models.AssetSubCategory{})

	enrichAssetSubCategoryQueryWithWhere(query, cond.Where)
	enrichAssetSubCategoryQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&assetCategory).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset category")
		}

		return nil, err
	}

	return assetCategory, nil
}

func enrichAssetCategoryMappingQueryWithPreload(query *gorm.DB, preload models.AssetCategoryMappingPreload) {
	if preload.SubCategory {
		query.Preload("SubCategory")
	}
}

func enrichAssetCategoryMappingQueryWithWhere(query *gorm.DB, where models.AssetCategoryMappingWhere) {
	if where.CategoryCode != "" {
		query.Where("category_code = ?", where.CategoryCode)
	} // CategoryCode

}

func (r *AssetRepository) GetAssetCategoryMapping(ctx context.Context, dB database.DBI, cond models.AssetCategoryMappingCondition) (*models.AssetCategoryMapping, error) {
	assetSubCategory := &models.AssetCategoryMapping{}
	query := dB.GetOrm().Model(&models.AssetCategoryMapping{})

	enrichAssetCategoryMappingQueryWithWhere(query, cond.Where)
	enrichAssetCategoryMappingQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&assetSubCategory).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset sub category")
		}
		return nil, err
	}

	return assetSubCategory, nil
}

func (r *AssetRepository) GetAssetCategoryMappings(ctx context.Context, dB database.DBI, cond models.AssetCategoryMappingCondition) ([]models.AssetCategoryMapping, error) {
	assetSubCategories := []models.AssetCategoryMapping{}
	query := dB.GetOrm().Model(&assetSubCategories)

	enrichAssetCategoryMappingQueryWithWhere(query, cond.Where)
	enrichAssetCategoryMappingQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.Find(&assetSubCategories).Error
	if err != nil {
		return nil, err
	}

	return assetSubCategories, nil
}

func (ar *AssetRepository) UpdateAssetPartnerOwnerName(ctx context.Context, dB database.DBI, partnerID string, newName string) error {
	return dB.GetTx().
		Model(&models.Asset{}).
		Where("partner_owner_id = ?", partnerID).
		Update("name", newName).Error
}

func (ar *AssetRepository) GetAssetStatusByCode(ctx context.Context, dB database.DBI, code string) (*models.AssetStatus, error) {
	var assetStatus models.AssetStatus
	err := dB.GetTx().Model(&assetStatus).
		Where("code = ?", code).
		First(&assetStatus).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset status")
		}
		return nil, err
	}

	return &assetStatus, nil
}

func (ar *AssetRepository) CountAsset(ctx context.Context, dB database.DBI, condition models.AssetCondition) (int, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, condition.Where)

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return 0, err
	}

	return int(total), nil
}

func (ar *AssetRepository) AddAssetDowngradeReason(ctx context.Context, dB database.DBI, assetID string, downgradeReason string) error {
	return dB.GetTx().
		Model(&models.Asset{}).
		Where("id = ?", assetID).
		Updates(map[string]interface{}{
			"downgrade_reason": gorm.Expr("COALESCE(downgrade_reason, '{}'::VARCHAR(255)[]) || ?", downgradeReason),
		}).Error
}
