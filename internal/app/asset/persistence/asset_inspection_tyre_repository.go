package persistence

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"
	"fmt"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AssetInspectionTyreRepository struct{}

func NewAssetInspectionTyreRepository() repository.AssetInspectionTyreRepository {
	return &AssetInspectionTyreRepository{}
}

// Deprecated
func (r *AssetInspectionTyreRepository) GetAssetInspectionTyres(ctx context.Context, dB database.DBI, tyres *[]models.AssetInspectionTyre, pagination commonmodel.ListRequest, inspectionID string) (int, error) {
	var totalRecords int64
	query := dB.GetTx().Model(&models.AssetInspectionTyre{})

	if pagination.SearchKeyword != "" {
		query.Where("LOWER(client_id) LIKE LOWER(?)", "%"+pagination.SearchKeyword+"%")
	}

	if err := query.Model(&models.AssetInspectionTyre{}).Count(&totalRecords).Error; err != nil {
		return 0, err
	}

	offset := (pagination.PageNo - 1) * pagination.PageSize

	query = query.Offset(offset).Limit(pagination.PageSize)

	if inspectionID != "" {
		query = query.Where("asset_inspection_id = ?", inspectionID)
	}

	if err := query.Preload("AssetInspection").Preload("AssetTyre").Preload("AssetTyre.Asset").Find(tyres).Error; err != nil {
		return 0, err
	}

	return int(totalRecords), nil
}

func (r *AssetInspectionTyreRepository) CreateAssetInspectionTyre(ctx context.Context, dB database.DBI, assetInspectionTyre *models.AssetInspectionTyre) error {
	return dB.GetTx().Create(assetInspectionTyre).Error
}

func (r *AssetInspectionTyreRepository) UpdateAssetInspectionTyre(ctx context.Context, dB database.DBI, id string, assetInspectionTyre *models.AssetInspectionTyre) error {
	return dB.GetTx().Where("id = ?", id).Updates(assetInspectionTyre).Error
}

func enrichAssetInspectionTyreQueryWithWhere(query *gorm.DB, where models.AssetInspectionTyreWhere) {
	if where.IsSingleTyreInspection {
		query.
			Joins("LEFT JOIN ams_asset_inspection_vehicle aaiv ON aaiv.asset_inspection_id = ams_asset_inspection_tyre.asset_inspection_id").
			Where("aaiv.id IS NULL")
	} // IsSingleTyreInspection

	if where.ClientID != "" {
		query.Where("ams_asset_inspection_tyre.client_id = ?", where.ClientID)
	} // ClientID

	if where.StartDate != "" {
		query.Where("ams_asset_inspections.created_at >= ?", where.StartDate)
	} // StartDate

	if where.EndDate != "" {
		query.Where("ams_asset_inspections.created_at < ?", where.EndDate)
	} // EndDate

	if !where.StartDate1.IsZero() {
		query.Where("ams_asset_inspections.created_at >= ?", where.StartDate1)
	} // StartDate1

	if !where.EndDate1.IsZero() {
		query.Where("ams_asset_inspections.created_at < ?", where.EndDate1)
	} // EndDate1

	if where.ReferenceCode != "" {
		query.Where("ams_asset_inspections.reference_code = ? AND ams_asset_inspections.reference_id = ?", where.ReferenceCode, where.ReferenceID)
	} // ReferenceCode

	if where.InspectionID != "" {
		query.Where("ams_asset_inspection_tyre.asset_inspection_id = ?", where.InspectionID)
	} // InspectionID

	if len(where.InspectionIDs) > 0 {
		query.Where("ams_asset_inspection_tyre.asset_inspection_id IN ?", where.InspectionIDs)
	} // InspectionIDs

	if where.AssetID != "" {
		query.Where("ams_asset_inspection_tyre.asset_tyre_id = ?", where.AssetID)
	} // AssetID

	if len(where.AssetIDs) > 0 {
		query.Where("ams_asset_inspection_tyre.asset_tyre_id IN ?", where.AssetIDs)
	} // AssetIDs

	if len(where.InspectByUserIDs) > 0 {
		query.Where("ams_asset_inspections.inspect_by_user_id IN ?", where.InspectByUserIDs)
	} // InspectByUserIDs

	if where.InspectByUserID != "" {
		query.Where("ams_asset_inspections.inspect_by_user_id = ?", where.InspectByUserID)
	} // InspectByUserID

	if where.HasPartnerOwnerID {
		query.Where("ams_assets.partner_owner_id IS NOT NULL")
	}

	if where.HasRTD {
		query.Where("ams_asset_inspection_tyre.average_rtd != 0")
		// query.Where("ams_asset_inspection_tyre.average_rtd IS NOT NULL")
	}

	if where.HasPressure {
		query.Where("ams_asset_inspection_tyre.pressure != 0")
		// query.Where("ams_asset_inspection_tyre.pressure IS NOT NULL")
	}

	if where.HasTemperature {
		query.Where("ams_asset_inspection_tyre.temperature IS NOT NULL")
	}

	if where.DigiSpectOnlySource {
		query.Where("ams_asset_inspection_tyre.source_type_code IN (?)", []string{
			constants.INSPECTION_SOURCE_TRANSLOGIC_APP,
			constants.INSPECTION_SOURCE_ASSETFINDR_APP,
		})
	} // DigiSpectOnlySource

	if where.DigispectConfID != "" {
		query.Where("ams_asset_inspection_tyre.digispect_config_id = ?", where.DigispectConfID)
	}
	if len(where.DigispectConfIDs) > 0 {
		query.Where("ams_asset_inspection_tyre.digispect_config_id IN ?", where.DigispectConfIDs)
	}

	// ASSET INSPECTION TYRE
	if !where.StartDateTyre.IsZero() {
		query.Where("ams_asset_inspection_tyre.created_at >= ?", where.StartDateTyre)
	} // StartDate1

	if !where.EndDateTyre.IsZero() {
		query.Where("ams_asset_inspection_tyre.created_at < ?", where.EndDateTyre)
	} // EndDate1
}

func enrichAssetInspectionTyreQueryWithPreload(query *gorm.DB, preload models.AssetInspectionTyrePreload) {
	if preload.AssetInspection {
		query.Preload("AssetInspection")
	}

	if preload.AssetTyre {
		query.Preload("AssetTyre")
	}

	if preload.AssetTyreAsset {
		query.Preload("AssetTyre.Asset")
	}

	if preload.AssetTyreAssetBrand {
		query.Preload("AssetTyre.Asset.Brand")
	}

	if preload.AssetTyreTyre {
		query.Preload("AssetTyre.Tyre")
	}

	if preload.AssetInspectionVehicle {
		query.Preload("AssetInspectionVehicle")
	}

	if preload.AssetInspectionVehicleAsset {
		query.Preload("AssetInspectionVehicle.Asset")
	}

	if preload.DigispectConfig {
		query.Preload("DigispectConfig.BrandDigiscpect")
	}

	if preload.AssetInspectionVehicleAssetBrand {
		query.Preload("AssetInspectionVehicle.Asset.Brand")
	}

	if preload.AssetInspectionVehicleAssetModel {
		query.Preload("AssetInspectionVehicle.Asset.AssetModel")
	}
}

func (r *AssetInspectionTyreRepository) GetAssetInspectionTyreList(ctx context.Context, dB database.DBI, param models.GetAssetInspectionTyreListParam) (int, []models.AssetInspectionTyre, error) {
	var totalRecords int64
	assetInspectionTyres := []models.AssetInspectionTyre{}
	query := dB.GetTx().Model(&assetInspectionTyres)

	query.
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id=ams_asset_inspection_tyre.asset_inspection_id").
		Joins("LEFT JOIN ams_assets ON ams_assets.id=ams_asset_inspection_tyre.asset_tyre_id")

	enrichAssetInspectionTyreQueryWithWhere(query, param.Cond.Where)
	enrichAssetInspectionTyreQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_asset_inspections.inspection_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_asset_inspection_tyre.custom_serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assetInspectionTyres, nil
	}

	directionDesc := true
	if param.Cond.Where.Direction == "ASC" {
		directionDesc = false
	}

	sortColumn := "updated_at"
	if param.Cond.Where.SortBy != "" {
		sortColumn = param.Cond.Where.SortBy
	}

	query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortColumn}, Desc: directionDesc})
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assetInspectionTyres).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assetInspectionTyres, nil
}

func (r *AssetInspectionTyreRepository) GetAssetInspectionTyreListExport(ctx context.Context, dB database.DBI, param models.GetAssetInspectionTyreListParam) (int, []models.AssetInspectionTyre, error) {
	var totalRecords int64
	assetInspectionTyres := []models.AssetInspectionTyre{}
	query := dB.GetTx().Model(&assetInspectionTyres)

	query.
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id=ams_asset_inspection_tyre.asset_inspection_id").
		Joins("LEFT JOIN ams_assets ON ams_assets.id=ams_asset_inspection_tyre.asset_tyre_id")

	enrichAssetInspectionTyreQueryWithWhere(query, param.Cond.Where)
	enrichAssetInspectionTyreQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_asset_inspections.inspection_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_asset_inspection_tyre.custom_serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assetInspectionTyres, nil
	}

	directionDesc := true
	if param.Cond.Where.Direction == "ASC" {
		directionDesc = false
	}

	sortColumn := "updated_at"
	if param.Cond.Where.SortBy != "" {
		sortColumn = param.Cond.Where.SortBy
	}

	query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortColumn}, Desc: directionDesc})
	err = query.Find(&assetInspectionTyres).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assetInspectionTyres, nil
}

func (r *AssetInspectionTyreRepository) GetAssetInspectionTyre(ctx context.Context, dB database.DBI, cond models.AssetInspectionTyreCondition) (*models.AssetInspectionTyre, error) {
	assetInspectionTyres := models.AssetInspectionTyre{}
	query := dB.GetTx().Model(&assetInspectionTyres)

	// query.
	// 	Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id=ams_asset_inspection_tyre.asset_inspection_id").
	// 	Joins("LEFT JOIN ams_assets ON ams_assets.id=ams_asset_inspection_tyre.asset_tyre_id")

	enrichAssetInspectionTyreQueryWithWhere(query, cond.Where)
	enrichAssetInspectionTyreQueryWithPreload(query, cond.Preload)

	query.Order("created_at DESC")
	err := query.First(&assetInspectionTyres).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset inspection tyre")
		}
		return nil, err
	}

	return &assetInspectionTyres, nil
}

func (r *AssetInspectionTyreRepository) GetAssetInspectionTyresV2(ctx context.Context, dB database.DBI, cond models.AssetInspectionTyreCondition) ([]models.AssetInspectionTyre, error) {
	assetInspectionTyres := []models.AssetInspectionTyre{}
	query := dB.GetTx().Model(&models.AssetInspectionTyre{})

	query.
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id=ams_asset_inspection_tyre.asset_inspection_id").
		Joins("LEFT JOIN ams_assets ON ams_assets.id=ams_asset_inspection_tyre.asset_tyre_id")

	enrichAssetInspectionTyreQueryWithWhere(query, cond.Where)
	enrichAssetInspectionTyreQueryWithPreload(query, cond.Preload)

	query.Order("ams_asset_inspections.created_at DESC")
	err := query.Find(&assetInspectionTyres).Error
	if err != nil {
		return nil, err
	}

	return assetInspectionTyres, nil
}

func (ar *AssetInspectionTyreRepository) ChartTotalInspectionToDate(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionTyre{})

	startOfMonth := time.Now().Truncate(24*time.Hour).AddDate(0, 0, -time.Now().Day()+1)
	endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Nanosecond)

	query.Joins("LEFT JOIN ams_asset_inspection_vehicle ON ams_asset_inspection_vehicle.asset_inspection_id = ams_asset_inspection_tyre.asset_inspection_id").
		Where("ams_asset_inspection_vehicle.id IS NOT NULL").
		Where("ams_asset_inspection_tyre.asset_tyre_id IS NOT NULL")

	enrichAssetInspectionTyreQueryWithWhere(query, models.AssetInspectionTyreWhere{
		ClientID:      clientID,
		StartDateTyre: startOfMonth,
		EndDateTyre:   endOfMonth,
	})

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}

	return []commonmodel.Chart{
		{
			Y:    float64(total),
			Name: "No. of Tyre Inspections to Date",
		},
	}, nil
}

func (ar *AssetInspectionTyreRepository) ChartCountInspectionTyreType(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionTyre{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_tyre.asset_inspection_id")

	startOfMonth := time.Now().Truncate(24*time.Hour).AddDate(0, 0, -time.Now().Day()+1)
	endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Nanosecond)

	enrichAssetInspectionTyreQueryWithWhere(query, models.AssetInspectionTyreWhere{
		ClientID:      clientID,
		StartDateTyre: startOfMonth,
		EndDateTyre:   endOfMonth,
	})

	caseExprName := `
		CASE 
			WHEN ams_asset_inspection_tyre.source_type_code IN ('ASSETFINDR_APP', 'TRANSLOGIC_APP') THEN 'DigiSpect'
			ELSE 'Manual'
		END`

	caseExprCode := `
		CASE 
			WHEN ams_asset_inspection_tyre.source_type_code IN ('ASSETFINDR_APP', 'TRANSLOGIC_APP') THEN 'DIGISPECT'
			ELSE 'MANUAL'
		END`

	query.Select(
		fmt.Sprintf("%s AS name", caseExprName),
		fmt.Sprintf("%s AS code", caseExprCode),
		"COUNT(DISTINCT ams_asset_inspections.id) AS y",
	)

	query.Group(fmt.Sprintf("%s, %s", caseExprName, caseExprCode))

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetInspectionTyreRepository) ChartCountInstalledTyreInspected(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	startOfMonth := time.Now().Truncate(24*time.Hour).AddDate(0, 0, -time.Now().Day()+1)
	endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Nanosecond)

	subQuery := dB.GetTx().Model(&models.AssetLinked{})

	subQuery.Where("ams_linked_assets.client_id = ? ", clientID)
	subQuery.Where("ams_linked_assets.unlinked_datetime IS NULL OR ams_linked_assets.unlinked_datetime >= ?", startOfMonth)
	subQuery.Where("ams_linked_assets.linked_asset_type_code = ?", constants.ASSET_LINKED_TYPE_VEHICLE_TYRE)

	inspectionSubQuery := dB.GetTx().
		Table("ams_asset_inspection_tyre").
		Select("ams_asset_inspection_tyre.*").
		Joins("INNER JOIN ams_asset_inspection_vehicle ON ams_asset_inspection_tyre.asset_inspection_id = ams_asset_inspection_vehicle.asset_inspection_id").
		Where("ams_asset_inspection_tyre.created_at >= ? AND ams_asset_inspection_tyre.created_at < ?", startOfMonth, endOfMonth)

	subQuery.Joins(
		"LEFT JOIN (?) AS ams_asset_inspection_tyre ON ams_asset_inspection_tyre.asset_tyre_id = ams_linked_assets.child_asset_id",
		inspectionSubQuery,
	)
	subQuery.Group("ams_linked_assets.child_asset_id")

	subQuery.Select(
		"ams_linked_assets.child_asset_id",
		`CASE 
			WHEN COUNT(distinct ams_asset_inspection_tyre.id) = 0 THEN 'NEVER_INSPECTED'
			WHEN COUNT(distinct ams_asset_inspection_tyre.id) = 1 THEN '1_INSPECTION'
			WHEN COUNT(distinct ams_asset_inspection_tyre.id) BETWEEN 2 AND 5 THEN '2_5_INSPECTION'
			WHEN COUNT(distinct ams_asset_inspection_tyre.id) > 5 THEN 'MT_5_INSPECTION'
		END AS code`,
	)

	query := dB.GetTx().Table("(?) AS temp", subQuery)
	query.Group("code")
	query.Select(
		`CASE code 
			WHEN 'NEVER_INSPECTED' THEN 'Never Inspected'
			WHEN '1_INSPECTION' THEN '1 Inspection'
			WHEN '2_5_INSPECTION' THEN '2-5 Inspection'
			WHEN 'MT_5_INSPECTION' THEN '>5 Inspection'
		END AS name`,
		"code",
		"COUNT(child_asset_id) AS y",
	)

	var charts []commonmodel.Chart
	if err := query.Scan(&charts).Error; err != nil {
		return nil, err
	}

	// Ensure all inspection types are represented
	existingCodes := map[string]bool{}
	for _, chart := range charts {
		existingCodes[chart.Code.String] = true
	}

	requiredCodes := map[string]string{
		"NEVER_INSPECTED": "Never Inspected",
		"1_INSPECTION":    "1 Inspection",
		"2_5_INSPECTION":  "2-5 Inspection",
		"MT_5_INSPECTION": ">5 Inspection",
	}

	for code, name := range requiredCodes {
		if !existingCodes[code] {
			charts = append(charts, commonmodel.Chart{
				Name: name,
				Code: null.StringFrom(code),
				Y:    0,
			})
		}
	}

	return charts, nil
}

func (ar *AssetInspectionTyreRepository) ChartCountInstalledTyreNotInspected(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	startOfMonth := time.Now().Truncate(24*time.Hour).AddDate(0, 0, -time.Now().Day()+1)
	endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Nanosecond)

	subQuery := dB.GetTx().Model(&models.AssetLinked{})

	subQuery.Where("ams_linked_assets.client_id = ?", clientID)
	subQuery.Where("ams_linked_assets.unlinked_datetime IS NULL OR ams_linked_assets.unlinked_datetime >= ?", startOfMonth)
	subQuery.Where("ams_linked_assets.linked_asset_type_code = ?", constants.ASSET_LINKED_TYPE_VEHICLE_TYRE)

	inspectionSubQuery := dB.GetTx().
		Table("ams_asset_inspection_tyre").
		Select("ams_asset_inspection_tyre.*").
		Joins("INNER JOIN ams_asset_inspection_vehicle ON ams_asset_inspection_tyre.asset_inspection_id = ams_asset_inspection_vehicle.asset_inspection_id").
		Where("ams_asset_inspection_tyre.created_at >= ? AND ams_asset_inspection_tyre.created_at < ?", startOfMonth, endOfMonth)

	subQuery.Joins(
		"LEFT JOIN (?) AS ams_asset_inspection_tyre ON ams_asset_inspection_tyre.asset_tyre_id = ams_linked_assets.child_asset_id",
		inspectionSubQuery,
	)

	subQuery.Group("ams_linked_assets.child_asset_id")

	subQuery.Select(
		"ams_linked_assets.child_asset_id",
		`CASE 
			WHEN COUNT(DISTINCT ams_asset_inspection_tyre.id) = 0 THEN 'NEVER_INSPECTED'
		END AS code`,
	)

	query := dB.GetTx().Table("(?) AS temp", subQuery)
	query.Group("code")
	query.Select(
		`CASE code 
			WHEN 'NEVER_INSPECTED' THEN 'Never Inspected'
		END AS name`,
		"code",
		"COUNT(child_asset_id) AS y",
	)

	var charts []commonmodel.Chart
	if err := query.Scan(&charts).Error; err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetInspectionTyreRepository) ChartTyreInspectionPerDate(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionTyre{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_tyre.asset_inspection_id")

	enrichAssetInspectionTyreQueryWithWhere(query, models.AssetInspectionTyreWhere{
		ClientID:      req.ClientID,
		StartDateTyre: req.StartDatetime,
		EndDateTyre:   req.EndDatetime,
	})

	// Group by date and count unique custom_serial_number
	query.Select(
		"DATE(ams_asset_inspections.created_at) AS name",
		"COUNT(DISTINCT ams_asset_inspection_tyre.custom_serial_number) AS y",
	)

	query.Group("DATE(ams_asset_inspections.created_at)")
	query.Order("name ASC")

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetInspectionTyreRepository) ChartTop5TyreBrandBySize(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionTyre{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_tyre.asset_inspection_id")

	enrichAssetInspectionTyreQueryWithWhere(query, models.AssetInspectionTyreWhere{
		ClientID:      req.ClientID,
		StartDateTyre: req.StartDatetime,
		EndDateTyre:   req.EndDatetime,
	})

	// Filter out empty brand names or sizes
	query.Where("ams_asset_inspection_tyre.custom_brand_name IS NOT NULL AND ams_asset_inspection_tyre.custom_brand_name != ''")
	query.Where("ams_asset_inspection_tyre.custom_tyre_size IS NOT NULL AND ams_asset_inspection_tyre.custom_tyre_size != ''")

	// Group by brand and size, count occurrences
	query.Select(
		"ams_asset_inspection_tyre.custom_brand_name AS x",
		"ams_asset_inspection_tyre.custom_tyre_size AS name",
		"ams_asset_inspection_tyre.custom_tyre_size AS code",
		"COUNT(*) AS y",
	)

	query.Group("ams_asset_inspection_tyre.custom_brand_name, ams_asset_inspection_tyre.custom_tyre_size")

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}
