package persistence

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AssetLinkedRepository struct{}

func NewAssetLinkedRepository() repository.AssetLinkedRepository {
	return &AssetLinkedRepository{}
}

func (r *AssetLinkedRepository) GetAssetLinkeds(ctx context.Context, dB database.DBI, condition models.AssetLinkedCondition) ([]models.AssetLinked, error) {
	assetLinkeds := []models.AssetLinked{}
	query := dB.GetOrm().Model(assetLinkeds)

	enrichAssetLinkedWithWhere(query, condition.Where)
	enrichAssetLinkedWithPreload(query, condition.Preload)

	if condition.IsForUpdate {
		query.Clauses(clause.Locking{Strength: "UPDATE"})
	}

	err := query.Find(&assetLinkeds).Error
	if err != nil {
		return nil, err
	}

	return assetLinkeds, nil
}

func enrichAssetLinkedWithWhere(query *gorm.DB, where models.AssetLinkedWhere) {
	if where.ParentAssetID != "" {
		query.Where("ams_linked_assets.parent_asset_id = ?", where.ParentAssetID)
	}

	if where.ChildAssetID != "" {
		query.Where("ams_linked_assets.child_asset_id = ?", where.ChildAssetID)
	}

	if where.RelatedAssetID != "" {
		query.Where(
			"ams_linked_assets.parent_asset_id = ? OR ams_linked_assets.child_asset_id = ?",
			where.RelatedAssetID,
			where.RelatedAssetID)
	}

	if where.ClientID != "" {
		query.Where("ams_linked_assets.client_id = ?", where.ClientID)
	}

	if where.ID != "" {
		query.Where("ams_linked_assets.id = ?", where.ID)
	}

	if len(where.IDs) > 0 {
		query.Where("ams_linked_assets.id IN ?", where.IDs)
	}

	if !where.WithUnlinked {
		query.Where("ams_linked_assets.unlinked_datetime IS NULL")
	}

	if where.TypeCode != "" {
		query.Where("ams_linked_assets.linked_asset_type_code = ?", where.TypeCode)
	}
}

func enrichAssetLinkedWithPreload(query *gorm.DB, preload models.AssetLinkedPreload) {
	if preload.AssetLinkedAssetVehicleTyre {
		query.Preload("AssetLinkedAssetVehicleTyre")
	}

	if preload.ChildAsset {
		query.Preload("ChildAsset")
	}

	if preload.ParentAsset {
		query.Preload("ParentAsset")
	}

	if preload.AssetChild {
		query.Preload("AssetChild")
	}

	if preload.AssetParent {
		query.Preload("AssetParent")
	}

}

func (r *AssetLinkedRepository) GetAssetLinked(ctx context.Context, dB database.DBI, condition models.AssetLinkedCondition) (*models.AssetLinked, error) {
	assetLinked := models.AssetLinked{}
	query := dB.GetOrm().Model(assetLinked)

	enrichAssetLinkedWithWhere(query, condition.Where)
	enrichAssetLinkedWithPreload(query, condition.Preload)

	err := query.First(&assetLinked).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset linked")
		}
		return nil, err
	}

	return &assetLinked, nil
}

func (r *AssetLinkedRepository) GetAssetLinkedTyres(ctx context.Context, dB database.DBI, assetLinkedRecords *[]models.AssetLinkedAssetVehicleTyre, parentAssetId string) error {

	query := dB.GetOrm().Model(&models.AssetLinkedAssetVehicleTyre{}).
		Joins("JOIN ams_linked_assets ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id").
		Preload("AssetLinked").
		Preload("AssetLinked.ChildAsset").
		Preload("AssetLinked.ParentAsset").
		Preload("AssetLinked.LinkedAssetType").
		Where("unlinked_datetime IS NULL")

	if parentAssetId != "" {
		query = query.Where("ams_linked_assets.parent_asset_id = ?", parentAssetId)
	}

	if err := query.Find(assetLinkedRecords).Error; err != nil {
		return err
	}

	return nil
}

func enrichAssetLinkedTyresWithPreload(query *gorm.DB, preload models.AssetLinkedVehicleTyrePreload) {
	if preload.AssetLinked {
		query.Preload("AssetLinked")
	}

	if preload.AssetLinkedChildAsset {
		query.Preload("AssetLinked.ChildAsset")
		query.Preload("AssetLinked.ChildAsset.Asset")
	}

	if preload.AssetLinkedParentAsset {
		query.Preload("AssetLinked.ParentAsset")
		query.Preload("AssetLinked.ParentAsset.Asset")
		query.Preload("AssetLinked.ParentAsset.Asset.AssetModel")
		query.Preload("AssetLinked.ParentAsset.Asset.AssetModel.Brand")
		query.Preload("AssetLinked.ParentAsset.Asset.Location")
		query.Preload("AssetLinked.ParentAsset.Asset.CustomAssetCategory")
	}

	if preload.AssetLinkedAssetParent {
		query.Preload("AssetLinked.AssetParent")
	} // AssetLinkedAssetParent

	if preload.AssetLinkedLinkedAssetType {
		query.Preload("AssetLinked.LinkedAssetType")
	}
}

func enrichAssetLinkedTyresWithWhere(query *gorm.DB, where models.AssetLinkedVehicleTyreWhere) {
	if where.ParentAssetID != "" {
		query.Where("ams_linked_assets.parent_asset_id = ?", where.ParentAssetID)
	}

	if len(where.ParentAssetIDs) > 0 {
		query.Where("ams_linked_assets.parent_asset_id IN ?", where.ParentAssetIDs)
	}

	if len(where.ChildAssetIDs) > 0 {
		query.Where("ams_linked_assets.child_asset_id IN ?", where.ChildAssetIDs)
	}

	if where.ClientID != "" {
		query.Where("ams_linked_asset_vehicle_tyres.client_id = ?", where.ClientID)
	}

	if where.ChildAssetID != "" {
		query.Where("ams_linked_assets.child_asset_id = ?", where.ChildAssetID)
	}

	if where.TyrePosition != 0 {
		query.Where("ams_linked_asset_vehicle_tyres.tyre_position = ?", where.TyrePosition)
	}

	if where.NotChildAssetID != "" {
		query.Where("ams_linked_assets.child_asset_id != ?", where.NotChildAssetID)
	}
}

func (r *AssetLinkedRepository) GetAssetLinkedTyresV2(ctx context.Context, dB database.DBI, condition models.AssetLinkedVehicleTyreCondition) ([]models.AssetLinkedAssetVehicleTyre, error) {
	assetLinkedRecords := []models.AssetLinkedAssetVehicleTyre{}
	query := dB.GetTx().Model(assetLinkedRecords).
		Joins("JOIN ams_linked_assets ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id").
		Where("unlinked_datetime IS NULL")

	enrichAssetLinkedTyresWithWhere(query, condition.Where)
	enrichAssetLinkedTyresWithPreload(query, condition.Preload)

	query.Order("ams_linked_asset_vehicle_tyres.updated_at DESC")

	if err := query.Find(&assetLinkedRecords).Error; err != nil {
		return nil, err
	}

	return assetLinkedRecords, nil
}

func (r *AssetLinkedRepository) GetAssetLinkedTyre(ctx context.Context, dB database.DBI, condition models.AssetLinkedVehicleTyreCondition) (*models.AssetLinkedAssetVehicleTyre, error) {
	assetLinkedRecords := models.AssetLinkedAssetVehicleTyre{}
	query := dB.GetTx().Model(&models.AssetLinkedAssetVehicleTyre{}).
		Joins("JOIN ams_linked_assets ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id").
		Where("unlinked_datetime IS NULL")

	enrichAssetLinkedTyresWithWhere(query, condition.Where)
	enrichAssetLinkedTyresWithPreload(query, condition.Preload)

	query.Order("ams_linked_asset_vehicle_tyres.updated_at DESC")

	if err := query.First(&assetLinkedRecords).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errorhandler.ErrDataNotFound("asset linked")
		}
		return nil, err
	}

	return &assetLinkedRecords, nil
}

func (r *AssetLinkedRepository) GetAssetLinkedByID(ctx context.Context, dB database.DBI, id string) (*models.AssetLinked, error) {
	var assetLinked models.AssetLinked
	if err := dB.GetOrm().Model(&models.AssetLinked{}).
		Where("id = ?", id).Preload("ChildAsset").Preload("ParentAsset").
		First(&assetLinked).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("Asset Linked not found")
		}
		return nil, err
	}
	return &assetLinked, nil
}

func (r *AssetLinkedRepository) GetAssetLinkedList(ctx context.Context, dB database.DBI, param models.GetAssetLinkedGeneralListParam) (int, []models.AssetLinked, error) {
	var totalRecords int64
	assetLinkeds := []models.AssetLinked{}
	query := dB.GetTx().Model(&assetLinkeds)
	enrichAssetLinkedWithWhere(query, param.Cond.Where)
	enrichAssetLinkedWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Where("LOWER(linked_asset_type_code) LIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assetLinkeds).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assetLinkeds, nil

}

func (r *AssetLinkedRepository) GetAssetLinkedByChildAssetIds(ctx context.Context, dB database.DBI, assetLinkeds *[]models.AssetLinked, ids []string) error {
	if err := dB.GetOrm().Select("*, max(updated_at) as max_updated_at").Preload("ParentAsset").
		Where("child_asset_id IN ? AND unlinked_datetime IS NULL", ids).Group("id").
		Find(&assetLinkeds).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("Asset Linked not found")
		}
		return err
	}
	return nil
}

func (r *AssetLinkedRepository) GetAssetLinkedAssetVehiceTyreByID(ctx context.Context, dB database.DBI, id string) (*models.AssetLinkedAssetVehicleTyre, error) {
	var AssetLinkedAssetVehicleTyre models.AssetLinkedAssetVehicleTyre
	if err := dB.GetOrm().Model(&models.AssetLinkedAssetVehicleTyre{}).
		Where("asset_linked_id = ?", id).
		First(&AssetLinkedAssetVehicleTyre).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("Asset Vehicle Tyre Linked not found")
		}
		return nil, err
	}
	return &AssetLinkedAssetVehicleTyre, nil
}

func (r *AssetLinkedRepository) CreateAssetLinked(ctx context.Context, dB database.DBI, assetLinked *models.AssetLinked) error {
	return dB.GetTx().Create(assetLinked).Error
}

func (r *AssetLinkedRepository) CreateAssetLinkeds(ctx context.Context, dB database.DBI, assetLinkeds []models.AssetLinked) error {
	return dB.GetTx().Create(&assetLinkeds).Error
}

func (r *AssetLinkedRepository) CreateAssetLinkedAssetVehiceTyre(ctx context.Context, dB database.DBI, AssetLinkedAssetVehicleTyre *models.AssetLinkedAssetVehicleTyre) error {
	return dB.GetTx().Create(AssetLinkedAssetVehicleTyre).Error
}

func (r *AssetLinkedRepository) UpdateAssetLinked(ctx context.Context, dB database.DBI, assetLinked *models.AssetLinked) error {

	existingAssetLinked, err := r.GetAssetLinkedByID(ctx, dB, assetLinked.ID)
	if err != nil {
		return err
	}

	existingAssetLinked.ChildAssetID = assetLinked.ChildAssetID
	existingAssetLinked.ParentAssetID = assetLinked.ParentAssetID
	existingAssetLinked.LinkedDatetime = assetLinked.LinkedDatetime
	existingAssetLinked.UnlinkedDatetime = assetLinked.UnlinkedDatetime
	existingAssetLinked.ClientID = assetLinked.ClientID
	existingAssetLinked.LinkedAssetTypeCode = assetLinked.LinkedAssetTypeCode

	if err := dB.GetTx().Save(existingAssetLinked).Error; err != nil {
		return err
	}

	return nil
}

func (r *AssetLinkedRepository) UpdateAssetLinkedV2(ctx context.Context, dB database.DBI, id string, assetLinked *models.AssetLinked) error {
	return dB.GetTx().Where("id = ?", id).Updates(assetLinked).Error
}

func (r *AssetLinkedRepository) UpdateAssetLinkedAssetVehicleTyre(ctx context.Context, dB database.DBI, assetLinkedAssetVehicleTyre *models.AssetLinkedAssetVehicleTyre) error {
	existingAssetLinkedAssetVehicleTyre, err := r.GetAssetLinkedAssetVehiceTyreByID(context.Background(), dB, assetLinkedAssetVehicleTyre.AssetLinkedID)
	if err != nil {
		return err
	}

	existingAssetLinkedAssetVehicleTyre.TyrePosition = assetLinkedAssetVehicleTyre.TyrePosition
	existingAssetLinkedAssetVehicleTyre.OnLinkedVehicleKm = assetLinkedAssetVehicleTyre.OnLinkedVehicleKm
	existingAssetLinkedAssetVehicleTyre.OnLinkedTyreKm = assetLinkedAssetVehicleTyre.OnLinkedTyreKm
	existingAssetLinkedAssetVehicleTyre.OnUnlinkedVehicleKm = assetLinkedAssetVehicleTyre.OnUnlinkedVehicleKm
	existingAssetLinkedAssetVehicleTyre.OnUnlinkedTyreKm = assetLinkedAssetVehicleTyre.OnUnlinkedTyreKm
	existingAssetLinkedAssetVehicleTyre.OnLinkedVehicleHm = assetLinkedAssetVehicleTyre.OnLinkedVehicleHm
	existingAssetLinkedAssetVehicleTyre.OnLinkedTyreHm = assetLinkedAssetVehicleTyre.OnLinkedTyreHm
	existingAssetLinkedAssetVehicleTyre.OnUnlinkedVehicleHm = assetLinkedAssetVehicleTyre.OnUnlinkedVehicleHm
	existingAssetLinkedAssetVehicleTyre.OnUnlinkedTyreHm = assetLinkedAssetVehicleTyre.OnUnlinkedTyreHm
	existingAssetLinkedAssetVehicleTyre.ClientID = assetLinkedAssetVehicleTyre.ClientID

	if err := dB.GetTx().Save(existingAssetLinkedAssetVehicleTyre).Error; err != nil {
		return err
	}

	return nil
}

func (r *AssetLinkedRepository) UpdateAssetLinkedAssetVehicleTyreV2(
	ctx context.Context, dB database.DBI, assetLinkedAssetVehicleTyre *models.AssetLinkedAssetVehicleTyre,
) error {
	return dB.GetTx().
		Where("asset_linked_id = ?", assetLinkedAssetVehicleTyre.AssetLinkedID).
		Updates(assetLinkedAssetVehicleTyre).Error
}

func (r *AssetLinkedRepository) GetAssetTyrePosition(ctx context.Context, dB database.DBI, ids []string) ([]dtos.AssetTyrePosition, error) {
	response := make([]dtos.AssetTyrePosition, 0)
	err := dB.GetOrm().Table("ams_asset_tyres as at").
		Select(
			"al.id AS asset_linked_id",
			"al.child_asset_id",
			"al.parent_asset_id",
			"vt.tyre_position as tyre_position",
		).
		Joins("LEFT JOIN ams_linked_assets al ON at.asset_id=al.child_asset_id").
		Joins("INNER JOIN ams_linked_asset_vehicle_tyres vt ON al.id=vt.asset_linked_id").
		Where("al.child_asset_id IN ? AND unlinked_datetime IS NULL", ids).Find(&response).Error
	return response, err
}

func (r *AssetLinkedRepository) GetAssetTyrePositionById(ctx context.Context, dB database.DBI, id string) (int, error) {
	response := dtos.AssetTyrePosition{}
	err := dB.GetOrm().Table("ams_asset_tyres as at").Select("at.asset_id as id,vt.tyre_position as tyre_position").
		Joins("LEFT JOIN ams_linked_assets al ON at.asset_id=al.child_asset_id").
		Joins("INNER JOIN ams_linked_asset_vehicle_tyres vt ON al.id=vt.asset_linked_id").
		Where("al.child_asset_id = ? AND unlinked_datetime IS NULL", id).Find(&response).Error
	result := response.TyrePosition
	return result, err
}

func (r *AssetLinkedRepository) GetAssetLinkedTyreHistory(ctx context.Context, dB database.DBI, req models.GetAssetLinkedListParam) (int, []models.AssetLinkedAssetVehicleTyre, error) {
	var totalRecords int64
	assetLinkedRecords := []models.AssetLinkedAssetVehicleTyre{}
	query := dB.GetOrm().Model(assetLinkedRecords).
		Joins("JOIN ams_linked_assets ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id")

	enrichAssetLinkedTyresWithWhere(query, req.Cond.Where)
	enrichAssetLinkedTyresWithPreload(query, req.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assetLinkedRecords, nil
	}

	query.Order("ams_linked_asset_vehicle_tyres.updated_at DESC")
	offset := (req.PageNo - 1) * req.PageSize
	err = query.Offset(offset).Limit(req.PageSize).Find(&assetLinkedRecords).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assetLinkedRecords, nil
}

func (r *AssetLinkedRepository) GetAssetLinkedVehicleHistory(ctx context.Context, dB database.DBI, req models.GetAssetLinkedListParam) (int, []models.AssetLinkedAssetVehicleTyre, error) {
	var totalRecords int64
	assetLinkedRecords := []models.AssetLinkedAssetVehicleTyre{}
	query := dB.GetOrm().Model(assetLinkedRecords).
		Joins("JOIN ams_linked_assets ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id").
		Preload("AssetLinked").
		Preload("AssetLinked.ChildAsset").
		Preload("AssetLinked.ChildAsset.Tyre").
		Preload("AssetLinked.ChildAsset.Asset").
		Preload("AssetLinked.ChildAsset.Asset.Brand").
		Preload("AssetLinked.ParentAsset").
		Preload("AssetLinked.LinkedAssetType")

	if req.Cond.Where.ParentAssetID != "" {
		query.Where("ams_linked_assets.parent_asset_id = ?", req.Cond.Where.ParentAssetID)
	}

	if req.Cond.Where.ClientID != "" {
		query.Where("ams_linked_asset_vehicle_tyres.client_id = ?", req.Cond.Where.ClientID)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assetLinkedRecords, nil
	}

	query.Order("ams_linked_asset_vehicle_tyres.updated_at DESC")
	offset := (req.PageNo - 1) * req.PageSize
	err = query.Offset(offset).Limit(req.PageSize).Find(&assetLinkedRecords).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assetLinkedRecords, nil
}

func (r *AssetLinkedRepository) UnlinkAssetLinkedByIDs(ctx context.Context, dB database.DBI, IDs []string, timeNow time.Time) error {
	return dB.GetTx().
		Where("id IN ?", IDs).
		Updates(&models.AssetLinked{
			UnlinkedDatetime: &timeNow,
		}).
		Error
}

func (r *AssetLinkedRepository) UpdateClaimBonusPenaltyByIDs(ctx context.Context, dB database.DBI, IDs []string, cond bool) error {
	return dB.GetTx().Model(&models.AssetLinkedAssetVehicleTyre{}).
		Where("asset_linked_id IN ?", IDs).
		Updates(map[string]interface{}{
			"is_claimed_bonus_penalty": cond,
		}).
		Error
}

func (r *AssetLinkedRepository) GetAssetLinkedBonusPenaltyElig(ctx context.Context, dB database.DBI, req models.GetAssetLinkedListParam) (int, []dtos.AssetLinkedBonusPenaltyElig, error) {
	var totalRecords int64
	assetLinkedRecords := []dtos.AssetLinkedBonusPenaltyElig{}
	query := dB.GetOrm().Model(assetLinkedRecords).Table("ams_linked_asset_vehicle_tyres").
		Select(`ala.parent_asset_id, ala.child_asset_id, aat.tyre_id,
		ab.id AS brand_id, ab.brand_name AS brand_name,aa.serial_number AS serial_number,at.pattern_type AS pattern_type,
		MIN(ams_linked_asset_vehicle_tyres.on_linked_vehicle_km) AS first_on_linked_vehicle_km, 
		MAX(ams_linked_asset_vehicle_tyres.on_unlinked_vehicle_km) AS last_on_unlinked_vehicle_km, 
		MIN(ams_linked_asset_vehicle_tyres.on_linked_tyre_km) AS first_on_linked_tyre_km, 
		MAX(ams_linked_asset_vehicle_tyres.on_unlinked_tyre_km) AS last_on_unlinked_tyre_km, 
		SUM(ams_linked_asset_vehicle_tyres.on_unlinked_tyre_km - ams_linked_asset_vehicle_tyres.on_linked_tyre_km) AS total_linked_tyre_km,
		SUM(ams_linked_asset_vehicle_tyres.on_unlinked_vehicle_km - ams_linked_asset_vehicle_tyres.on_linked_vehicle_km) AS total_linked_vehicle_km,
		MIN(ams_linked_asset_vehicle_tyres.on_linked_vehicle_hm) AS first_on_linked_vehicle_hm, 
		MAX(ams_linked_asset_vehicle_tyres.on_unlinked_vehicle_hm) AS last_on_unlinked_vehicle_hm, 
		MIN(ams_linked_asset_vehicle_tyres.on_linked_tyre_hm) AS first_on_linked_tyre_hm, 
		MAX(ams_linked_asset_vehicle_tyres.on_unlinked_tyre_hm) AS last_on_unlinked_tyre_hm, 
		SUM(ams_linked_asset_vehicle_tyres.on_unlinked_tyre_hm - ams_linked_asset_vehicle_tyres.on_linked_tyre_hm) AS total_linked_tyre_hm,
		SUM(ams_linked_asset_vehicle_tyres.on_unlinked_vehicle_hm - ams_linked_asset_vehicle_tyres.on_linked_vehicle_hm) AS total_linked_vehicle_hm`).
		Joins("INNER JOIN ams_linked_assets AS ala ON ams_linked_asset_vehicle_tyres.asset_linked_id = ala.id").
		Joins("JOIN ams_assets AS aa ON ala.child_asset_id=aa.id").
		Joins("JOIN ams_brands AS ab ON aa.brand_id=ab.id").
		Joins("JOIN ams_asset_tyres AS aat ON aa.id=aat.asset_id").
		Joins("JOIN ams_tyres AS at ON aat.tyre_id=at.id").
		Where("ala.parent_asset_id = ? AND ala.unlinked_datetime IS NOT NULL AND  ams_linked_asset_vehicle_tyres.is_claimed_bonus_penalty = ?", req.Cond.Where.ParentAssetID, false).
		Group("ala.child_asset_id, ala.parent_asset_id,aat.tyre_id,ab.id,ab.brand_name,aa.serial_number,at.pattern_type")

	if req.Cond.Where.ClientID != "" {
		query.Where("ams_linked_asset_vehicle_tyres.client_id = ?", req.Cond.Where.ClientID)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assetLinkedRecords, nil
	}

	offset := (req.PageNo - 1) * req.PageSize
	err = query.Offset(offset).Limit(req.PageSize).Scan(&assetLinkedRecords).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assetLinkedRecords, nil
}
