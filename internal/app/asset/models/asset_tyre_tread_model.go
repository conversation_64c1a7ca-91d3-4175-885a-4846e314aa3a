package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type AssetTyreTread struct {
	commonmodel.ModelV2
	AssetID            string                `json:"asset_id"`
	ThreadSequence     int                   `json:"thread_sequence"`
	AverageRTD         float64               `json:"average_rtd"`
	TotalKM            int                   `json:"total_km"`
	TotalLifetime      int                   `json:"total_lifetime"`
	VendorName         null.String           `gorm:"default:null" json:"vendor_name"`
	BrandName          string                `gorm:"default:null" json:"brand_name"`
	RetreadType        string                `gorm:"default:null" json:"retread_type"`
	StartThreadDepth   float64               `json:"start_thread_depth"`
	OriginalTd         float64               `json:"original_td"`
	RetreadDate        time.Time             `json:"retread_date" gorm:"default:null"`
	Cost               int                   `json:"cost"`
	TotalHm            int                   `json:"total_hm"`
	TypeCode           string                `json:"type_code"`
	PartnerID          string                `json:"partner_id" gorm:"default:null"`
	TyresTreadConfigID null.String           `json:"tyres_tread_config_id" gorm:"default:null"`
	Type               AssetTyreTreadType    `gorm:"foreignKey:TypeCode;references:Code"`
	TreadConfig        AssetTyresTreadConfig `gorm:"foreignKey:TyresTreadConfigID;references:ID"`
	Notes              null.String           `json:"notes"`
}

func (AssetTyreTread) TableName() string {
	return "ams_asset_tyres_treads"
}

func (a *AssetTyreTread) BeforeCreate(tx *gorm.DB) error {
	a.SetUUID("att")
	a.ModelV2.BeforeCreate(tx)
	return nil
}

func (a *AssetTyreTread) BeforeUpdate(tx *gorm.DB) error {
	a.ModelV2.BeforeUpdate(tx)
	return nil
}

type AssetTyreTreadType struct {
	Code        string
	Label       string
	Description string
}

func (us *AssetTyreTreadType) TableName() string {
	return "ams_ASSET_TYRE_TREAD_TYPES"
}

type AssetTyreTreadCondition struct {
	Where   AssetTyreTreadWhere
	Columns []string
	Preload AssetTyreTreadPreload
}

type AssetTyreTreadWhere struct {
	ID            string
	AssetID       string
	ClientID      string
	IsLastest     bool
	Sequence      null.Int
	PartnerID     string
	TreadConfigID string
}

type AssetTyreTreadPreload struct {
}

type GetAssetTyreTreadListParam struct {
	commonmodel.ListRequest
	Cond AssetTyreTreadCondition
}
