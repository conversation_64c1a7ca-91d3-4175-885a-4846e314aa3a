package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
)

type AssetInspectionTyre struct {
	commonmodel.Model
	AssetInspectionID  string          `json:"asset_inspection_id" gorm:"type:varchar(40)"`
	AssetInspection    AssetInspection `json:"asset_inspection"`
	AssetTyreID        string          `json:"asset_tyre_id" gorm:"type:varchar(40);default:null"`
	AssetTyre          AssetTyre       `json:"asset_tyre" gorm:"foreignKey:AssetTyreID"`
	Remark             string          `json:"remark" gorm:"type:varchar(255)"`
	AssetAssignmentID  string          `json:"asset_assignment_id" gorm:"type:varchar(40);default:null"`
	Pressure           float64         `json:"pressure" gorm:"type:numeric(10,2);not null"`
	RDT1               float64         `json:"rdt1" gorm:"type:numeric(14,2)"`
	RDT2               float64         `json:"rdt2" gorm:"type:numeric(14,2)"`
	RDT3               float64         `json:"rdt3" gorm:"type:numeric(14,2)"`
	RDT4               float64         `json:"rdt4" gorm:"type:numeric(14,2)"`
	TyrePosition       float64         `json:"tyre_position" gorm:"type:numeric(10,2)"`
	AverageRTD         float64         `json:"average_rtd" gorm:"type:numeric(10,2)"`
	StartThreadDepth   float64         `json:"start_thread_depth" gorm:"type:numeric(10,2)"`
	ClientID           string          `json:"client_id" gorm:"type:varchar(40);not null"`
	PressureStatusCode string          `json:"pressure_status_code" gorm:"type:varchar(20);default:null"`
	TyreKM             int             `json:"tyre_km"`
	TyreHm             int             `json:"tyre_hm"`
	DeviceID           string          `json:"device_id" gorm:"default:null"`
	Temperature        null.Float      `json:"temperature" gorm:"default:null"`
	CustomRFID         string          `json:"custom_rfid" gorm:"column:custom_rfid;type:varchar(20)"`

	FailedVisualChecking   null.Bool   `json:"failed_visual_checking"`
	RequireRotationTyre    null.Bool   `json:"require_rotation_tyre"`
	RequireSpooringVehicle null.Bool   `json:"require_spooring_vehicle"`
	CustomSerialNumber     string      `json:"custom_serial_number" gorm:"default:null"`
	TireTreadAndRimDamage  null.Bool   `json:"tire_tread_and_rim_damage" gorm:"default:null"`
	CustomBrandName        null.String `json:"custom_brand_name"`
	CustomTyreSize         string      `gorm:"default:null" json:"custom_tyre_size"`

	AssetInspectionVehicle  *AssetInspectionVehicle `gorm:"foreignKey:AssetInspectionID;references:AssetInspectionID" json:"asset_inspection_vehicle"`
	NumberOfInspectionPoint int                     `gorm:"default:null"`
	PressureSensorRef       string                  `gorm:"default:null"`
	TemperatureSensorRef    string                  `gorm:"default:null"`
	IsMismatch              null.Bool               `gorm:"default:null"`
	DigispectConfigID       string                  `gorm:"default:null"`
	DigispectConfig         DigispectConfig
	SourceTypeCode          null.String `json:"source_type_code"`
}

func (a *AssetInspectionTyre) TableName() string {
	return "ams_asset_inspection_tyre"
}

func (a *AssetInspectionTyre) SetID() {
	a.SetUUID("ait")
}

type AssetInspectionTyreWhere struct {
	ClientID               string
	InspectionID           string
	AssetID                string
	StartDate              string
	EndDate                string
	StartDate1             time.Time
	EndDate1               time.Time
	StartDateTyre          time.Time
	EndDateTyre            time.Time
	AssetIDs               []string
	InspectByUserIDs       []string
	InspectByUserID        string
	ReferenceID            string
	ReferenceCode          string
	Direction              string
	SortBy                 string
	IsSingleTyreInspection bool
	HasPartnerOwnerID      bool
	InspectionIDs          []string
	DigispectConfID        string
	DigispectConfIDs       []string
	HasRTD                 bool
	HasPressure            bool
	HasTemperature         bool
	DigiSpectOnlySource    bool
}

type AssetInspectionTyrePreload struct {
	AssetInspection                  bool
	AssetTyre                        bool
	AssetTyreAsset                   bool
	AssetTyreAssetBrand              bool
	AssetTyreTyre                    bool
	AssetInspectionVehicle           bool
	AssetInspectionVehicleAsset      bool
	AssetInspectionVehicleAssetBrand bool
	AssetInspectionVehicleAssetModel bool
	DigispectConfig                  bool
}

type AssetInspectionTyreCondition struct {
	Where   AssetInspectionTyreWhere
	Preload AssetInspectionTyrePreload
	Columns []string
}

type GetAssetInspectionTyreListParam struct {
	commonmodel.ListRequest
	Cond AssetInspectionTyreCondition
}
