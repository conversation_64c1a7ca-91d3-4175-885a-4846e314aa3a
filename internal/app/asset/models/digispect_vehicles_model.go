package models

import (
	"assetfindr/pkg/common/commonmodel"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type DigispectVehicle struct {
	commonmodel.ModelV2
	ReferenceNumber      string       `json:"reference_number"`
	PartnerOwnerName     null.String  `json:"partner_owner_name" gorm:"default:null"`
	DigispectBrandID     *null.String `json:"digispect_brand_id" gorm:"default:null"`
	DigispectBrandName   null.String  `json:"digispect_brand_name"`
	DigispectConfigID    *null.String `json:"digispect_config_id" gorm:"default:null"`
	DigispectConfigModel null.String  `json:"digispect_config_model"`
	Photo                null.String  `json:"photo"`
	AxleConfiguration    pgtype.JSONB `json:"axle_configuration" gorm:"type:jsonb"`

	InspectionVechicles []AssetInspectionVehicle `gorm:"foreignKey:DigispectVehicleID;references:ID"`
}

func (DigispectVehicle) TableName() string {
	return "ams_digispect_vehicles"
}

func (l *DigispectVehicle) BeforeCreate(db *gorm.DB) error {
	l.SetUUID("dvh")
	l.ModelV2.BeforeCreate(db)
	return nil
}

func (l *DigispectVehicle) BeforeUpdate(db *gorm.DB) error {
	l.ModelV2.BeforeUpdate(db)
	return nil
}

// DigispectVehicleParam represents the parameters for querying digispect vehicles
type GetDigispectVehicleListParam struct {
	commonmodel.ListRequest
	Cond DigispectVehicleCondition
}

// DigispectVehicleCondition represents the conditions for querying digispect vehicles
type DigispectVehicleCondition struct {
	Where   DigispectVehicleWhere
	Preload DigispectVehiclePreload
}

type DigispectVehiclePreload struct {
	InspectionVechicles        bool
	InspectionVechiclesVehicle bool
}

// DigispectVehicleWhere represents the where conditions for querying digispect vehicles
type DigispectVehicleWhere struct {
	ID                   string
	IDs                  []string
	ReferenceNumber      string
	LowerReferenceNumber string
	ClientID             string
	WithOrmDeleted       bool
}

// GetDigispectVehicleCustomerListParam represents the parameters for getting digispect vehicle customers
type GetDigispectVehicleCustomerListParam struct {
	commonmodel.ListRequest
	ClientID string
}
