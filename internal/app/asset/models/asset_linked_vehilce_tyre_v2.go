package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

// AssetVehicleMeterState represents a historical record of vehicle meter readings
type AssetVehicleMeterState struct {
	commonmodel.ModelV2
	AssetID           string       `json:"asset_id" gorm:"column:asset_id;type:varchar(40);not null"`
	VehicleKm         int          `json:"vehicle_km" gorm:"column:vehicle_km;type:bigint"`
	VehicleHm         int          `json:"vehicle_hm" gorm:"column:vehicle_hm;type:bigint"`
	DateTime          time.Time    `json:"date_time" gorm:"column:date_time;type:timestamptz;not null;default:now()"`
	AxleConfiguration pgtype.JSONB `json:"axle_configuration" gorm:"column:axle_configuration;type:jsonb"`
}

// TableName specifies the table name for AssetVehicleMeterState
func (AssetVehicleMeterState) TableName() string {
	return "ams_asset_vehicle_meter_states"
}

// Before<PERSON><PERSON> sets the ID with a prefix
func (m *AssetVehicleMeterState) BeforeCreate(tx *gorm.DB) error {
	m.SetUUID("avs")
	return m.ModelV2.BeforeCreate(tx)
}

// LinkedVehicleTyreV2 represents a relationship between a vehicle and a tyre
type LinkedVehicleTyreV2 struct {
	commonmodel.ModelV2
	ParentAssetID        string      `json:"parent_asset_id" gorm:"column:parent_asset_id;type:varchar(40);not null"`
	ChildAssetID         string      `json:"child_asset_id" gorm:"column:child_asset_id;type:varchar(40);not null"`
	TyrePosition         int         `json:"tyre_position" gorm:"column:tyre_position;type:integer;not null"`
	IsSpare              bool        `json:"is_spare" gorm:"column:is_spare;type:boolean;not null;default:false"`
	LinkedMeterStateID   string      `json:"linked_meter_state_id" gorm:"column:linked_meter_state_id;type:varchar(40)"`
	UnlinkedMeterStateID null.String `json:"unlinked_meter_state_id" gorm:"column:unlinked_meter_state_id;type:varchar(40)"`

	// Relationships
	ParentAsset        *Asset                  `json:"parent_asset,omitempty" gorm:"foreignKey:ParentAssetID"`
	ChildAsset         *Asset                  `json:"child_asset,omitempty" gorm:"foreignKey:ChildAssetID"`
	LinkedMeterState   AssetVehicleMeterState  `json:"linked_meter_state,omitempty" gorm:"foreignKey:LinkedMeterStateID"`
	UnlinkedMeterState *AssetVehicleMeterState `json:"unlinked_meter_state,omitempty" gorm:"foreignKey:UnlinkedMeterStateID"`
}

// TableName specifies the table name for LinkedVehicleTyreV2
func (LinkedVehicleTyreV2) TableName() string {
	return "ams_linked_vehicle_tyres_v2"
}

// BeforeCreate sets the ID with a prefix
func (m *LinkedVehicleTyreV2) BeforeCreate(tx *gorm.DB) error {
	m.SetUUID("lvt")
	return m.ModelV2.BeforeCreate(tx)
}
