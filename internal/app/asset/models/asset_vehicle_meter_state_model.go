package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type AssetVehicleMeterState struct {
	commonmodel.ModelV2
	AssetID           string       `json:"asset_id" gorm:"column:asset_id;type:varchar(40);not null"`
	VehicleKM         int          `json:"vehicle_km" gorm:"column:vehicle_km;type:bigint"`
	VehicleHm         int          `json:"vehicle_hm" gorm:"column:vehicle_hm;type:bigint"`
	DateTime          time.Time    `json:"date_time" gorm:"column:date_time;type:timestamptz;not null;default:now()"`
	AxleConfiguration pgtype.JSONB `json:"axle_configuration" gorm:"column:axle_configuration;type:jsonb"`
	IsLast            bool         `json:"is_last" gorm:"column:is_last;type:boolean;default:false"`
	DeleteReason      null.String  `json:"delete_reason" gorm:"column:delete_reason;type:varchar(255);default:null"`

	AssetLinkeds   []AssetLinked `json:"asset_linkeds" gorm:"foreignKey:LinkedStateID;references:ID"`
	AssetUnlinkeds []AssetLinked `json:"asset_unlinkeds" gorm:"foreignKey:UnlinkedStateID;references:ID"`
	Asset          Asset         `json:"asset" gorm:"foreignKey:AssetID;references:ID"`
	AssetVehicle   AssetVehicle  `json:"asset_vehicle" gorm:"foreignKey:AssetID;references:ID"`
}

// TableName specifies the table name for AssetVehicleMeterState
func (AssetVehicleMeterState) TableName() string {
	return "ams_asset_vehicle_meter_states"
}

// BeforeCreate sets the ID with a prefix
func (m *AssetVehicleMeterState) BeforeCreate(tx *gorm.DB) error {
	m.SetUUID("avs")
	return m.ModelV2.BeforeCreate(tx)
}

type AssetVehicleMeterStateWhere struct {
	ID        string
	AssetID   string
	ClientID  string
	DateTime  time.Time
	VehicleKM null.Int
	VehicleHm null.Int
}

type AssetVehicleMeterStatePreload struct {
	AssetLinkeds   bool
	AssetUnlinkeds bool
	Asset          bool

	AssetVehicle bool
}

type AssetVehicleMeterStateCondition struct {
	Where   AssetVehicleMeterStateWhere
	Preload AssetVehicleMeterStatePreload
	Columns []string
}

type GetAssetVehicleMeterStateListParam struct {
	commonmodel.ListRequest
	Cond AssetVehicleMeterStateCondition
}
