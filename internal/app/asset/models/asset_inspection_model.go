package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
)

type AssetInspectionReference struct {
	Code        string `gorm:"primaryKey;type:varchar(20)" json:"code"`
	Label       string `json:"label" gorm:"type:varchar(20);not null"`
	Description string `json:"description" gorm:"type:varchar(50);not null"`
}

func (us *AssetInspectionReference) TableName() string {
	return "ams_ASSET_INSPECTION_REFERENCE"
}

type AssetInspection struct {
	commonmodel.Model
	InspectionNumber       string                   `json:"inspection_number"`
	InspectByUserID        string                   `json:"inspect_by_user_id" gorm:"type:varchar(40);not null"`
	ReferenceID            string                   `json:"reference_id" gorm:"default:null"`
	ReferenceCode          string                   `json:"reference_code" gorm:"default:null"`
	ClientID               string                   `json:"client_id" gorm:"type:varchar(40);not null"`
	StatusCode             string                   `json:"status_code" gorm:"default:'OPEN'"`
	LocationLat            null.Float               `json:"location_lat"`
	LocationLong           null.Float               `json:"location_long"`
	LocationLabel          string                   `json:"location_label"`
	Reference              AssetInspectionReference `json:"reference"`
	Status                 AssetInspectionStatus
	AssetInspectionTyre    *AssetInspectionTyre
	AssetInspectionTyres   []AssetInspectionTyre
	AssetInspectionVehicle *AssetInspectionVehicle

	DisplayFormationCode            string                               `json:"display_formation_codes" gorm:"default:null"`
	AssetInspectionDisplayFormation AssetInspectionDisplayFormationCodes `json:"display_formation" gorm:"foreignKey:DisplayFormationCode;references:Code"`

	CountInspectionTyres CountInspectionTyres `gorm:"foreignKey:ID;references:AssetInspectionID"`
}

type CountInspectionTyres struct {
	AssetInspectionID    string
	NumberInspectionTyre int `gorm:"column:number_inspection_tyre"`
}

func (CountInspectionTyres) TableName() string {
	return "ams_asset_inspection_tyre"
}

func (a *AssetInspection) TableName() string {
	return "ams_asset_inspections"
}

func (a *AssetInspection) SetID() {
	a.SetUUID("asi")
}

type AssetInspectionStatus struct {
	Code        string `gorm:"primaryKey" json:"code"`
	Label       string `json:"label"`
	Description string `json:"description"`
}

func (ps AssetInspectionStatus) TableName() string {
	return "ams_ASSET_INSPECTION_STATUSES"
}

type InspectionLocationChart struct {
	Name string      `json:"name"`
	Code null.String `json:"code"`
	Lat  float64     `json:"location_lat"`
	Long float64     `json:"location_long"`
}

type AssetInspectionWhere struct {
	ID                       string
	UserID                   string
	ClientID                 string
	StartDate                string
	StartDate1               time.Time
	EndDate                  string
	EndDate1                 time.Time
	ReferenceID              string
	ReferenceCode            string
	ReferenceCodeExclude     string
	StatusCodes              []string
	IsAssignToUserLogin      bool
	HasVehicleInspection     *bool
	HasTyreInspection        *bool
	HasTyreOptimaxInspection *bool
	TyreSourceCodes          []string
	InspectByUserIDs         []string
	InspectByUserID          string
	DigiSpectOnlySource      bool
	Limit                    int
	DigiSpectVehicleID       string
}

type AssetInspectionPreload struct {
	Reference                    bool
	CompleteInspectionItemDetail bool
	CountInspectionTyres         bool
	AssetInspectionTyres         bool
	AssetInspectionVehicle       bool
}

type AssetInspectionCondition struct {
	Where   AssetInspectionWhere
	Preload AssetInspectionPreload
	Columns []string
}

type GetAssetInspectionListParam struct {
	commonmodel.ListRequest
	Cond AssetInspectionCondition
}

type AssetInspectionSourceTypes struct {
	Code        string `gorm:"primaryKey" json:"code"`
	Label       string `json:"label"`
	Description string `json:"description"`
}

func (ps AssetInspectionSourceTypes) TableName() string {
	return "ams_ASSET_INSPECTION_SOURCE_TYPES"
}

type AssetInspectionDisplayFormationCodes struct {
	Code        string `gorm:"primaryKey" json:"code"`
	Label       string `json:"label"`
	Description string `json:"description"`
}

func (a AssetInspectionDisplayFormationCodes) TableName() string {
	return "ams_ASSET_INPECTION_DISPLAY_FORMATION_CODES"
}

type InspectionChartReq struct {
	ClientID      string
	StartDatetime time.Time
	EndDatetime   time.Time
}
