package models

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type AssetTyre struct {
	commonmodel.ModelV2NoIDField
	AssetID                         string      `json:"asset_id" gorm:"type:varchar(40);primaryKey" pgjsonb:"asset_id"`
	Asset                           Asset       `json:"asset"`
	DatetimeOfLastCheck             time.Time   `json:"datetime_of_last_check" pgjsonb:"datetime_of_last_check"`
	LastStockDate                   time.Time   `json:"last_stock_date"`
	AverageRTD                      float64     `json:"average_rtd" gorm:"type:numeric(10,2)" pgjsonb:"average_rtd"`
	TotalKM                         int         `json:"total_km" gorm:"type:int" pgjsonb:"total_km"`
	RetreadNumber                   int         `json:"retread_number" gorm:"type:int" pgjsonb:"retread_number"`
	RepairedNumber                  int         `json:"repaired_number" gorm:"type:int" pgjsonb:"repaired_number"`
	DOTCode                         null.String `json:"dot_code" gorm:"type:varchar(100)" pgjsonb:"dot_code"`
	DateCode                        null.String `json:"date_code" gorm:"type:varchar(100)" pgjsonb:"date_code"`
	TyreID                          string      `json:"tyre_id" gorm:"type:varchar(40)" pgjsonb:"tyre_id"`
	StartThreadDepth                float64     `json:"start_thread_depth" gorm:"type:numeric(10,2)" pgjsonb:"start_thread_depth"`
	OriginalTd                      float64     `json:"original_td" gorm:"type:numeric(10,2)" pgjsonb:"original_td"`
	TotalLifetime                   int         `json:"total_lifetime" pgjsonb:"total_lifetime"`
	TotalHm                         int         `json:"total_hm" pgjsonb:"total_hm"`
	MeterCalculationCode            null.String `json:"meter_calculation_code" pgjson:"meter_calculation_code" gorm:"default:null"`
	AverageRtdLastUpdatedAt         null.Time   `json:"average_rtd_last_updated_at"`
	Pressure                        null.Float  `json:"pressure"`
	PressureLastUpdatedAt           null.Time   `json:"pressure_last_updated_at"`
	Temperature                     null.Float  `json:"temperature"`
	TemperatureLastUpdatedAt        null.Time   `json:"temperature_last_updated_at"`
	PressureLastUpdatedSensorRef    string      `json:"pressure_last_updated_sensor_ref" gorm:"default:null"`
	TemperatureLastUpdatedSensorRef string      `json:"temperature_last_updated_sensor_ref" gorm:"default:null"`

	UtilizationRatePercentage           float64 `json:"utilization_rate_percentage" gorm:"default:null"`
	UtilizationRatePercentageStatusCode string  `json:"utilization_rate_percentage_status_code" gorm:"default:null"`

	Rtd1 float64 `json:"rtd1" gorm:"default:null"`
	Rtd2 float64 `json:"rtd2" gorm:"default:null"`
	Rtd3 float64 `json:"rtd3" gorm:"default:null"`
	Rtd4 float64 `json:"rtd4" gorm:"default:null"`

	HasNotSetRTD            null.Bool `json:"has_not_set_rtd" gorm:"default:false"`
	PrevKmHmDataUnavailable null.Bool `json:"prev_km_hm_data_unavailable"`
	PrevTotalKM             null.Int  `json:"prev_total_km"`
	PrevTotalHm             null.Int  `json:"prev_total_hm"`
	FirstInstalledDatetime  null.Time `json:"first_installed_datetime" gorm:"default:null"`
	LastInspectedAt         null.Time `json:"last_inspected_at" gorm:"default:null"`

	Tyre                    Tyre             `json:"tyre"`
	AssetLinked             *AssetLinked     `json:"asset_linked" gorm:"foreignkey:AssetID;references:ChildAssetID"`
	LastUnlinkedAssetLinked *AssetLinked     `json:"last_unlinked_asset_linked" gorm:"foreignkey:AssetID;references:ChildAssetID"`
	RetreadTyre             []AssetTyreTread `json:"retread_tyres" gorm:"foreignkey:AssetID"`

	StatusRequestScrapped *AssetStatusRequest `json:"status_request_scrapped" gorm:"foreignKey:AssetID;references:AssetID"`
	StatusRequestDisposed *AssetStatusRequest `json:"status_request_disposed" gorm:"foreignKey:AssetID;references:AssetID"`

	UtilizationRatePercentageStatus UtilizationRatePercentageStatus `json:"utilization_rate_percentage_status" gorm:"foreignKey:UtilizationRatePercentageStatusCode;references:Code"`
}

func (a *AssetTyre) TableName() string {
	return "ams_asset_tyres"
}

func (a AssetTyre) LatestTread() AssetTyreTread {
	return a.RetreadTyre[len(a.RetreadTyre)-1]
}

func (a *AssetTyre) BeforeCreate(db *gorm.DB) error {
	a.ModelV2NoIDField.BeforeCreate(db)
	return nil
}

func (a *AssetTyre) BeforeUpdate(db *gorm.DB) error {
	a.ModelV2NoIDField.BeforeUpdate(db)
	return nil
}

func (a *AssetTyre) UseHM() bool {
	return a.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_HM
}

func (a *AssetTyre) UseKM() bool {
	return a.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM
}

func (a *AssetTyre) SumLifeTimeKM() int {
	sum := 0
	for i := range a.RetreadTyre {
		sum += a.RetreadTyre[i].TotalKM
	}

	return sum
}

func (a *AssetTyre) SumLifeTimeHM() float64 {
	sum := 0
	for i := range a.RetreadTyre {
		sum += a.RetreadTyre[i].TotalHm
	}

	return calculationhelpers.Div100(sum)
}

func (a *AssetTyre) TreadWearRateKm() float64 {
	treadWear := a.StartThreadDepth - a.AverageRTD
	if treadWear <= 0 {
		return 0
	}

	return float64(a.TotalKM) / treadWear
}

func (a *AssetTyre) TreadWearRateHm() float64 {
	treadWear := a.StartThreadDepth - a.AverageRTD
	if treadWear <= 0 {
		return 0
	}

	return calculationhelpers.Div100(a.TotalHm) / treadWear
}

type AssetTyreWhere struct {
	ClientID          string
	AssetID           string
	AssetIDs          []string
	StatusCodes       []string
	BrandIDs          []string
	AssignedUserIds   []string
	TyreSizes         []string
	PatternTypes      []string
	TyreID            string
	HasPartnerOwnerID null.Bool
	PartnerOwnerID    string
	Rfid              []string
	IsWorkshop        null.Bool

	ParentCustomAssetSubcategoryIDs []string
	LastInspectedBeforeDate         time.Time

	UtilizationRateStatusCodes []string
	MeterCalculationCodeOrNil  string
}

type AssetTyreCondition struct {
	Where       AssetTyreWhere
	Columns     []string
	Preload     AssetTyrePreload
	IsForUpdate bool
}

type AssetTyrePreload struct {
	Tyre                   bool
	Asset                  bool
	AssetBrand             bool
	AssetStatus            bool
	AssetLinkedVehicle     bool
	RetreadTyre            bool
	RetreadTyreType        bool
	RetreadTyreTreadConfig bool

	UtilizationRateStatus bool

	StatusRequestScrapped bool
	StatusRequestDisposed bool
}

type GetAssetTyreListParam struct {
	commonmodel.ListRequest
	Cond AssetTyreCondition

	IsUnlinkTyre bool
}

type GetAssetTyreScrappedDisposedListParam struct {
	commonmodel.ListRequest
	Cond                        AssetTyreCondition
	StatusRequestReasonCodes    []string
	StatusRequestSubReasonCodes []string
	StatusRequestGradeCodes     []string
}

type GetAssetTyreInstalledReportParam struct {
	commonmodel.ListRequest
	Cond AssetTyreInstalledCondition
}

type AssetTyreInstalledWhere struct {
	ParentAssetIDs             []string
	ClientID                   string
	BrandIDs                   []string
	TyreSizes                  []string
	PatternTypes               []string
	UtilizationRateStatusCodes []string
}

type AssetTyreInstalledCondition struct {
	Where       AssetTyreInstalledWhere
	Columns     []string
	Preload     AssetTyreInstalledPreload
	IsForUpdate bool
}

type AssetTyreInstalledPreload struct {
	Tyre               bool
	RetreadTyre        bool
	Asset              bool
	AssetBrand         bool
	AssetLinkedVehicle bool
}

type GetAssetTyreUninstalledReportParam struct {
	commonmodel.ListRequest
	Cond AssetTyreUninstalledCondition
}

type AssetTyreUninstalledWhere struct {
	ParentAssetIDs                  []string
	ClientID                        string
	BrandIDs                        []string
	TyreSizes                       []string
	PatternTypes                    []string
	StatusCodes                     []string
	UtilizationRateStatusCodes      []string
	ParentCustomAssetSubcategoryIDs []string
}

type AssetTyreUninstalledCondition struct {
	Where       AssetTyreUninstalledWhere
	Columns     []string
	IsForUpdate bool
}

type GetAssetTyreReplacementForecastReportParam struct {
	commonmodel.ListRequest
	Cond AssetTyreReplacementForecastCondition
}

type AssetTyreReplacementForecastWhere struct {
	ClientID string
}

type AssetTyreReplacementForecastCondition struct {
	Where       AssetTyreReplacementForecastWhere
	Columns     []string
	IsForUpdate bool
}

type GetAssetTyreUsageReportParam struct {
	commonmodel.ListRequest
	Cond AssetTyreUsageCondition
}

type AssetTyreUsageWhere struct {
	StartDate                       time.Time
	EndDate                         time.Time
	ClientID                        string
	BrandIDs                        []string
	PatternTypes                    []string
	TyreSizes                       []string
	ParentCustomAssetSubcategoryIDs []string
}

type AssetTyreUsageCondition struct {
	Where       AssetTyreUsageWhere
	Columns     []string
	IsForUpdate bool
}

var AssetTyreSorts = map[string]string{
	"average_rtd":                 "average_rtd",
	"utilization_rate_percentage": "utilization_rate_percentage",
	"start_thread_depth":          "start_thread_depth",
}

type AssetTyreInspectionReportWhere struct {
	StartDate                  time.Time
	EndDate                    time.Time
	BrandIDs                   []string
	TyreSizes                  []string
	PatternTypes               []string
	StatusCodes                []string
	UtilizationRateStatusCodes []string
	ClientID                   string
}

type AssetTyreInspectionReportCondition struct {
	Where AssetTyreInspectionReportWhere
}

type GetAssetTyreInspectionReportParam struct {
	commonmodel.ListRequest
	Cond AssetTyreInspectionReportCondition
}

type AssetTyreRotationReportWhere struct {
	StartDate time.Time
	EndDate   time.Time
	ClientID  string
}

type AssetTyreRotationReportCondition struct {
	Where AssetTyreRotationReportWhere
}

type GetAssetTyreRotationReportParam struct {
	commonmodel.ListRequest
	Cond AssetTyreRotationReportCondition
}
