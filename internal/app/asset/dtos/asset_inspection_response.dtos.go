package dtos

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"time"

	taskModels "assetfindr/internal/app/task/models"

	"github.com/jackc/pgtype"

	"gopkg.in/guregu/null.v4"
)

type GetAssetInspectionVehiclesResponse struct {
	ID                                 string       `json:"id"`
	AssetID                            string       `json:"asset_id"`
	Remark                             string       `json:"remark"`
	AssetAssignmentID                  string       `json:"asset_assignment_id"`
	VehicleKM                          float64      `json:"vehicle_km"`
	VehicleHm                          float64      `json:"vehicle_hm"`
	AssetInspectionID                  string       `json:"asset_inspections_id"`
	InspectionNumber                   string       `json:"inspection_number"`
	AssetInspectionCreatedAt           time.Time    `json:"asset_inspections_created_at"`
	AssetInspectionInspectedByUserId   string       `json:"asset_inspections_inspected_by_user_id"`
	AssetInspectionInspectedByUserName string       `json:"asset_inspections_inspected_by_user_name"`
	AssetInspectionLocationLat         null.Float   `json:"asset_inspections_location_lat"`
	AssetInspectionLocationLong        null.Float   `json:"asset_inspections_location_long"`
	AssetInspectionLocationLabel       string       `json:"asset_inspections_location_label"`
	RegistrationNumber                 string       `json:"registration_number"`
	ReferenceNumber                    string       `json:"reference_number"`
	SerialNumber                       string       `json:"serial_number"`
	PartnerOwnerID                     string       `json:"partner_owner_id"`
	PartnerOwnerNo                     string       `json:"partner_owner_no"`
	PartnerOwnerName                   string       `json:"partner_owner_name"`
	DigispectVehiclePhoto              string       `json:"digispect_vehicle_photo"`
	UseTyreOptimax                     null.Bool    `json:"use_tyre_optimax"`
	UseFleetOptimax                    null.Bool    `json:"use_fleet_optimax"`
	AxleConfiguration                  pgtype.JSONB `json:"axle_configuration"`
	MaxRtdDiffTolerance                *null.Int    `json:"max_rtd_diff_tolerance"`
	VehicleModel                       string       `json:"model_number"`
	FailedVisualChecking               null.Bool    `json:"failed_visual_checking"`
	RequireRotationTyre                null.Bool    `json:"require_rotation_tyre"`
	RequireSpooringVehicle             null.Bool    `json:"require_spooring_vehicle"`
	TireTreadAndRimDamage              null.Bool    `json:"tire_tread_and_rim_damage"`
	TicketNumber                       string       `json:"ticket_number"`
	TicketID                           string       `json:"ticket_id"`

	NumberOfTyres            null.Int                                    `json:"no_of_tyres"`
	NumberOfSpareTyres       null.Int                                    `json:"no_of_spare_tyres"`
	DisplayDataFormationCode string                                      `json:"display_data_formation_code"`
	DisplayDataFormation     models.AssetInspectionDisplayFormationCodes `json:"display_data_formation"`

	DeviceID       string                            `json:"device_id"`
	SourceTypeCode null.String                       `json:"source_type_code"`
	SourceType     models.AssetInspectionSourceTypes `json:"source_type"`

	CustomBrandName       null.String `json:"custom_brand_name"`
	CustomModelName       null.String `json:"custom_model_name"`
	CustomReferenceNumber null.String `json:"custom_reference_number"`

	AssetInspectionTyres []AssetInspectionTyreResp `json:"asset_inspection_tyres"`
}

func BuildGetAssetInspectionVehiclesResponseItem(
	assetInspectionVehicle models.AssetInspectionVehicle,
	inspectedUserName string,
	ticket taskModels.Ticket,
	digispectVehiclePhoto string,
) GetAssetInspectionVehiclesResponse {
	item := GetAssetInspectionVehiclesResponse{
		ID:                                 assetInspectionVehicle.ID,
		AssetID:                            assetInspectionVehicle.AssetVehicleID,
		Remark:                             assetInspectionVehicle.Remark,
		AssetAssignmentID:                  assetInspectionVehicle.AssetAssignmentID,
		VehicleKM:                          assetInspectionVehicle.VehicleKM,
		VehicleHm:                          calculationhelpers.Div100(assetInspectionVehicle.VehicleHm),
		AssetInspectionID:                  assetInspectionVehicle.AssetInspectionID,
		InspectionNumber:                   assetInspectionVehicle.AssetInspection.InspectionNumber,
		AssetInspectionCreatedAt:           assetInspectionVehicle.AssetInspection.CreatedAt,
		AssetInspectionInspectedByUserId:   assetInspectionVehicle.AssetInspection.InspectByUserID,
		AssetInspectionInspectedByUserName: inspectedUserName,
		AssetInspectionLocationLat:         assetInspectionVehicle.AssetInspection.LocationLat,
		AssetInspectionLocationLong:        assetInspectionVehicle.AssetInspection.LocationLong,
		AssetInspectionLocationLabel:       assetInspectionVehicle.AssetInspection.LocationLabel,
		RegistrationNumber:                 assetInspectionVehicle.AssetVehicle.RegistrationNumber,
		ReferenceNumber:                    assetInspectionVehicle.AssetVehicle.Asset.ReferenceNumber,
		SerialNumber:                       assetInspectionVehicle.AssetVehicle.Asset.SerialNumber,
		PartnerOwnerID:                     assetInspectionVehicle.AssetVehicle.Asset.PartnerOwnerID,
		PartnerOwnerName:                   assetInspectionVehicle.PartnerOwnerName,
		DigispectVehiclePhoto:              digispectVehiclePhoto,
		PartnerOwnerNo:                     assetInspectionVehicle.AssetVehicle.Asset.PartnerOwnerNo,
		UseFleetOptimax:                    assetInspectionVehicle.AssetVehicle.Asset.UseFleetOptimax,
		UseTyreOptimax:                     assetInspectionVehicle.AssetVehicle.Asset.UseTyreOptimax,
		AxleConfiguration:                  assetInspectionVehicle.AxleConfiguration,
		MaxRtdDiffTolerance:                assetInspectionVehicle.MaxRtdDiffTolerance,
		VehicleModel:                       assetInspectionVehicle.AssetVehicle.Vehicle.VehicleModel,
		FailedVisualChecking:               assetInspectionVehicle.FailedVisualChecking,
		RequireRotationTyre:                assetInspectionVehicle.RequireRotationTyre,
		RequireSpooringVehicle:             assetInspectionVehicle.RequireSpooringVehicle,
		TireTreadAndRimDamage:              assetInspectionVehicle.TireTreadAndRimDamage,
		CustomBrandName:                    assetInspectionVehicle.CustomBrandName,
		CustomModelName:                    assetInspectionVehicle.CustomModelName,
		CustomReferenceNumber:              assetInspectionVehicle.CustomReferenceNumber,
		DeviceID:                           assetInspectionVehicle.DeviceID,
		SourceTypeCode:                     assetInspectionVehicle.SourceTypeCode,
		SourceType:                         assetInspectionVehicle.SourceType,
		TicketNumber:                       ticket.TicketNumber,
		TicketID:                           ticket.ID,
		DisplayDataFormationCode:           assetInspectionVehicle.AssetInspection.DisplayFormationCode,
		DisplayDataFormation:               assetInspectionVehicle.AssetInspection.AssetInspectionDisplayFormation,
		NumberOfTyres:                      assetInspectionVehicle.NumberOfTyres,
		NumberOfSpareTyres:                 assetInspectionVehicle.NumberOfSpareTyres,
	}

	if item.PartnerOwnerName == "" {
		item.PartnerOwnerName = assetInspectionVehicle.AssetVehicle.Asset.PartnerOwnerName
	}

	return item
}

func BuildGetAssetInspectionVehiclesResponse(
	assetInspectionVehicles []models.AssetInspectionVehicle,
	usersMapById map[string]userIdentityModel.User,
) []GetAssetInspectionVehiclesResponse {
	response := []GetAssetInspectionVehiclesResponse{}

	for _, assetInspectionVehicle := range assetInspectionVehicles {
		userId := assetInspectionVehicle.AssetInspection.InspectByUserID

		user := usersMapById[userId]

		dto := GetAssetInspectionVehiclesResponse{
			ID:                                 assetInspectionVehicle.ID,
			AssetID:                            assetInspectionVehicle.AssetVehicleID,
			Remark:                             assetInspectionVehicle.Remark,
			AssetAssignmentID:                  assetInspectionVehicle.AssetAssignmentID,
			VehicleKM:                          assetInspectionVehicle.VehicleKM,
			VehicleHm:                          calculationhelpers.Div100(assetInspectionVehicle.VehicleHm),
			AssetInspectionID:                  assetInspectionVehicle.AssetInspectionID,
			InspectionNumber:                   assetInspectionVehicle.AssetInspection.InspectionNumber,
			AssetInspectionCreatedAt:           assetInspectionVehicle.AssetInspection.CreatedAt,
			AssetInspectionInspectedByUserId:   assetInspectionVehicle.AssetInspection.InspectByUserID,
			AssetInspectionInspectedByUserName: user.GetName(),
			AssetInspectionLocationLat:         assetInspectionVehicle.AssetInspection.LocationLat,
			AssetInspectionLocationLong:        assetInspectionVehicle.AssetInspection.LocationLong,
			AssetInspectionLocationLabel:       assetInspectionVehicle.AssetInspection.LocationLabel,
			RegistrationNumber:                 assetInspectionVehicle.AssetVehicle.RegistrationNumber,
			ReferenceNumber:                    assetInspectionVehicle.AssetVehicle.Asset.ReferenceNumber,
			SerialNumber:                       assetInspectionVehicle.AssetVehicle.Asset.SerialNumber,
			UseFleetOptimax:                    assetInspectionVehicle.AssetVehicle.Asset.UseFleetOptimax,
			UseTyreOptimax:                     assetInspectionVehicle.AssetVehicle.Asset.UseTyreOptimax,
			AxleConfiguration:                  assetInspectionVehicle.AxleConfiguration,
			MaxRtdDiffTolerance:                assetInspectionVehicle.MaxRtdDiffTolerance,
			VehicleModel:                       assetInspectionVehicle.AssetVehicle.Vehicle.VehicleModel,
			FailedVisualChecking:               assetInspectionVehicle.FailedVisualChecking,
			RequireRotationTyre:                assetInspectionVehicle.RequireRotationTyre,
			RequireSpooringVehicle:             assetInspectionVehicle.RequireSpooringVehicle,
			TireTreadAndRimDamage:              assetInspectionVehicle.TireTreadAndRimDamage,
			CustomBrandName:                    assetInspectionVehicle.CustomBrandName,
			CustomModelName:                    assetInspectionVehicle.CustomModelName,
			CustomReferenceNumber:              assetInspectionVehicle.CustomReferenceNumber,
		}
		response = append(response, dto)
	}

	return response
}

type GetAssetInspectionTyresResponse struct {
	ID                                 string         `json:"id"`
	UpdateAt                           time.Time      `json:"updated_at"`
	AssetID                            string         `json:"asset_id"`
	Remark                             string         `json:"remark"`
	AssetAssignmentID                  string         `json:"asset_assignment_id"`
	Pressure                           float64        `json:"pressure"`
	RDT1                               float64        `json:"rdt1"`
	RDT2                               float64        `json:"rdt2"`
	RDT3                               float64        `json:"rdt3"`
	RDT4                               float64        `json:"rdt4"`
	Temperature                        null.Float     `json:"temperature"`
	TyrePosition                       float64        `json:"tyre_position"`
	AverageRTD                         float64        `json:"average_rtd"`
	AssetInspectionID                  string         `json:"asset_inspections_id"`
	SerialNumber                       string         `json:"serial_number"`
	PressureStatusCode                 string         `json:"pressure_status_code"`
	InspectionNumber                   string         `json:"inspection_number"`
	AssetInspectionCreatedAt           time.Time      `json:"asset_inspections_created_at"`
	AssetInspectionUpdateAt            time.Time      `json:"asset_inspections_updated_at"`
	AssetInspectionInspectedByUserId   string         `json:"asset_inspections_inspected_by_user_id"`
	AssetInspectionInspectedByUserName string         `json:"asset_inspections_inspected_by_user_name"`
	AssetInspectionLocationLat         null.Float     `json:"asset_inspections_location_lat"`
	AssetInspectionLocationLong        null.Float     `json:"asset_inspections_location_long"`
	AssetInspectionLocationLabel       string         `json:"asset_inspections_location_label"`
	TyreKM                             int            `json:"tyre_km"`
	TyreHm                             float64        `json:"tyre_hm"`
	FailedVisualChecking               null.Bool      `json:"failed_visual_checking"`
	RequireRotationTyre                null.Bool      `json:"require_rotation_tyre"`
	RequireSpooringVehicle             null.Bool      `json:"require_spooring_vehicle"`
	TireTreadAndRimDamage              null.Bool      `json:"tire_tread_and_rim_damage"`
	CustomSerialNumber                 string         `json:"custom_serial_number"`
	DeviceID                           string         `json:"device_id"`
	AssetSerialNumber                  string         `json:"asset_serial_number"`
	AssetReferenceNumber               string         `json:"asset_reference_number"`
	PartnerOwnerID                     string         `json:"partner_owner_id"`
	PartnerOwnerNo                     string         `json:"partner_owner_no"`
	PartnerOwnerName                   string         `json:"partner_owner_name"`
	CustomBrandName                    null.String    `json:"custom_brand_name"`
	CustomTyreSize                     string         `json:"custom_tyre_size"`
	OriginalTd                         float64        `json:"original_td"`
	UtilizationRatePercentage          float64        `json:"utilization_rate_percentage"`
	InspectedAssetVehicle              *AttachedAsset `json:"inspected_asset_vehicle"`
	NumberOfInspectionPoint            int            `json:"number_of_inspection_point"`
	PressureSensorRef                  string         `json:"pressure_sensor_ref"`
	TemperatureSensorRef               string         `json:"temperature_sensor_ref"`
	IsMismatch                         null.Bool      `json:"is_mismatch"`
	TyreInspectionCreatedAt            time.Time      `json:"tyre_inspection_created_at"`
	CustomRFID                         string         `json:"custom_rfid"`
	AssetBrandName                     string         `json:"asset_brand_name"`
	TyreSectionWidth                   string         `json:"section_width"`
	TyreConstruction                   string         `json:"construction"`
	TyreRimDiameter                    string         `json:"rim_diameter"`

	DisplayDataFormationCode string                                      `json:"display_data_formation_code"`
	DisplayDataFormation     models.AssetInspectionDisplayFormationCodes `json:"display_data_formation"`

	DigispectConfigID string           `json:"digispect_config_id"`
	DigispectConfig   *DigispectConfig `json:"digispect_config"`

	TicketNumber string `json:"ticket_number"`
	TicketID     string `json:"ticket_id"`
}

type AttachedAsset struct {
	AssetID              string  `json:"asset_id"`
	AssetReferenceNumber string  `json:"asset_reference_number"`
	AssetSerialNumber    string  `json:"asset_serial_number"`
	VehicleKM            float64 `json:"vehicle_km"`
	VehicleHm            float64 `json:"vehicle_hm"`
}

type GetAssetInspectionTyresExportResponse struct {
	GetAssetInspectionTyresResponse
	ReferenceNumber string  `json:"reference_number"`
	VehicleKM       float64 `json:"vehicle_km"`
	BrandName       string  `json:"brand_name"`
	TyreSize        string  `json:"tyre_size"`
	Rfid            string  `json:"rfid"`
}

func BuildAnAssetInspectionTyresResponse(
	assetInspectionTyre models.AssetInspectionTyre,
	userName string,
) *GetAssetInspectionTyresResponse {
	dto := GetAssetInspectionTyresResponse{
		ID:                                 assetInspectionTyre.ID,
		AssetID:                            assetInspectionTyre.AssetTyreID,
		Remark:                             assetInspectionTyre.Remark,
		AssetAssignmentID:                  assetInspectionTyre.AssetAssignmentID,
		Pressure:                           assetInspectionTyre.Pressure,
		RDT1:                               assetInspectionTyre.RDT1,
		RDT2:                               assetInspectionTyre.RDT2,
		RDT3:                               assetInspectionTyre.RDT3,
		RDT4:                               assetInspectionTyre.RDT4,
		TyrePosition:                       assetInspectionTyre.TyrePosition,
		AverageRTD:                         assetInspectionTyre.AverageRTD,
		AssetInspectionID:                  assetInspectionTyre.AssetInspectionID,
		SerialNumber:                       assetInspectionTyre.AssetTyre.Asset.SerialNumber,
		PressureStatusCode:                 assetInspectionTyre.PressureStatusCode,
		InspectionNumber:                   assetInspectionTyre.AssetInspection.InspectionNumber,
		AssetInspectionCreatedAt:           assetInspectionTyre.AssetInspection.CreatedAt,
		AssetInspectionInspectedByUserId:   assetInspectionTyre.AssetInspection.InspectByUserID,
		AssetInspectionInspectedByUserName: userName,
		AssetInspectionLocationLat:         assetInspectionTyre.AssetInspection.LocationLat,
		AssetInspectionLocationLong:        assetInspectionTyre.AssetInspection.LocationLong,
		AssetInspectionLocationLabel:       assetInspectionTyre.AssetInspection.LocationLabel,
		TyreKM:                             assetInspectionTyre.TyreKM,
		TyreHm:                             calculationhelpers.Div100(assetInspectionTyre.TyreHm),
		FailedVisualChecking:               assetInspectionTyre.FailedVisualChecking,
		RequireRotationTyre:                assetInspectionTyre.RequireRotationTyre,
		RequireSpooringVehicle:             assetInspectionTyre.RequireSpooringVehicle,
		TireTreadAndRimDamage:              assetInspectionTyre.TireTreadAndRimDamage,
		CustomSerialNumber:                 assetInspectionTyre.CustomSerialNumber,
		DeviceID:                           assetInspectionTyre.DeviceID,
		AssetSerialNumber:                  assetInspectionTyre.AssetTyre.Asset.SerialNumber,
		AssetReferenceNumber:               assetInspectionTyre.AssetTyre.Asset.ReferenceNumber,
		CustomBrandName:                    assetInspectionTyre.CustomBrandName,
		CustomTyreSize:                     assetInspectionTyre.CustomTyreSize,
		OriginalTd:                         assetInspectionTyre.StartThreadDepth,
		Temperature:                        assetInspectionTyre.Temperature,
		UtilizationRatePercentage:          helpers.CalculateTyreUtilRate(assetInspectionTyre.StartThreadDepth, assetInspectionTyre.AverageRTD),
		PressureSensorRef:                  assetInspectionTyre.PressureSensorRef,
		TemperatureSensorRef:               assetInspectionTyre.TemperatureSensorRef,
		IsMismatch:                         assetInspectionTyre.IsMismatch,
		TyreInspectionCreatedAt:            assetInspectionTyre.CreatedAt,
		DigispectConfigID:                  assetInspectionTyre.DigispectConfigID,
		DigispectConfig:                    BuildDigispectConfigResp(assetInspectionTyre.DigispectConfig),
	}

	if assetInspectionTyre.AssetInspectionVehicle != nil {
		dto.InspectedAssetVehicle = &AttachedAsset{
			AssetID:              assetInspectionTyre.AssetInspectionVehicle.Asset.ID,
			AssetReferenceNumber: assetInspectionTyre.AssetInspectionVehicle.Asset.ReferenceNumber,
			AssetSerialNumber:    assetInspectionTyre.AssetInspectionVehicle.Asset.SerialNumber,
			VehicleKM:            assetInspectionTyre.AssetInspectionVehicle.VehicleKM,
			VehicleHm:            calculationhelpers.Div100(assetInspectionTyre.AssetInspectionVehicle.VehicleHm),
		}
	}

	return &dto
}

func BuildGetAssetInspectionTyresResponse(
	assetInspectionTyres []models.AssetInspectionTyre,
	usersMapName map[string]string,
	ticketsMap map[string]taskModels.Ticket,
	mapFlowIDToTicket map[string]taskModels.Ticket,
) []GetAssetInspectionTyresResponse {
	response := []GetAssetInspectionTyresResponse{}

	for _, assetInspectionTyre := range assetInspectionTyres {
		userId := assetInspectionTyre.AssetInspection.InspectByUserID

		item := GetAssetInspectionTyresResponse{
			ID:                       assetInspectionTyre.ID,
			UpdateAt:                 assetInspectionTyre.UpdatedAt,
			AssetID:                  assetInspectionTyre.AssetTyreID,
			Remark:                   assetInspectionTyre.Remark,
			AssetAssignmentID:        assetInspectionTyre.AssetAssignmentID,
			Pressure:                 assetInspectionTyre.Pressure,
			RDT1:                     assetInspectionTyre.RDT1,
			RDT2:                     assetInspectionTyre.RDT2,
			RDT3:                     assetInspectionTyre.RDT3,
			RDT4:                     assetInspectionTyre.RDT4,
			Temperature:              assetInspectionTyre.Temperature,
			TyrePosition:             assetInspectionTyre.TyrePosition,
			AverageRTD:               assetInspectionTyre.AverageRTD,
			AssetInspectionID:        assetInspectionTyre.AssetInspectionID,
			SerialNumber:             assetInspectionTyre.AssetTyre.Asset.SerialNumber,
			PressureStatusCode:       assetInspectionTyre.PressureStatusCode,
			InspectionNumber:         assetInspectionTyre.AssetInspection.InspectionNumber,
			AssetInspectionCreatedAt: assetInspectionTyre.AssetInspection.CreatedAt,
			AssetInspectionUpdateAt:  assetInspectionTyre.AssetInspection.UpdatedAt,
			DisplayDataFormationCode: assetInspectionTyre.AssetInspection.DisplayFormationCode,
			DisplayDataFormation:     assetInspectionTyre.AssetInspection.AssetInspectionDisplayFormation,

			AssetInspectionInspectedByUserId:   userId,
			AssetInspectionInspectedByUserName: usersMapName[userId],
			AssetInspectionLocationLat:         assetInspectionTyre.AssetInspection.LocationLat,
			AssetInspectionLocationLong:        assetInspectionTyre.AssetInspection.LocationLong,
			AssetInspectionLocationLabel:       assetInspectionTyre.AssetInspection.LocationLabel,
			TyreKM:                             assetInspectionTyre.TyreKM,
			TyreHm:                             calculationhelpers.Div100(assetInspectionTyre.TyreHm),
			FailedVisualChecking:               assetInspectionTyre.FailedVisualChecking,
			RequireRotationTyre:                assetInspectionTyre.RequireRotationTyre,
			RequireSpooringVehicle:             assetInspectionTyre.RequireSpooringVehicle,
			TireTreadAndRimDamage:              assetInspectionTyre.TireTreadAndRimDamage,
			CustomSerialNumber:                 assetInspectionTyre.CustomSerialNumber,
			DeviceID:                           assetInspectionTyre.DeviceID,
			AssetSerialNumber:                  assetInspectionTyre.AssetTyre.Asset.SerialNumber,
			AssetReferenceNumber:               assetInspectionTyre.AssetTyre.Asset.ReferenceNumber,
			PartnerOwnerID:                     assetInspectionTyre.AssetTyre.Asset.PartnerOwnerID,
			PartnerOwnerNo:                     assetInspectionTyre.AssetTyre.Asset.PartnerOwnerNo,
			PartnerOwnerName:                   assetInspectionTyre.AssetTyre.Asset.PartnerOwnerName,
			CustomBrandName:                    assetInspectionTyre.CustomBrandName,
			CustomTyreSize:                     assetInspectionTyre.CustomTyreSize,
			OriginalTd:                         assetInspectionTyre.StartThreadDepth,
			UtilizationRatePercentage:          helpers.CalculateTyreUtilRate(assetInspectionTyre.StartThreadDepth, assetInspectionTyre.AverageRTD),
			NumberOfInspectionPoint:            assetInspectionTyre.NumberOfInspectionPoint,
			PressureSensorRef:                  assetInspectionTyre.PressureSensorRef,
			TemperatureSensorRef:               assetInspectionTyre.TemperatureSensorRef,
			IsMismatch:                         assetInspectionTyre.IsMismatch,
			TyreInspectionCreatedAt:            assetInspectionTyre.CreatedAt,
			CustomRFID:                         assetInspectionTyre.CustomRFID,
			AssetBrandName:                     assetInspectionTyre.AssetTyre.Asset.Brand.BrandName,
			TyreSectionWidth:                   assetInspectionTyre.AssetTyre.Tyre.SectionWidth,
			TyreConstruction:                   assetInspectionTyre.AssetTyre.Tyre.Construction,
			TyreRimDiameter:                    assetInspectionTyre.AssetTyre.Tyre.RimDiameter,
			DigispectConfigID:                  assetInspectionTyre.DigispectConfigID,
			DigispectConfig:                    BuildDigispectConfigResp(assetInspectionTyre.DigispectConfig),
		}

		if assetInspectionTyre.AssetInspection.ReferenceCode == constants.ASSET_INSPECTION_REFERENCE_CODE_WORK_ORDER {
			ticket := ticketsMap[assetInspectionTyre.AssetInspection.ReferenceID]
			item.TicketNumber = ticket.TicketNumber
			item.TicketID = ticket.ID
		} else if assetInspectionTyre.AssetInspection.ReferenceCode == constants.ASSET_INSPECTION_REFERENCE_CODE_FLOW {
			ticket := mapFlowIDToTicket[assetInspectionTyre.AssetInspection.ReferenceID]
			item.TicketNumber = ticket.TicketNumber
			item.TicketID = ticket.ID
		}

		if assetInspectionTyre.AssetInspectionVehicle != nil {
			item.InspectedAssetVehicle = &AttachedAsset{
				AssetID:              assetInspectionTyre.AssetInspectionVehicle.Asset.ID,
				AssetReferenceNumber: assetInspectionTyre.AssetInspectionVehicle.Asset.ReferenceNumber,
				AssetSerialNumber:    assetInspectionTyre.AssetInspectionVehicle.Asset.SerialNumber,
				VehicleKM:            assetInspectionTyre.AssetInspectionVehicle.VehicleKM,
				VehicleHm:            calculationhelpers.Div100(assetInspectionTyre.AssetInspectionVehicle.VehicleHm),
			}
		}
		response = append(response, item)
	}

	return response
}

func BuildGetAssetInspectionTyresExportResponse(
	assetInspectionTyres []models.AssetInspectionTyre, usersMapById map[string]userIdentityModel.User,
	assetTyresMapById map[string]models.AssetTyre,
	assetInspectionVehiclesMapById map[string]models.AssetInspectionVehicle,
) []GetAssetInspectionTyresExportResponse {
	response := []GetAssetInspectionTyresExportResponse{}

	for _, assetInspectionTyre := range assetInspectionTyres {
		userId := assetInspectionTyre.AssetInspection.InspectByUserID

		referenceNumber := ""
		var vehicleKM float64 = 0
		if assetInspectionTyre.AssetInspectionVehicle != nil {
			referenceNumber = assetInspectionTyre.AssetInspectionVehicle.Asset.ReferenceNumber
			vehicleKM = assetInspectionTyre.AssetInspectionVehicle.VehicleKM
		}

		rfid := ""
		if assetInspectionTyre.AssetTyreID != "" {
			rfid = assetInspectionTyre.AssetTyre.Asset.Rfid
			vehicleKM = assetInspectionTyre.AssetInspectionVehicle.VehicleKM
		} else {
			rfid = assetInspectionTyre.CustomRFID
		}

		dto := GetAssetInspectionTyresExportResponse{
			GetAssetInspectionTyresResponse: GetAssetInspectionTyresResponse{
				ID:                                 assetInspectionTyre.ID,
				AssetID:                            assetInspectionTyre.AssetTyreID,
				Remark:                             assetInspectionTyre.Remark,
				AssetAssignmentID:                  assetInspectionTyre.AssetAssignmentID,
				Pressure:                           assetInspectionTyre.Pressure,
				RDT1:                               assetInspectionTyre.RDT1,
				RDT2:                               assetInspectionTyre.RDT2,
				RDT3:                               assetInspectionTyre.RDT3,
				RDT4:                               assetInspectionTyre.RDT4,
				Temperature:                        null.Float{},
				TyrePosition:                       assetInspectionTyre.TyrePosition,
				AverageRTD:                         assetInspectionTyre.AverageRTD,
				AssetInspectionID:                  assetInspectionTyre.AssetInspectionID,
				SerialNumber:                       assetInspectionTyre.AssetTyre.Asset.SerialNumber,
				PressureStatusCode:                 assetInspectionTyre.PressureStatusCode,
				InspectionNumber:                   assetInspectionTyre.AssetInspection.InspectionNumber,
				AssetInspectionCreatedAt:           assetInspectionTyre.AssetInspection.CreatedAt,
				AssetInspectionInspectedByUserId:   userId,
				AssetInspectionInspectedByUserName: usersMapById[userId].GetName(),
				AssetInspectionLocationLat:         assetInspectionTyre.AssetInspection.LocationLat,
				AssetInspectionLocationLong:        assetInspectionTyre.AssetInspection.LocationLong,
				AssetInspectionLocationLabel:       assetInspectionTyre.AssetInspection.LocationLabel,
				TyreKM:                             assetInspectionTyre.TyreKM,
				TyreHm:                             calculationhelpers.Div100(assetInspectionTyre.TyreHm),
				FailedVisualChecking:               assetInspectionTyre.FailedVisualChecking,
				RequireRotationTyre:                assetInspectionTyre.RequireRotationTyre,
				RequireSpooringVehicle:             assetInspectionTyre.RequireSpooringVehicle,
				TireTreadAndRimDamage:              assetInspectionTyre.TireTreadAndRimDamage,
				CustomSerialNumber:                 assetInspectionTyre.CustomSerialNumber,
				DeviceID:                           assetInspectionTyre.DeviceID,
				AssetSerialNumber:                  assetInspectionTyre.AssetTyre.Asset.SerialNumber,
				AssetReferenceNumber:               assetInspectionTyre.AssetTyre.Asset.ReferenceNumber,
				CustomBrandName:                    assetInspectionTyre.CustomBrandName,
				CustomTyreSize:                     assetInspectionTyre.CustomTyreSize,
				OriginalTd:                         assetInspectionTyre.StartThreadDepth,
				UtilizationRatePercentage:          helpers.CalculateTyreUtilRate(assetInspectionTyre.StartThreadDepth, assetInspectionTyre.AverageRTD),
				NumberOfInspectionPoint:            assetInspectionTyre.NumberOfInspectionPoint,
				PressureSensorRef:                  assetInspectionTyre.PressureSensorRef,
				TemperatureSensorRef:               assetInspectionTyre.TemperatureSensorRef,
				IsMismatch:                         assetInspectionTyre.IsMismatch,
			},
			ReferenceNumber: referenceNumber,
			VehicleKM:       vehicleKM,
			BrandName:       assetInspectionTyre.AssetTyre.Asset.Brand.BrandName,
			TyreSize:        assetInspectionTyre.AssetTyre.Tyre.SectionWidth + " " + assetInspectionTyre.AssetTyre.Tyre.Construction + " " + assetInspectionTyre.AssetTyre.Tyre.TyreNumber,
			Rfid:            rfid,
		}
		response = append(response, dto)
	}

	return response
}

type AssetInspectionVehicleTyreResp struct {
	ID              string `json:"id"`
	InspectByUserID string `json:"inspect_by_user_id"`
	ReferenceID     string `json:"reference_id"`
	ReferenceCode   string `json:"reference_code"`
}

type AssetInspectionVehicleResp struct {
	AssetInspectionID string  `json:"asset_inspection_id"`
	AssetVehicleID    string  `json:"asset_vehicle_id"`
	Remark            string  `json:"remark"`
	AssetAssignmentID string  `json:"asset_assignment_id"`
	VehicleKM         float64 `json:"vehicle_km"`
	VehicleHm         float64 `json:"vehicle_hm"`
}

type AssetInspectionResp struct {
	ID                                     string                                    `json:"id"`
	InspectionNumber                       string                                    `json:"inspection_number"`
	CreatedAt                              time.Time                                 `json:"created_at"`
	UpdatedAt                              time.Time                                 `json:"updated_at"`
	InspectByUserID                        string                                    `json:"inspect_by_user_id"`
	InspectByUserName                      string                                    `json:"inspect_by_user_name"`
	ReferenceID                            string                                    `json:"reference_id" gorm:"default:null"`
	ReferenceCode                          string                                    `json:"reference_code" gorm:"default:null"`
	ClientID                               string                                    `json:"client_id"`
	StatusCode                             string                                    `json:"status_code"`
	Reference                              commonmodel.ConstantModel                 `json:"reference"`
	LocationLat                            null.Float                                `json:"location_lat"`
	LocationLong                           null.Float                                `json:"location_long"`
	LocationLabel                          string                                    `json:"location_label"`
	AssetInspectionTyreDetail              *AssetInspectionTyreDetailResp            `json:"asset_inspection_tyre_detail"`
	AssetInspectionVehicleDetail           *AssetInspectionVehicleDetailResp         `json:"asset_inspection_vehicle_detail"`
	AssetInspectionReferenceWorOrderDetail *AssetInspectionReferenceTicketDetailResp `json:"asset_inspection_reference_work_order_detail"`
	CountInspectionTyres                   int                                       `json:"count_inspection_tyres"`

	DisplayDataFormationCode string                                      `json:"display_data_formation_code"`
	DisplayDataFormation     models.AssetInspectionDisplayFormationCodes `json:"display_data_formation"`
}

func BuildAssetInspectionResp(
	inspection models.AssetInspection,
	mapUserName map[string]string,
	mapTickets map[string]taskModels.Ticket,

) AssetInspectionResp {
	assetInspectionResp := AssetInspectionResp{
		ID:                   inspection.ID,
		InspectionNumber:     inspection.InspectionNumber,
		CreatedAt:            inspection.CreatedAt,
		UpdatedAt:            inspection.UpdatedAt,
		InspectByUserID:      inspection.InspectByUserID,
		InspectByUserName:    mapUserName[inspection.InspectByUserID],
		ReferenceID:          inspection.ReferenceID,
		ReferenceCode:        inspection.ReferenceCode,
		StatusCode:           inspection.StatusCode,
		LocationLat:          inspection.LocationLat,
		LocationLong:         inspection.LocationLong,
		LocationLabel:        inspection.LocationLabel,
		Reference:            commonmodel.ConstantModel(inspection.Reference),
		CountInspectionTyres: inspection.CountInspectionTyres.NumberInspectionTyre,

		DisplayDataFormationCode: inspection.DisplayFormationCode,
		DisplayDataFormation:     inspection.AssetInspectionDisplayFormation,
	}

	// TICKET DETAIL
	ticket := mapTickets[inspection.ReferenceID]
	if ticket.ID != "" {
		assetInspectionResp.AssetInspectionReferenceWorOrderDetail = &AssetInspectionReferenceTicketDetailResp{
			ID:           ticket.ID,
			Subject:      ticket.Subject,
			TicketNumber: ticket.TicketNumber,
			ReferenceID:  ticket.ReferenceID,
		}
	}

	// TYRE DETAIL
	if inspection.AssetInspectionTyre != nil {
		assetInspectionResp.AssetInspectionTyreDetail = &AssetInspectionTyreDetailResp{
			ID:                      inspection.AssetInspectionTyre.ID,
			UpdatedAt:               inspection.AssetInspectionTyre.UpdatedAt,
			PartnerOwnerID:          inspection.AssetInspectionTyre.AssetTyre.Asset.PartnerOwnerID,
			PartnerOwnerNo:          inspection.AssetInspectionTyre.AssetTyre.Asset.PartnerOwnerNo,
			PartnerOwnerName:        inspection.AssetInspectionTyre.AssetTyre.Asset.PartnerOwnerName,
			AssetID:                 inspection.AssetInspectionTyre.AssetTyre.Asset.ID,
			AssetSerialNumber:       inspection.AssetInspectionTyre.AssetTyre.Asset.SerialNumber,
			AssetReferenceNumber:    inspection.AssetInspectionTyre.AssetTyre.Asset.ReferenceNumber,
			CustomBrandName:         inspection.AssetInspectionTyre.CustomBrandName,
			CustomTyreSize:          inspection.AssetInspectionTyre.CustomTyreSize,
			CustomSerialNumber:      inspection.AssetInspectionTyre.CustomSerialNumber,
			BrandName:               inspection.AssetInspectionTyre.AssetTyre.Asset.Brand.BrandName,
			SectionWidth:            inspection.AssetInspectionTyre.AssetTyre.Tyre.SectionWidth,
			Construction:            inspection.AssetInspectionTyre.AssetTyre.Tyre.Construction,
			RimDiameter:             inspection.AssetInspectionTyre.AssetTyre.Tyre.RimDiameter,
			NumberOfInspectionPoint: inspection.AssetInspectionTyre.NumberOfInspectionPoint,
			PressureSensorRef:       inspection.AssetInspectionTyre.PressureSensorRef,
			TemperatureSensorRef:    inspection.AssetInspectionTyre.TemperatureSensorRef,
			IsMismatch:              inspection.AssetInspectionTyre.IsMismatch,
			DeviceID:                inspection.AssetInspectionTyre.DeviceID,
		}
	}

	// VEHICLE DETAIL
	if inspection.AssetInspectionVehicle != nil {
		assetInspectionResp.AssetInspectionVehicleDetail = &AssetInspectionVehicleDetailResp{
			ID:                    inspection.AssetInspectionVehicle.ID,
			UpdatedAt:             inspection.AssetInspectionVehicle.UpdatedAt,
			PartnerOwnerID:        inspection.AssetInspectionVehicle.AssetVehicle.Asset.PartnerOwnerID,
			PartnerOwnerNo:        inspection.AssetInspectionVehicle.AssetVehicle.Asset.PartnerOwnerNo,
			PartnerOwnerName:      inspection.AssetInspectionVehicle.PartnerOwnerName,
			AssetID:               inspection.AssetInspectionVehicle.AssetVehicle.Asset.ID,
			AssetSerialNumber:     inspection.AssetInspectionVehicle.AssetVehicle.Asset.SerialNumber,
			UseFleetOptimax:       inspection.AssetInspectionVehicle.AssetVehicle.Asset.UseFleetOptimax,
			UseTyreOptimax:        inspection.AssetInspectionVehicle.AssetVehicle.Asset.UseTyreOptimax,
			AssetReferenceNumber:  inspection.AssetInspectionVehicle.AssetVehicle.Asset.ReferenceNumber,
			CustomBrandName:       inspection.AssetInspectionVehicle.CustomBrandName,
			CustomModelName:       inspection.AssetInspectionVehicle.CustomModelName,
			CustomReferenceNumber: inspection.AssetInspectionVehicle.CustomReferenceNumber,
			BrandName:             inspection.AssetInspectionVehicle.AssetVehicle.Asset.Brand.BrandName,
			VehicleModel:          inspection.AssetInspectionVehicle.AssetVehicle.Vehicle.VehicleModel,
			VehicleKM:             inspection.AssetInspectionVehicle.VehicleKM,
			VehicleHm:             calculationhelpers.Div100(inspection.AssetInspectionVehicle.VehicleHm),
		}
	}
	return assetInspectionResp
}

type AssetInspectionVehicleDetailResp struct {
	ID                    string      `json:"id"`
	UpdatedAt             time.Time   `json:"updated_at"`
	PartnerOwnerID        string      `json:"partner_owner_id"`
	PartnerOwnerNo        string      `json:"partner_owner_no"`
	PartnerOwnerName      string      `json:"partner_owner_name"`
	AssetID               string      `json:"asset_id"`
	AssetSerialNumber     string      `json:"asset_serial_number"`
	AssetReferenceNumber  string      `json:"asset_reference_number"`
	CustomBrandName       null.String `json:"custom_brand_name"`
	CustomModelName       null.String `json:"custom_model_name"`
	CustomReferenceNumber null.String `json:"custom_reference_number"`
	BrandName             string      `json:"brand_name"`
	VehicleModel          string      `json:"model_number"`
	UseTyreOptimax        null.Bool   `json:"use_tyre_optimax"`
	UseFleetOptimax       null.Bool   `json:"use_fleet_optimax"`
	VehicleKM             float64     `json:"vehicle_km"`
	VehicleHm             float64     `json:"vehicle_hm"`
}

type AssetInspectionTyreDetailResp struct {
	ID                      string      `json:"id"`
	UpdatedAt               time.Time   `json:"updated_at"`
	PartnerOwnerID          string      `json:"partner_owner_id"`
	PartnerOwnerNo          string      `json:"partner_owner_no"`
	PartnerOwnerName        string      `json:"partner_owner_name"`
	AssetID                 string      `json:"asset_id"`
	AssetSerialNumber       string      `json:"asset_serial_number"`
	AssetReferenceNumber    string      `json:"asset_reference_number"`
	CustomBrandName         null.String `json:"custom_brand_name"`
	CustomTyreSize          string      `json:"custom_tyre_size"`
	CustomSerialNumber      string      `json:"custom_serial_number"`
	BrandName               string      `json:"brand_name"`
	SectionWidth            string      `json:"section_width"`
	Construction            string      `json:"construction"`
	RimDiameter             string      `json:"rim_diameter"`
	NumberOfInspectionPoint int         `json:"number_of_inspection_point"`
	PressureSensorRef       string      `json:"pressure_sensor_ref"`
	TemperatureSensorRef    string      `json:"temperature_sensor_ref"`
	IsMismatch              null.Bool   `json:"is_mismatch"`
	DeviceID                string      `json:"device_id"`
}

type AssetInspectionReferenceTicketDetailResp struct {
	ID           string `json:"id"`
	Subject      string `json:"subject"`
	TicketNumber string `json:"ticket_number"`
	ReferenceID  string `json:"reference_id"`
}

type AssetInspectionTicketResp struct {
	ID               string    `json:"id"`
	InspectionNumber string    `json:"inspection_number"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	InspectByUserID  string    `json:"inspect_by_user_id" gorm:"type:varchar(40);not null"`
	ReferenceID      string    `json:"reference_id" gorm:"default:null"`
	ReferenceCode    string    `json:"reference_code" gorm:"default:null"`
	StatusCode       string    `json:"status_code"`
	Ticket           Ticket    `json:"ticket"`
}

func BuildAssetInspectionTicketResp(inspection models.AssetInspection, ticket taskModels.Ticket, asset models.Asset) AssetInspectionTicketResp {
	resp := AssetInspectionTicketResp{
		ID:               inspection.ID,
		InspectionNumber: inspection.InspectionNumber,
		CreatedAt:        inspection.CreatedAt,
		UpdatedAt:        inspection.UpdatedAt,
		InspectByUserID:  inspection.InspectByUserID,
		ReferenceID:      inspection.ReferenceID,
		ReferenceCode:    inspection.ReferenceCode,
		StatusCode:       inspection.StatusCode,
		Ticket: Ticket{
			ID:                   ticket.ID,
			Subject:              ticket.Subject,
			TicketNumber:         ticket.TicketNumber,
			ReferenceID:          ticket.ReferenceID,
			PartnerOwnerID:       asset.PartnerOwnerID,
			PartnerOwnerNo:       asset.PartnerOwnerNo,
			PartnerOwnerName:     asset.PartnerOwnerName,
			AssetID:              asset.ID,
			AssetReferenceNumber: asset.ReferenceNumber,
			AssetSerialNumber:    asset.SerialNumber,
			BrandName:            asset.Brand.BrandName,
		},
	}

	return resp
}

type Ticket struct {
	ID                   string `json:"id"`
	Subject              string `json:"subject"`
	TicketNumber         string `json:"ticket_number"`
	ReferenceID          string `json:"reference_id"`
	PartnerOwnerID       string `json:"partner_owner_id"`
	PartnerOwnerNo       string `json:"partner_owner_no"`
	PartnerOwnerName     string `json:"partner_owner_name"`
	AssetID              string `json:"asset_id"`
	AssetSerialNumber    string `json:"asset_serial_number"`
	AssetReferenceNumber string `json:"asset_reference_number"`
	BrandName            string `json:"brand_name"`
}
