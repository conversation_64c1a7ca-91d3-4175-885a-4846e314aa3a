package dtos

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/pkg/common/commonmodel"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type CreateDigispectVehicleReq struct {
	ReferenceNumber      string       `json:"reference_number"`
	PartnerOwnerName     null.String  `json:"partner_owner_name"`
	DigispectBrandID     null.String  `json:"digispect_brand_id"`
	DigispectBrandName   null.String  `json:"digispect_brand_name"`
	DigispectConfigID    null.String  `json:"digispect_config_id"`
	DigispectConfigModel null.String  `json:"digispect_config_model"`
	Photo                string       `json:"photo"`
	AxleConfiguration    pgtype.JSONB `json:"axle_configuration"`
}

type UpdateDigispectVehicleReq struct {
	ReferenceNumber      string       `json:"reference_number"`
	PartnerOwnerName     null.String  `json:"partner_owner_name"`
	DigispectBrandID     null.String  `json:"digispect_brand_id"`
	DigispectBrandName   null.String  `json:"digispect_brand_name"`
	DigispectConfigID    null.String  `json:"digispect_config_id"`
	DigispectConfigModel null.String  `json:"digispect_config_model"`
	Photo                string       `json:"photo"`
	AxleConfiguration    pgtype.JSONB `json:"axle_configuration"`
}
type UpdateDigispectVehicleAxleConfigurationReq struct {
	AxleConfiguration pgtype.JSONB `json:"axle_configuration"`
}

type DigispectVehicleListReq struct {
	commonmodel.ListRequest
}

type DigispectVehicle struct {
	ID                   string       `json:"id"`
	ReferenceNumber      string       `json:"reference_number"`
	PartnerOwnerName     null.String  `json:"partner_owner_name"`
	DigispectBrandID     *null.String `json:"digispect_brand_id"`
	DigispectBrandName   null.String  `json:"digispect_brand_name"`
	DigispectConfigID    *null.String `json:"digispect_config_id"`
	DigispectConfigModel null.String  `json:"digispect_config_model"`
	Photo                null.String  `json:"photo"`
	AxleConfiguration    pgtype.JSONB `json:"axle_configuration"`
	UpdatedAt            time.Time    `json:"updated_at"`
	CreatedAt            time.Time    `json:"created_at"`
	LastInspectedAt      time.Time    `json:"last_inspected_at"`
}

func BuildDigispectVehicleListResponse(digispectVehicles []models.DigispectVehicle) []DigispectVehicle {
	var digispectVehicleList []DigispectVehicle
	for _, digispectVehicle := range digispectVehicles {
		if digispectVehicle.AxleConfiguration.Status == pgtype.Undefined {
			digispectVehicle.AxleConfiguration.Status = pgtype.Null
		}

		var lastInspectedAt time.Time
		for _, inspection := range digispectVehicle.InspectionVechicles {
			if inspection.AssetVehicle.LastInspectedAt.Valid &&
				lastInspectedAt.Before(inspection.AssetVehicle.LastInspectedAt.Time) {
				lastInspectedAt = inspection.UpdatedAt
			}
		}

		digispectVehicleList = append(digispectVehicleList, DigispectVehicle{
			ID:                   digispectVehicle.ID,
			ReferenceNumber:      digispectVehicle.ReferenceNumber,
			PartnerOwnerName:     digispectVehicle.PartnerOwnerName,
			DigispectBrandID:     digispectVehicle.DigispectBrandID,
			DigispectBrandName:   digispectVehicle.DigispectBrandName,
			DigispectConfigID:    digispectVehicle.DigispectConfigID,
			DigispectConfigModel: digispectVehicle.DigispectConfigModel,
			Photo:                digispectVehicle.Photo,
			AxleConfiguration:    digispectVehicle.AxleConfiguration,
			UpdatedAt:            digispectVehicle.UpdatedAt,
			CreatedAt:            digispectVehicle.CreatedAt,
			LastInspectedAt:      lastInspectedAt,
		})
	}
	return digispectVehicleList
}
