package dtos

import (
	"assetfindr/internal/app/asset/models"
	financeDtos "assetfindr/internal/app/finance/dtos"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"

	"assetfindr/pkg/common/helpers/calculationhelpers"
	"time"

	"gopkg.in/guregu/null.v4"
)

type RetreadAssetTyreReq struct {
	VendorName               string                 `json:"vendor_name"`
	PartnerID                string                 `json:"partner_id"`
	BrandName                string                 `json:"brand_name"`
	RetreadType              string                 `json:"retread_type"`
	OriginalStartThreadDepth float64                `json:"original_start_thread_depth"`
	StartThreadDepth         float64                `json:"start_thread_depth"`
	Cost                     int                    `json:"cost"`
	RetreadDate              string                 `json:"retread_date"`
	TypeCode                 string                 `json:"type_code"`
	Photos                   []commonmodel.PhotoReq `json:"photos"`
	Notes                    null.String            `json:"notes"`
	TyresTreadConfigID       null.String            `json:"tyres_tread_config_id"`
}

type RetreadAssetTyreRes struct {
	ID             string  `json:"id"`
	AssetID        string  `json:"asset_id"`
	ThreadSequence int     `json:"thread_sequence"`
	AverageRTD     float64 `json:"average_rtd"`
	TotalKM        int     `json:"total_km"`
	TotalHM        float64 `json:"total_hm"`
	TotalLifetime  int     `json:"total_lifetime"`
	TypeCode       string  `json:"type_code"`

	Notes null.String `json:"notes"`

	Type commonmodel.ConstantModel `json:"type,omitempty"`

	VendorName       string  `json:"vendor_name"`
	PartnerID        string  `json:"partner_id"`
	PartnerName      string  `json:"partner_name"`
	BrandName        string  `json:"brand_name"`
	RetreadType      string  `json:"retread_type"`
	StartThreadDepth float64 `json:"start_thread_depth"`
	OriginalTd       float64 `json:"original_td"`

	UtilizationRatePercentage float64    `json:"utilization_rate_percentage"`
	ProjectedLifeKM           null.Int   `json:"projected_life_km"`
	ProjectedLifeHm           null.Float `json:"projected_life_hm"`

	Cost        int       `json:"cost"`
	RunningCost float64   `json:"running_cost"`
	CreatedAt   time.Time `json:"created_at"`
	RetreadDate time.Time `json:"retread_date"`
	TotalCPK    float64   `json:"total_cpk"`
	TotalCPH    float64   `json:"total_cph"`

	TreadConfig RetreadTreadConfigRes `json:"tread_config"`
}

type RetreadTreadConfigRes struct {
	ID                 string `json:"id"`
	BrandName          string `json:"brand_name"`
	RetreadType        string `json:"retread_type"`
	OriginalTreadDepth int    `json:"original_tread_depth"`
	Width              int    `json:"width"`
	Weight             int    `json:"weight"`
}

func (ratr *RetreadAssetTyreRes) Set(tread models.AssetTyreTread, amount financeDtos.ReferenceTotalAmount, partnerName string, calculateRunningCost bool) {
	ratr.ID = tread.ID
	ratr.AssetID = tread.AssetID
	ratr.ThreadSequence = tread.ThreadSequence
	ratr.AverageRTD = tread.AverageRTD
	ratr.TotalKM = tread.TotalKM
	ratr.TotalLifetime = tread.TotalLifetime
	ratr.VendorName = tread.VendorName.String
	ratr.PartnerID = tread.PartnerID
	ratr.PartnerName = partnerName
	ratr.BrandName = tread.BrandName
	ratr.RetreadType = tread.RetreadType
	ratr.StartThreadDepth = tread.StartThreadDepth
	ratr.OriginalTd = tread.OriginalTd
	ratr.CreatedAt = tread.CreatedAt
	ratr.Cost = amount.AmountFixedAsset
	ratr.RunningCost = float64(amount.Amount)
	if calculateRunningCost {
		ratr.RunningCost = helpers.CalculateTyreTreadRunningCost(tread.StartThreadDepth, tread.AverageRTD, amount.Amount)
	}
	ratr.RetreadDate = tread.RetreadDate
	ratr.TypeCode = tread.TypeCode
	ratr.Type = commonmodel.ConstantModel(tread.Type)
	ratr.Notes = tread.Notes
	tUR := 0.00
	if tread.StartThreadDepth > 0 {
		tUR = helpers.CalculateTyreUtilRate(tread.StartThreadDepth, ratr.AverageRTD)
	}
	ratr.UtilizationRatePercentage = tUR
	ratr.TotalKM = tread.TotalKM
	if tread.TotalKM > 0 {
		ratr.TotalCPK = ratr.RunningCost / float64(ratr.TotalKM)
		if tread.StartThreadDepth > 0 && calculateRunningCost {
			projectedLifeKM := helpers.CalculateTyreProjectedLife(tread.TotalKM, tUR)
			ratr.ProjectedLifeKM = null.IntFrom(int64(projectedLifeKM))
		}
	}

	if tread.TotalHm > 0 {
		totalHm := calculationhelpers.Div100(tread.TotalHm)
		ratr.TotalHM = totalHm
		ratr.TotalCPH = ratr.RunningCost / ratr.TotalHM
		if tread.StartThreadDepth > 0 && calculateRunningCost {
			projectedLifeHm := helpers.CalculateTyreProjectedLifeHM(totalHm, tUR)
			ratr.ProjectedLifeHm = null.FloatFrom(projectedLifeHm)
		}
	}

	ratr.TreadConfig = RetreadTreadConfigRes{
		ID:                 tread.TreadConfig.ID,
		BrandName:          tread.TreadConfig.BrandName,
		RetreadType:        tread.TreadConfig.RetreadType,
		OriginalTreadDepth: tread.TreadConfig.OriginalTreadDepth,
		Width:              tread.TreadConfig.Width,
		Weight:             tread.TreadConfig.Weight,
	}
}
