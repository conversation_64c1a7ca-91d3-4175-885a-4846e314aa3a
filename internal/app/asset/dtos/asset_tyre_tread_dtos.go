package dtos

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	financeDtos "assetfindr/internal/app/finance/dtos"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"math"

	"assetfindr/pkg/common/helpers/calculationhelpers"
	"time"

	"gopkg.in/guregu/null.v4"
)

type RetreadAssetTyreReq struct {
	VendorName               string                 `json:"vendor_name"`
	PartnerID                string                 `json:"partner_id"`
	BrandName                string                 `json:"brand_name"`
	RetreadType              string                 `json:"retread_type"`
	OriginalStartThreadDepth float64                `json:"original_start_thread_depth"`
	StartThreadDepth         float64                `json:"start_thread_depth"`
	Cost                     int                    `json:"cost"`
	RetreadDate              string                 `json:"retread_date"`
	TypeCode                 string                 `json:"type_code"`
	Photos                   []commonmodel.PhotoReq `json:"photos"`
	Notes                    null.String            `json:"notes"`
	TyresTreadConfigID       null.String            `json:"tyres_tread_config_id"`
}

type RetreadAssetTyreRes struct {
	ID             string  `json:"id"`
	AssetID        string  `json:"asset_id"`
	ThreadSequence int     `json:"thread_sequence"`
	AverageRTD     float64 `json:"average_rtd"`
	TotalKM        int     `json:"total_km"`
	TotalHM        float64 `json:"total_hm"`
	TotalLifetime  int     `json:"total_lifetime"`
	TypeCode       string  `json:"type_code"`

	Notes null.String `json:"notes"`

	Type commonmodel.ConstantModel `json:"type,omitempty"`

	VendorName       string  `json:"vendor_name"`
	PartnerID        string  `json:"partner_id"`
	PartnerName      string  `json:"partner_name"`
	BrandName        string  `json:"brand_name"`
	RetreadType      string  `json:"retread_type"`
	StartThreadDepth float64 `json:"start_thread_depth"`
	OriginalTd       float64 `json:"original_td"`

	UtilizationRatePercentage float64    `json:"utilization_rate_percentage"`
	ProjectedLifeKM           null.Int   `json:"projected_life_km"`
	ProjectedLifeHm           null.Float `json:"projected_life_hm"`

	TreadWearRateKm float64 `json:"tread_wear_rate_km"`
	TreadWearRateHm float64 `json:"tread_wear_rate_hm"`

	Cost        int       `json:"cost"`
	RunningCost float64   `json:"running_cost"`
	CreatedAt   time.Time `json:"created_at"`
	RetreadDate time.Time `json:"retread_date"`
	TotalCPK    float64   `json:"total_cpk"`
	TotalCPH    float64   `json:"total_cph"`

	TreadConfig RetreadTreadConfigRes `json:"tread_config"`
}

type RetreadTreadConfigRes struct {
	ID                 string `json:"id"`
	BrandName          string `json:"brand_name"`
	RetreadType        string `json:"retread_type"`
	OriginalTreadDepth int    `json:"original_tread_depth"`
	Width              int    `json:"width"`
	Weight             int    `json:"weight"`
}

func BuildRetreadAssetTyreRes(assetTyre *models.AssetTyre, tread models.AssetTyreTread, amount financeDtos.ReferenceTotalAmount, partnerName string, calculateRunningCost bool) RetreadAssetTyreRes {
	if constants.IsTyreStatusNotUsingRunningCostCalculation(assetTyre.Asset.AssetStatusCode) {
		calculateRunningCost = false
	}

	ratr := RetreadAssetTyreRes{
		ID:               tread.ID,
		AssetID:          tread.AssetID,
		ThreadSequence:   tread.ThreadSequence,
		AverageRTD:       tread.AverageRTD,
		TotalKM:          tread.TotalKM,
		TotalLifetime:    tread.TotalLifetime,
		TypeCode:         tread.TypeCode,
		Notes:            tread.Notes,
		Type:             commonmodel.ConstantModel(tread.Type),
		VendorName:       tread.VendorName.String,
		PartnerID:        tread.PartnerID,
		PartnerName:      partnerName,
		BrandName:        tread.BrandName,
		RetreadType:      tread.RetreadType,
		StartThreadDepth: tread.StartThreadDepth,
		OriginalTd:       tread.OriginalTd,
		CreatedAt:        tread.CreatedAt,
		Cost:             amount.AmountFixedAsset,
		RunningCost:      float64(amount.Amount),
		RetreadDate:      tread.RetreadDate,
		TreadConfig: RetreadTreadConfigRes{
			ID:                 tread.TreadConfig.ID,
			BrandName:          tread.TreadConfig.BrandName,
			RetreadType:        tread.TreadConfig.RetreadType,
			OriginalTreadDepth: tread.TreadConfig.OriginalTreadDepth,
			Width:              tread.TreadConfig.Width,
			Weight:             tread.TreadConfig.Weight,
		},
		TotalHM:                   calculationhelpers.Div100(tread.TotalHm),
		UtilizationRatePercentage: helpers.CalculateTyreUtilRate(tread.OriginalTd, tread.AverageRTD),
		ProjectedLifeKM:           null.Int{},
		ProjectedLifeHm:           null.Float{},
		TotalCPK:                  0,
		TotalCPH:                  0,
	}

	if tread.OriginalTd > 0 {
		ratr.RunningCost *= tread.StartThreadDepth / tread.OriginalTd
	}

	if calculateRunningCost {
		ratr.RunningCost = helpers.CalculateTyreTreadRunningCost(tread.OriginalTd, tread.StartThreadDepth, tread.AverageRTD, amount.Amount)
	}

	if tread.TotalKM > 0 {
		totalKM := tread.TotalKM
		prevKM := 0
		if tread.ThreadSequence == 0 {
			prevKM = int(assetTyre.PrevTotalKM.Int64)
			totalKM -= prevKM
		}

		if totalKM > 0 {
			ratr.TotalCPK = ratr.RunningCost / float64(totalKM)
		}
		if tread.StartThreadDepth > 0 && calculateRunningCost {
			ratr.ProjectedLifeKM = null.IntFrom(int64(prevKM + helpers.CalculateTyreProjectedLife(totalKM, tread.StartThreadDepth, tread.AverageRTD)))
		}
	}

	if tread.TotalHm > 0 {
		totalHM := calculationhelpers.Div100(tread.TotalHm)
		prevHM := 0.0
		if tread.ThreadSequence == 0 {
			prevHM = calculationhelpers.Div100(int(assetTyre.PrevTotalHm.Int64))
			totalHM -= prevHM
		}

		if totalHM > 0 {
			ratr.TotalCPH = ratr.RunningCost / totalHM
		}

		if tread.StartThreadDepth > 0 && calculateRunningCost {
			ratr.ProjectedLifeHm = null.FloatFrom(prevHM + helpers.CalculateTyreProjectedLifeHM(totalHM, tread.StartThreadDepth, tread.AverageRTD))
		}
	}

	return ratr
}

// Deprecated
func (ratr *RetreadAssetTyreRes) Set(tread models.AssetTyreTread, amount financeDtos.ReferenceTotalAmount, partnerName string, calculateRunningCost bool) {
	ratr.ID = tread.ID
	ratr.AssetID = tread.AssetID
	ratr.ThreadSequence = tread.ThreadSequence
	ratr.AverageRTD = tread.AverageRTD
	ratr.TotalKM = tread.TotalKM
	ratr.TotalLifetime = tread.TotalLifetime
	ratr.VendorName = tread.VendorName.String
	ratr.PartnerID = tread.PartnerID
	ratr.PartnerName = partnerName
	ratr.BrandName = tread.BrandName
	ratr.RetreadType = tread.RetreadType
	ratr.StartThreadDepth = tread.StartThreadDepth
	ratr.OriginalTd = tread.OriginalTd
	ratr.CreatedAt = tread.CreatedAt
	ratr.Cost = amount.AmountFixedAsset
	ratr.RunningCost = float64(amount.Amount)
	if calculateRunningCost {
		ratr.RunningCost = helpers.CalculateTyreTreadRunningCost(tread.OriginalTd, tread.StartThreadDepth, tread.AverageRTD, amount.Amount)
	}
	ratr.RetreadDate = tread.RetreadDate
	ratr.TypeCode = tread.TypeCode
	ratr.Type = commonmodel.ConstantModel(tread.Type)
	ratr.Notes = tread.Notes
	ratr.UtilizationRatePercentage = math.Round(helpers.CalculateTyreUtilRate(tread.OriginalTd, ratr.AverageRTD)*100) / 100
	ratr.TotalKM = tread.TotalKM
	if tread.TotalKM > 0 {
		ratr.TotalCPK = ratr.RunningCost / float64(ratr.TotalKM)
		if tread.StartThreadDepth > 0 && calculateRunningCost {
			// ratr.ProjectedLifeKM = null.IntFrom(int64(tread.ProjectedLifeKM()))
		}
	}

	if tread.TotalHm > 0 {
		totalHm := calculationhelpers.Div100(tread.TotalHm)
		ratr.TotalHM = totalHm
		ratr.TotalCPH = ratr.RunningCost / ratr.TotalHM
		if tread.StartThreadDepth > 0 && calculateRunningCost {
			// ratr.ProjectedLifeHm = null.FloatFrom(tread.ProjectedLifeHm())
		}
	}

	if treadWear := tread.StartThreadDepth - tread.AverageRTD; treadWear > 0 {
		ratr.TreadWearRateKm = float64(ratr.TotalKM) / treadWear
		ratr.TreadWearRateHm = ratr.TotalHM / treadWear
	}

	ratr.TreadConfig = RetreadTreadConfigRes{
		ID:                 tread.TreadConfig.ID,
		BrandName:          tread.TreadConfig.BrandName,
		RetreadType:        tread.TreadConfig.RetreadType,
		OriginalTreadDepth: tread.TreadConfig.OriginalTreadDepth,
		Width:              tread.TreadConfig.Width,
		Weight:             tread.TreadConfig.Weight,
	}
}
