package dtos

import (
	"assetfindr/internal/app/asset/models"
	userIdentityModels "assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"time"

	"gopkg.in/guregu/null.v4"
)

type AssetLinkedBonusPenaltyElig struct {
	ParentAssetID           string  `json:"parent_asset_id"`
	ChildAssetID            string  `json:"child_asset_id"`
	TyreID                  string  `json:"tyre_id"`
	BrandID                 string  `json:"brand_id"`
	BrandName               string  `json:"brand_name"`
	PatternType             string  `json:"pattern_type"`
	SerialNumber            string  `json:"serial_number"`
	FirstOnLinkedVehicleKm  int     `json:"first_on_linked_vehicle_km"`
	FirstOnLinkedTyreKm     int     `json:"first_on_linked_tyre_km"`
	LastOnUnlinkedVehicleKm int     `json:"last_on_unlinked_vehicle_km"`
	LastOnUnlinkedTyreKm    int     `json:"last_on_unlinked_tyre_km"`
	FirstOnLinkedVehicleHm  float64 `json:"first_on_linked_vehicle_hm"`
	FirstOnLinkedTyreHm     float64 `json:"first_on_linked_tyre_hm"`
	LastOnUnlinkedVehicleHm float64 `json:"last_on_unlinked_vehicle_hm"`
	LastOnUnlinkedTyreHm    float64 `json:"last_on_unlinked_tyre_hm"`
	TotalLinkedTyreKM       int     `json:"total_linked_tyre_km"`
	TotalLinkedTyreHM       float64 `json:"total_linked_tyre_hm"`
	TotalLinkedVehicleKM    int     `json:"total_linked_vehicle_km"`
	TotalLinkedVehicleHM    float64 `json:"total_linked_vehicle_hm"`
}

type AssetLinkedGeneral struct {
	ParentAssetID string `json:"parent_asset_id" binding:"required"`
	ChildAssetID  string `json:"child_asset_id" binding:"required"`
}

type AssetLinkedGeneralListReq struct {
	commonmodel.ListRequest
	ID             string `form:"id"`
	TypeCode       string `form:"type_code"`
	ChildAssetID   string `form:"child_asset_id"`
	ParentAssetID  string `form:"parent_asset_id"`
	RelatedAssetID string `form:"related_asset_id"`
}

type LinkedAsset struct {
	ID                string    `json:"id"`
	AssetName         string    `json:"asset_name"`
	ModelNumber       string    `json:"model_number"`
	SerialNumber      string    `json:"serial_number"`
	ReferenceNumber   string    `json:"reference_number"`
	AssetCategoryCode string    `json:"asset_category_code"`
	ClientID          string    `json:"client_id"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	CreatedBy         string    `json:"created_by"`
	UpdatedBy         string    `json:"updated_by"`
}

func (la *LinkedAsset) Set(asset models.Asset) {
	la.ID = asset.ID
	la.AssetName = asset.Name
	la.ModelNumber = asset.ModelNumber
	la.SerialNumber = asset.SerialNumber
	la.ReferenceNumber = asset.ReferenceNumber
	la.AssetCategoryCode = asset.AssetCategoryCode
	la.ClientID = asset.ClientID
	la.CreatedAt = asset.CreatedAt
	la.UpdatedAt = asset.UpdatedAt
	la.CreatedBy = asset.CreatedBy
	la.UpdatedBy = asset.UpdatedBy
}

type GetAssetLinked struct {
	ID                     string      `json:"id"`
	ChildAssetID           string      `json:"child_asset_id"`
	AssignedToUserFullName string      `json:"assigned_to_user_full_name"`
	ChildAsset             LinkedAsset `json:"child_asset"`
	ParentAssetID          string      `json:"parent_asset_id"`
	ParentAsset            LinkedAsset `json:"parent_asset"`
	LinkedDateTime         time.Time   `json:"linked_datetime"`
	UnlinkedDatetime       *time.Time  `json:"unlinked_datetime"`
	ClientID               string      `json:"client_id"`
	LinkedAssetTypeCode    string      `json:"linked_asset_type_code"`
	CreatedAt              time.Time   `json:"created_at"`
	UpdatedAt              time.Time   `json:"updated_at"`
	CreatedBy              string      `json:"created_by"`
	UpdatedBy              string      `json:"updated_by"`
}

func (al *GetAssetLinked) Set(assetLinked models.AssetLinked, assignedToUserFullName string) {
	al.ID = assetLinked.ID
	al.ChildAssetID = assetLinked.ChildAssetID
	al.AssignedToUserFullName = assignedToUserFullName
	childDto := LinkedAsset{}
	childDto.Set(assetLinked.AssetChild)
	al.ChildAsset = childDto
	al.ParentAssetID = assetLinked.ParentAssetID
	parentDto := LinkedAsset{}
	parentDto.Set(assetLinked.AssetParent)
	al.ParentAsset = parentDto
	al.LinkedDateTime = assetLinked.LinkedDatetime
	al.UnlinkedDatetime = assetLinked.UnlinkedDatetime
	al.ClientID = assetLinked.ClientID
	al.LinkedAssetTypeCode = assetLinked.LinkedAssetTypeCode
	al.CreatedAt = assetLinked.CreatedAt
	al.UpdatedAt = assetLinked.UpdatedAt
	al.CreatedBy = assetLinked.CreatedBy
	al.UpdatedBy = assetLinked.UpdatedBy
}

type AssetLinkedResponse struct {
	ID                        string     `json:"id"`
	ChildAssetID              string     `json:"child_asset_id"`
	ParentAssetID             string     `json:"parent_asset_id"`
	NumberOfTyres             null.Int   `json:"no_of_tyres"`
	LinkedDatetime            time.Time  `json:"linked_datetime"`
	UnlinkedDatetime          *time.Time `json:"unlinked_datetime"`
	ClientID                  string     `json:"client_id"`
	LinkedAssetTypeCode       string     `json:"linked_asset_type_code"`
	TyrePosition              int        `json:"tyre_position"`
	OnLinkedVehicleKm         int        `json:"on_linked_vehicle_km"`
	OnLinkedTyreKm            int        `json:"on_linked_tyre_km"`
	OnUnlinkedVehicleKm       int        `json:"on_unlinked_vehicle_km"`
	OnUnlinkedTyreKm          int        `json:"on_unlinked_tyre_km"`
	OnLinkedVehicleHm         float64    `json:"on_linked_vehicle_hm"`
	OnLinkedTyreHm            float64    `json:"on_linked_tyre_hm"`
	OnUnlinkedVehicleHm       float64    `json:"on_unlinked_vehicle_hm"`
	OnUnlinkedTyreHm          float64    `json:"on_unlinked_tyre_hm"`
	IsMismatch                null.Bool  `json:"is_mismatch"`
	UnlinkChildAssetId        string     `json:"unlink_child_asset_id"`
	VehicleKM                 float64    `json:"vehicle_km"`
	VehicleRegistrationNumber string     `json:"vehicle_registration_number"`
	BrandID                   string     `json:"brand_id"`
	BrandName                 string     `json:"brand_name"`
	PatternType               string     `json:"pattern_type"`
	SerialNumber              string     `json:"serial_number"`
	CreatedBy                 string     `json:"created_by_user_fullname"`
	UpdatedAt                 time.Time  `json:"updated_at" `
	UpdatedBy                 string     `json:"updated_by_user_fullname"`

	CauseOfUnlink      string `json:"cause_of_unlink"`
	UnlinkTyreStatus   string `json:"unlink_tyre_status"`
	IsNeedCreateTicket bool   `json:"is_need_create_ticket"`
	TicketType         string `json:"ticket_type"`
	Notes              string `json:"notes"`
	TicketSubject      string `json:"ticket_subject"`
	TicketCategoryID   string `json:"ticket_category_id"`

	PartnerID   string `json:"vendor_id"`
	PartnerName string `json:"vendor_name"`

	UnlinkScrapStatusRequest UnlinkScrapStatusRequest `json:"unlink_scrap_status_request"`
}

type UnlinkScrapStatusRequest struct {
	ReasonCode    string                 `json:"reason_code"`
	Reason        string                 `json:"reason"`
	SubReasonCode *string                `json:"sub_reason_code"`
	SubReason     string                 `json:"sub_reason"`
	GradeCode     string                 `json:"grade_code"`
	GradeReason   string                 `json:"grade_reason"`
	Photos        []commonmodel.PhotoReq `json:"photos"`
}

func BuildAssetLinkedResponse(assets []models.AssetLinkedAssetVehicleTyre, user map[string]userIdentityModels.User) []AssetLinkedResponse {
	response := []AssetLinkedResponse{}

	for _, asset := range assets {
		response = append(response, AssetLinkedResponse{
			ID:                        asset.AssetLinkedID,
			ChildAssetID:              asset.AssetLinked.ChildAssetID,
			ParentAssetID:             asset.AssetLinked.ParentAssetID,
			NumberOfTyres:             asset.AssetLinked.ParentAsset.NumberOfTyres,
			LinkedDatetime:            asset.AssetLinked.LinkedDatetime,
			UnlinkedDatetime:          asset.AssetLinked.UnlinkedDatetime,
			CreatedBy:                 user[asset.AssetLinked.CreatedBy].GetName(),
			UpdatedAt:                 asset.AssetLinked.UpdatedAt,
			UpdatedBy:                 user[asset.AssetLinked.UpdatedBy].GetName(),
			TyrePosition:              asset.TyrePosition,
			ClientID:                  asset.ClientID,
			OnLinkedVehicleKm:         asset.OnLinkedVehicleKm,
			OnLinkedTyreKm:            asset.OnLinkedTyreKm,
			OnUnlinkedVehicleKm:       asset.OnUnlinkedVehicleKm,
			OnUnlinkedTyreKm:          asset.OnUnlinkedTyreKm,
			OnLinkedVehicleHm:         calculationhelpers.Div100(asset.OnLinkedVehicleHm),
			OnLinkedTyreHm:            calculationhelpers.Div100(asset.OnLinkedTyreHm),
			OnUnlinkedVehicleHm:       calculationhelpers.Div100(asset.OnUnlinkedVehicleHm),
			OnUnlinkedTyreHm:          calculationhelpers.Div100(asset.OnUnlinkedTyreHm),
			IsMismatch:                asset.IsMismatch,
			VehicleRegistrationNumber: asset.AssetLinked.AssetParent.ReferenceNumber,
			BrandID:                   asset.AssetLinked.ChildAsset.Asset.BrandID,
			BrandName:                 asset.AssetLinked.ChildAsset.Asset.Brand.BrandName,
			PatternType:               asset.AssetLinked.ChildAsset.Tyre.PatternType,
			SerialNumber:              asset.AssetLinked.ChildAsset.Asset.SerialNumber,

			PartnerID:   asset.AssetLinked.ParentAsset.Asset.PartnerOwnerID,
			PartnerName: asset.AssetLinked.ParentAsset.Asset.PartnerOwnerName,

			LinkedAssetTypeCode: asset.AssetLinked.LinkedAssetTypeCode,
		})
	}

	return response
}

type CountTyreLinkedReq struct {
	ParentAssetID string `form:"parent_asset_id"`
}

func (r *CountTyreLinkedReq) Validate() error {
	if r.ParentAssetID == "" {
		return errorhandler.ErrBadRequest("'parent_asset_id' should in url query")
	}

	return nil
}

type CountTyreLinkedResp struct {
	NumberOfTyres int `json:"number_of_tyres"`
}
