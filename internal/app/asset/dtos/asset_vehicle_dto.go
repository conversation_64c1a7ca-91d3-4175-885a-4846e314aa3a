package dtos

import (
	"assetfindr/pkg/common/commonmodel"
)

// AssetVehicleMeterStateListReq represents the request for getting asset vehicle meter states
type AssetVehicleMeterStateListReq struct {
	commonmodel.ListRequest
	AssetID   string `json:"asset_id" form:"asset_id"`
	StartDate string `json:"start_date" form:"start_date"`
	EndDate   string `json:"end_date" form:"end_date"`
}

type DeleteAssetVehicleMeterStateReq struct {
	Reason string `json:"reason"`
}
