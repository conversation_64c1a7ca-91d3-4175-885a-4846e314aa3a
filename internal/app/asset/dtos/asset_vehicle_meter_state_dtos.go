package dtos

import (
	"assetfindr/internal/app/asset/models"
	"time"

	"github.com/jackc/pgtype"
)

type AssetVehicleMeterStateListItem struct {
	ID                string    `json:"id"`
	AssetID           string    `json:"asset_id" `
	VehicleKM         int       `json:"vehicle_km"`
	VehicleHm         int       `json:"vehicle_hm"`
	DateTime          time.Time `json:"date_time"`
	UpdatedByUserName string    `json:"updated_by_user_name"`
	UpdatedBy         string    `json:"updated_by"`
	IsLast            bool      `json:"is_last"`
}

func BuildAssetVehicleMeterStateListResponse(assetVehicleMeterStates []models.AssetVehicleMeterState, mapUserName map[string]string) []AssetVehicleMeterStateListItem {
	response := []AssetVehicleMeterStateListItem{}

	for _, assetVehicleMeterState := range assetVehicleMeterStates {
		userId := assetVehicleMeterState.UpdatedBy
		response = append(response, AssetVehicleMeterStateListItem{
			ID:                assetVehicleMeterState.ID,
			AssetID:           assetVehicleMeterState.AssetID,
			VehicleKM:         assetVehicleMeterState.VehicleKM,
			VehicleHm:         assetVehicleMeterState.VehicleHm,
			DateTime:          assetVehicleMeterState.DateTime,
			UpdatedByUserName: mapUserName[userId],
			UpdatedBy:         assetVehicleMeterState.UpdatedBy,
			IsLast:            assetVehicleMeterState.IsLast,
		})
	}

	return response
}

type AssetVehicleMeterStateDetail struct {
	AssetName             string                                    `json:"asset_name"`
	AssetAssignedUserName string                                    `json:"asset_assigned_user_name"`
	AssetID               string                                    `json:"asset_id" `
	VehicleKM             int                                       `json:"vehicle_km"`
	VehicleHm             int                                       `json:"vehicle_hm"`
	DateTime              time.Time                                 `json:"date_time"`
	UpdatedByUserName     string                                    `json:"updated_by_user_name"`
	UpdatedBy             string                                    `json:"updated_by"`
	IsLast                bool                                      `json:"is_last"`
	AxleConfiguration     pgtype.JSONB                              `json:"axle_configuration"`
	AssetLinkeds          []AssetLinkedAssetVehicleMeterStateDetail `json:"asset_linkeds"`
	AssetUnlinkeds        []AssetLinkedAssetVehicleMeterStateDetail `json:"asset_unlinkeds"`
}

type AssetLinkedAssetVehicleMeterStateDetail struct {
	CreatedAt       time.Time `json:"created_at"`
	ChildAssetID    string    `json:"child_asset_id"`
	SerialNumber    string    `json:"serial_number"`
	LinkedStateID   string    `json:"linked_state_id"`
	UnlinkedStateID string    `json:"unlinked_state_id"`
	TyrePosition    int       `json:"tyre_position"`
}

func BuildAssetVehicleMeterStateDetailResponse(assetVehicleMeterState *models.AssetVehicleMeterState, assetAssignedUserName, updatedByUserName string) AssetVehicleMeterStateDetail {
	response := AssetVehicleMeterStateDetail{
		AssetID:               assetVehicleMeterState.AssetID,
		VehicleKM:             assetVehicleMeterState.VehicleKM,
		VehicleHm:             assetVehicleMeterState.VehicleHm,
		DateTime:              assetVehicleMeterState.DateTime,
		UpdatedByUserName:     updatedByUserName,
		UpdatedBy:             assetVehicleMeterState.UpdatedBy,
		IsLast:                assetVehicleMeterState.IsLast,
		AssetName:             assetVehicleMeterState.Asset.Name,
		AssetAssignedUserName: assetAssignedUserName,
		AxleConfiguration:     assetVehicleMeterState.AxleConfiguration,
	}

	for _, assetLinked := range assetVehicleMeterState.AssetLinkeds {
		response.AssetLinkeds = append(response.AssetLinkeds,
			AssetLinkedAssetVehicleMeterStateDetail{
				CreatedAt:       assetLinked.CreatedAt,
				ChildAssetID:    assetLinked.ChildAssetID,
				SerialNumber:    assetLinked.AssetChild.SerialNumber,
				LinkedStateID:   assetLinked.LinkedStateID,
				UnlinkedStateID: assetLinked.UnlinkedStateID,
				TyrePosition:    assetLinked.AssetLinkedAssetVehicleTyre.TyrePosition,
			})
	}

	for _, assetLinked := range assetVehicleMeterState.AssetUnlinkeds {
		response.AssetUnlinkeds = append(response.AssetUnlinkeds,
			AssetLinkedAssetVehicleMeterStateDetail{
				CreatedAt:       assetLinked.CreatedAt,
				ChildAssetID:    assetLinked.ChildAssetID,
				SerialNumber:    assetLinked.AssetChild.SerialNumber,
				LinkedStateID:   assetLinked.LinkedStateID,
				UnlinkedStateID: assetLinked.UnlinkedStateID,
				TyrePosition:    assetLinked.AssetLinkedAssetVehicleTyre.TyrePosition,
			})
	}

	return response
}
