package dtos

import (
	"assetfindr/internal/app/asset/models"
	userIdentityModels "assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
)

type CreateUpdateAsset struct {
	CategoryCode          string                 `json:"category_code" binding:"required"`
	SubCategoryCode       string                 `json:"sub_category_code"`
	CustomCategoryID      string                 `json:"custom_category_id" binding:"required"`
	CustomSubCategoryID   null.String            `json:"custom_sub_category_id"`
	Name                  string                 `json:"name"`
	BrandID               string                 `json:"brand_id"`
	SerialNumber          string                 `json:"serial_number"`
	ReferenceNumber       string                 `json:"reference_number"`
	OwnershipCategoryCode string                 `json:"ownership_category_code" binding:"required"`
	StatusCode            string                 `json:"status_code" binding:"required"`
	LocationID            string                 `json:"location_id"`
	AssignedTo            []AssignedTo           `json:"assigned_to"`
	Photo                 string                 `json:"photo"`
	Photos                []commonmodel.PhotoReq `json:"photos"`
	PartnerOwnerID        string                 `json:"partner_owner_id"`
	PartnerOwnerNo        string                 `json:"partner_owner_no"`
	PartnerOwnerName      string                 `json:"partner_owner_name"`
	TemplateID            null.String            `json:"template_id"`
	FormFields            []commonmodel.FieldReq `json:"additional_form_fields"`
	Rfid                  string                 `json:"rfid"`
	GpsImei               string                 `json:"gps_imei"`
	NeedApproval          bool                   `json:"need_approval"`
	ModelID               string                 `json:"model_id"`
	AssetAssignmentUserID string                 `json:"asset_assignment_user_id"`
	Address               string                 `json:"address"`

	ConvertToFleetOptimax     bool                   `json:"convert_to_fleet_optimax"`
	ConvertToFleetOptimaxData ConvertToOptimax       `json:"convert_to_fleet_optimax_data"`
	AssetVehicle              *UpdateAssetVehicleReq `json:"asset_vehicle"`
}

type UpdateAssetVehicleReq struct {
	VehicleID string `json:"vehicle_id"`
}

type AssignedTo struct {
	UserID string `json:"user_id" binding:"required"`
}

type CustomAssetCategory struct {
	ID                string `json:"id"`
	Name              string `json:"name"`
	AssetCategoryCode string `json:"asset_category_code"`
	Description       string `json:"description"`
}

func (c *CustomAssetCategory) Set(customAssetCategory models.CustomAssetCategory) {
	c.ID = customAssetCategory.ID
	c.Name = customAssetCategory.Name
	c.AssetCategoryCode = customAssetCategory.AssetCategoryCode
	c.Description = customAssetCategory.Description
}

type CustomAssetSubCategory struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

func (c *CustomAssetSubCategory) Set(customAssetSubCategory models.CustomAssetSubCategory) {
	c.ID = customAssetSubCategory.ID
	c.Name = customAssetSubCategory.Name
	c.Description = customAssetSubCategory.Description
}

type GetAssetList struct {
	ID        string    `json:"id"`
	CreatedBy string    `json:"created_by"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedBy string    `json:"updated_by"`

	// Category
	CategoryCode    string                  `json:"category_code"`
	Category        models.AssetCategory    `json:"category"`
	SubCategoryCode string                  `json:"sub_category_code"`
	SubCategory     models.AssetSubCategory `json:"sub_category"`

	// Custom Category
	CustomCategoryID    string                 `json:"custom_category_id"`
	CustomCategory      CustomAssetCategory    `json:"custom_category"`
	CustomSubCategoryID *null.String           `json:"custom_sub_category_id"`
	CustomSubCategory   CustomAssetSubCategory `json:"custom_sub_category"`

	Name                   string                        `json:"name"`
	BrandID                string                        `json:"brand_id"`
	BrandName              string                        `json:"brand_name"`
	SerialNumber           string                        `json:"serial_number"`
	ReferenceNumber        string                        `json:"reference_number"`
	OwnershipCategoryCode  string                        `json:"ownership_category_code"`
	OwnershipCategory      models.AssetOwnershipCategory `json:"ownership_category"`
	StatusCode             string                        `json:"status_code"`
	Status                 models.AssetStatus            `json:"status"`
	LocationName           string                        `json:"location_name"`
	Photo                  null.String                   `json:"photo"`
	GpsImei                string                        `json:"gps_imei"`
	HandoverFormTemplateID null.String                   `json:"handover_form_template_id"`
	HandoverNeedInspection null.Bool                     `json:"handover_need_inspection"`
	ModelID                string                        `json:"model_id"`
	ModelName              string                        `json:"model_name"`
	AssigneeID             string                        `json:"assignee_id"`
	AssigneeName           string                        `json:"assignee_name"`
	Address                string                        `json:"address"`

	UseTyreOptimax  null.Bool `json:"use_tyre_optimax"`
	UseFleetOptimax null.Bool `json:"use_fleet_optimax"`
}

func (a *GetAssetList) Set(asset models.Asset) {
	a.ID = asset.ID
	a.CreatedBy = asset.CreatedBy
	a.CreatedAt = asset.CreatedAt
	a.UpdatedBy = asset.UpdatedBy

	// Category
	a.CategoryCode = asset.AssetCategoryCode
	a.SubCategoryCode = asset.SubCategoryCode
	a.SubCategory = asset.SubCategory
	a.Category = asset.AssetCategory

	// Custom Category
	a.CustomCategoryID = asset.CustomAssetCategoryID
	a.CustomSubCategoryID = asset.CustomAssetSubCategoryID
	customAssetCategory := CustomAssetCategory{}
	customAssetCategory.Set(asset.CustomAssetCategory)
	a.CustomCategory = customAssetCategory
	customAssetSubCategory := CustomAssetSubCategory{}
	customAssetSubCategory.Set(asset.CustomAssetSubCategory)
	a.CustomSubCategory = customAssetSubCategory

	a.Name = asset.Name
	a.BrandID = asset.BrandID
	a.BrandName = asset.Brand.BrandName
	a.SerialNumber = asset.SerialNumber
	a.ReferenceNumber = asset.ReferenceNumber
	a.OwnershipCategoryCode = asset.OwnershipCategoryCode
	a.OwnershipCategory = asset.OwnershipCategory
	a.StatusCode = asset.AssetStatusCode
	a.Status = asset.AssetStatus
	a.LocationName = asset.Location.Name
	a.Photo = asset.Photo
	a.GpsImei = asset.GpsImei
	a.HandoverFormTemplateID = asset.HandoverFormTemplateID
	a.HandoverNeedInspection = asset.HandoverNeedInspection
	a.ModelID = asset.AssetModel.ID
	a.ModelName = asset.AssetModel.AssetModelName
	a.Address = asset.Address
	if asset.AssetAssignment != nil && asset.AssetAssignment.UserID != "" {
		a.AssigneeID = asset.AssetAssignment.UserID
	}

	a.UseTyreOptimax = asset.UseTyreOptimax
	a.UseFleetOptimax = asset.UseFleetOptimax
}

type GetAssetDetail struct {
	ID        string    `json:"id"`
	CreatedBy string    `json:"created_by"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedBy string    `json:"updated_by"`

	// Category
	CategoryCode    string                  `json:"category_code"`
	Category        models.AssetCategory    `json:"category"`
	SubCategoryCode string                  `json:"sub_category_code"`
	SubCategory     models.AssetSubCategory `json:"sub_category"`

	// Custom Category
	CustomCategoryID    string                 `json:"custom_category_id"`
	CustomCategory      CustomAssetCategory    `json:"custom_category"`
	CustomSubCategoryID *null.String           `json:"custom_sub_category_id"`
	CustomSubCategory   CustomAssetSubCategory `json:"custom_sub_category"`

	Name                   string                         `json:"name"`
	BrandID                string                         `json:"brand_id"`
	BrandName              string                         `json:"brand_name"`
	SerialNumber           string                         `json:"serial_number"`
	ReferenceNumber        string                         `json:"reference_number"`
	OwnershipCategoryCode  string                         `json:"ownership_category_code"`
	OwnershipCategory      models.AssetOwnershipCategory  `json:"ownership_category"`
	StatusCode             string                         `json:"status_code"`
	Status                 models.AssetStatus             `json:"status"`
	LocationID             string                         `json:"location_id"`
	LocationName           string                         `json:"location_name"`
	CostPrice              string                         `json:"cost"`
	PurchasedDate          string                         `json:"purchased_date"`
	Assigned               Assigned                       `json:"assigned"`
	AssignedTo             []GetAssignedTo                `json:"assigned_to"`
	Photo                  null.String                    `json:"photo"`
	Rfid                   string                         `json:"rfid"`
	GpsImei                string                         `json:"gps_imei"`
	InitialConditionCode   string                         `json:"initial_condition_code"`
	AssetVehicle           *AssetAssignmentDetailResponse `json:"asset_vehicle"`
	HandoverFormTemplateID null.String                    `json:"handover_form_template_id"`
	HandoverNeedInspection null.Bool                      `json:"handover_need_inspection"`
	ModelID                string                         `json:"model_id"`
	Models                 models.AssetModel              `json:"models"`
	Address                string                         `json:"address"`

	UseTyreOptimax  null.Bool `json:"use_tyre_optimax"`
	UseFleetOptimax null.Bool `json:"use_fleet_optimax"`
}

type GetAssignedTo struct {
	ID     string `json:"id"`
	UserID string `json:"user_id"`
	Name   string `json:"name"`
}

func (c *GetAssignedTo) Set(assigned models.AssetAssignmentGroup, user userIdentityModels.User) {
	c.ID = assigned.ID
	c.UserID = assigned.UserID
	c.Name = user.GetName()
}

type Assigned struct {
	ID          string `json:"id"`
	UserID      string `json:"user_id"`
	Name        string `json:"name"`
	PhoneNumber string `json:"phone_number"`
}

func (c *Assigned) Set(assign models.AssetAssignment, user userIdentityModels.User) {
	c.ID = assign.ID
	c.UserID = assign.UserID
	c.Name = user.GetName()
	c.PhoneNumber = user.PhoneNumber
}

func (a *GetAssetDetail) Set(asset models.Asset, assigned []models.AssetAssignmentGroup, user userIdentityModels.User, assign models.AssetAssignment, assetVehicle *AssetAssignmentDetailResponse) {
	a.ID = asset.ID
	a.CreatedBy = asset.CreatedBy
	a.CreatedAt = asset.CreatedAt
	a.UpdatedBy = asset.UpdatedBy
	a.Name = asset.Name
	a.BrandID = asset.BrandID
	a.BrandName = asset.Brand.BrandName
	a.SerialNumber = asset.SerialNumber
	a.ReferenceNumber = asset.ReferenceNumber

	// Category
	a.CategoryCode = asset.AssetCategoryCode
	a.Category = asset.AssetCategory
	a.SubCategoryCode = asset.SubCategoryCode
	a.SubCategory = asset.SubCategory

	// Custom Category
	a.CustomCategoryID = asset.CustomAssetCategoryID
	a.CustomSubCategoryID = asset.CustomAssetSubCategoryID
	customAssetCategory := CustomAssetCategory{}
	customAssetCategory.Set(asset.CustomAssetCategory)
	a.CustomCategory = customAssetCategory
	customAssetSubCategory := CustomAssetSubCategory{}
	customAssetSubCategory.Set(asset.CustomAssetSubCategory)
	a.CustomSubCategory = customAssetSubCategory

	a.OwnershipCategoryCode = asset.OwnershipCategoryCode
	a.OwnershipCategory = asset.OwnershipCategory
	a.StatusCode = asset.AssetStatusCode
	a.Status = asset.AssetStatus
	a.LocationID = asset.LocationID
	a.LocationName = asset.Location.Name
	a.Photo = asset.Photo

	assignDTO := Assigned{}
	assignDTO.Set(assign, user)
	a.Assigned = assignDTO
	assigneds := []GetAssignedTo{}
	for _, val := range assigned {
		contactDTO := GetAssignedTo{}
		contactDTO.Set(val, user)
		assigneds = append(assigneds, contactDTO)
	}
	a.AssignedTo = assigneds
	a.Rfid = asset.Rfid
	a.GpsImei = asset.GpsImei
	a.InitialConditionCode = asset.InitialConditionCode
	a.AssetVehicle = assetVehicle

	a.UseTyreOptimax = asset.UseTyreOptimax
	a.UseFleetOptimax = asset.UseFleetOptimax
}

type AssetManagementListReq struct {
	commonmodel.ListRequest
	StatusCodes           []string `form:"status_codes"`
	PartnerOwnerID        string   `form:"partner_owner_id"`
	Categories            []string `form:"categories"`
	SubCategories         []string `form:"sub_categories"`
	CustomCategories      []string `form:"custom_categories"`
	CustomSubCategories   []string `form:"custom_sub_categories"`
	LocationIds           []string `form:"location_ids"`
	CreateOn              string   `form:"create_on"`
	CreatedStartDate      string   `form:"created_start_date"`
	CreatedEndDate        string   `form:"created_end_date"`
	IsAssignedToUserLogin bool     `form:"is_assigned_to_user_login"`
	OptimaxStatuses       []string `form:"optimax_statuses"`
	NotLinkedToAssetID    string   `form:"not_linked_to_asset_id"`
}

type AssetListReq struct {
	commonmodel.ListRequest
	StatusCodes []string `form:"status_codes"`
	ClientID    string   `form:"client_id"`
}
