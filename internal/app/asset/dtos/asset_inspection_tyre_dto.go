package dtos

import (
	"assetfindr/internal/app/asset/models"

	"gopkg.in/guregu/null.v4"
)

type AssetInspectionTyreResp struct {
	AssetInspectionID       string      `json:"asset_inspection_id"`
	AssetTyreID             string      `json:"asset_tyre_id"`
	Remark                  string      `json:"remark"`
	AssetAssignmentID       string      `json:"asset_assignment_id"`
	Pressure                float64     `json:"pressure"`
	RDT1                    float64     `json:"rdt1"`
	RDT2                    float64     `json:"rdt2"`
	RDT3                    float64     `json:"rdt3"`
	RDT4                    float64     `json:"rdt4"`
	TyrePosition            float64     `json:"tyre_position"`
	AverageRTD              float64     `json:"average_rtd"`
	ClientID                string      `json:"client_id"`
	PressureStatusCode      string      `json:"pressure_status_code"`
	TyreKM                  int         `json:"tyre_km"`
	TyreHm                  int         `json:"tyre_hm"`
	DeviceID                string      `json:"device_id"`
	FailedVisualChecking    null.Bool   `json:"failed_visual_checking"`
	RequireRotationTyre     null.Bool   `json:"require_rotation_tyre"`
	RequireSpooringVehicle  null.Bool   `json:"require_spooring_vehicle"`
	CustomSerialNumber      string      `json:"custom_serial_number"`
	TireTreadAndRimDamage   null.Bool   `json:"tire_tread_and_rim_damage"`
	CustomBrandName         null.String `json:"custom_brand_name"`
	CustomTyreSize          string      `json:"custom_tyre_size"`
	NumberOfInspectionPoint int         `json:"number_of_inspection_point"`
	PressureSensorRef       string      `json:"pressure_sensor_ref"`
	TemperatureSensorRef    string      `json:"temperature_sensor_ref"`
	IsMismatch              null.Bool   `json:"is_mismatch"`

	Temperature null.Float `json:"temperature"`
}

func BuildAssetInspectionTyreResp(assetInspectionTyre models.AssetInspectionTyre) AssetInspectionTyreResp {
	return AssetInspectionTyreResp{
		AssetInspectionID:       assetInspectionTyre.AssetInspectionID,
		AssetTyreID:             assetInspectionTyre.AssetTyreID,
		Remark:                  assetInspectionTyre.Remark,
		AssetAssignmentID:       assetInspectionTyre.AssetAssignmentID,
		Pressure:                assetInspectionTyre.Pressure,
		RDT1:                    assetInspectionTyre.RDT1,
		RDT2:                    assetInspectionTyre.RDT2,
		RDT3:                    assetInspectionTyre.RDT3,
		RDT4:                    assetInspectionTyre.RDT4,
		TyrePosition:            assetInspectionTyre.TyrePosition,
		AverageRTD:              assetInspectionTyre.AverageRTD,
		ClientID:                assetInspectionTyre.ClientID,
		PressureStatusCode:      assetInspectionTyre.PressureStatusCode,
		TyreKM:                  assetInspectionTyre.TyreKM,
		TyreHm:                  assetInspectionTyre.TyreHm,
		DeviceID:                assetInspectionTyre.DeviceID,
		FailedVisualChecking:    assetInspectionTyre.FailedVisualChecking,
		RequireRotationTyre:     assetInspectionTyre.RequireRotationTyre,
		RequireSpooringVehicle:  assetInspectionTyre.RequireSpooringVehicle,
		CustomSerialNumber:      assetInspectionTyre.CustomSerialNumber,
		TireTreadAndRimDamage:   assetInspectionTyre.TireTreadAndRimDamage,
		CustomBrandName:         assetInspectionTyre.CustomBrandName,
		CustomTyreSize:          assetInspectionTyre.CustomTyreSize,
		NumberOfInspectionPoint: assetInspectionTyre.NumberOfInspectionPoint,
		PressureSensorRef:       assetInspectionTyre.PressureSensorRef,
		TemperatureSensorRef:    assetInspectionTyre.TemperatureSensorRef,
		IsMismatch:              assetInspectionTyre.IsMismatch,
		Temperature:             assetInspectionTyre.Temperature,
	}
}
