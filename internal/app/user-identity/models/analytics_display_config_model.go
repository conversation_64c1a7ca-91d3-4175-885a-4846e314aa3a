package models

import (
	"assetfindr/pkg/common/commonmodel"

	"gorm.io/gorm"
)

type AnalyticDisplayConfig struct {
	commonmodel.ModelV2
	AnalyticCode string `gorm:"type:varchar(40);not null" json:"analytic_code"`
	Sequence     int    `json:"sequence"`
	UserID       string `json:"client_id"`
	IsActive     bool   `json:"is_active"`

	Analytic Analytic `gorm:"foreignKey:AnalyticCode" json:"analytic"`
}

func (AnalyticDisplayConfig) TableName() string {
	return "uis_analytic_display_configs"
}

func (adc *AnalyticDisplayConfig) BeforeCreate(db *gorm.DB) error {
	adc.SetUUID("adc")
	adc.ModelV2.BeforeCreate(db)
	return nil
}

func (adc *AnalyticDisplayConfig) BeforeUpdate(db *gorm.DB) error {
	adc.ModelV2.BeforeUpdate(db)
	return nil
}

type AnalyticDisplayConfigPreload struct {
	Analytic bool
}
