package models

import (
	"assetfindr/pkg/common/commonmodel"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

// Model for uis_user_devices
type AuthenticationLog struct {
	commonmodel.Model
	UserID        string      `gorm:"type:varchar(40);not null" json:"user_id"`
	DeviceTypeRef null.String `gorm:"type:varchar(20);default:null" json:"device_type_ref"`
	LogType       string      `gorm:"type:varchar(20);not null" json:"log_type"`
	ClientID      string      `gorm:"type:varchar(40);not null" json:"client_id"`
}

func (m *AuthenticationLog) TableName() string {
	return "uis_authentication_log"
}

func (m *AuthenticationLog) BeforeCreate(tx *gorm.DB) error {
	if m.ID == "" {
		m.SetUUID("ual")
	}

	return nil
}
