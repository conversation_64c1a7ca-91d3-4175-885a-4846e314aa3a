package repository

import (
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type UserRepository interface {
	CreateUser(ctx context.Context, dB database.DBI, user *models.User) error
	GetUserByField(ctx context.Context, dB database.DBI, user *models.User, columnName string, columnValue string) error
	GetUserById(ctx context.Context, dB database.DBI, user *models.User, userId string) error
	GetUsersByIds(ctx context.Context, dB database.DBI, user *[]models.User, userIds []string) error
	GetUsersInMapByIds(ctx context.Context, dB database.DBI, usersMapById *map[string]models.User, userIds []string) error

	GetUser(ctx context.Context, dB database.DBI, condition models.UserCondition) (*models.User, error)
	GetClient(ctx context.Context, dB database.DBI, condition models.ClientCondition) (*models.Client, error)
	GetClients(ctx context.Context, dB database.DBI, condition models.ClientCondition) ([]models.Client, error)
	GetUsersV2(ctx context.Context, dB database.DBI, condition models.UserCondition) ([]models.User, error)
	// Temporary Repository / API. will remove this later
	GetUsers(ctx context.Context, dB database.DBI, user *[]models.User) error

	UpdateUser(ctx context.Context, dB database.DBI, user *models.User) error
	UpdateClient(ctx context.Context, dB database.DBI, user *models.Client) error

	GetUserList(ctx context.Context, dB database.DBI, param models.GetUserListParam) (int, []models.User, error)

	CreateClient(ctx context.Context, dB database.DBI, client *models.Client) error
	CreateUserClient(ctx context.Context, dB database.DBI, userClient *models.UserClient) error
	UpdateUserClient(ctx context.Context, dB database.DBI, userClient *models.UserClient) error

	GetDigispectPackages(ctx context.Context, dB database.DBI) ([]models.DigispectPackage, error)

	GetUserClients(ctx context.Context, dB database.DBI, condition models.UserClientCondition) ([]models.UserClient, error)
	GetUserClient(ctx context.Context, dB database.DBI, condition models.UserClientCondition) (*models.UserClient, error)

	CreateUserDevice(ctx context.Context, dB database.DBI, device *models.UserDevice) error
	GetUserFirebaseDeviceTokens(ctx context.Context, dB database.DBI, userID string) ([]string, error)
	GetUserDevice(ctx context.Context, dB database.DBI, condition models.UserDeviceCondition) (*models.UserDevice, error)
	UpdateUserDevice(ctx context.Context, dB database.DBI, device *models.UserDevice) error
	GetClientByAlias(ctx context.Context, dB database.DBI, clientAlias string) (models.Client, error)

	GetUserByClientAliasAndFirebaseId(ctx context.Context, dB database.DBI, user *models.User, clientAlias string, firebaseToken string) error
	CreateAuthenticationLog(ctx context.Context, dB database.DBI, authLog *models.AuthenticationLog) error
}
