package repository

import (
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type AnalyticRepository interface {
	UpsertAnalyticDisplayConfig(ctx context.Context, dB database.DBI, analyticDisCon *[]models.AnalyticDisplayConfig) error
	GetAnalyticDisplayConfigs(ctx context.Context, dB database.DBI, clientID string, userID string, where models.AnalyticDisplayConfigWhere, preload models.AnalyticDisplayConfigPreload) ([]models.AnalyticDisplayConfig, error)

	GetAnalytics(ctx context.Context, dB database.DBI, where models.AnalyticWhere) ([]models.Analytic, error)
}
