package handler

import (
	"assetfindr/internal/app/user-identity/dtos"
	"assetfindr/internal/app/user-identity/usecase"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type AnalyticHandler struct {
	AnalyticUseCase *usecase.AnalyticUseCase
}

func NewAnalyticHandler(
	analyticUseCase *usecase.AnalyticUseCase,
) *AnalyticHandler {
	return &AnalyticHandler{
		AnalyticUseCase: analyticUseCase,
	}
}

func (h *AnalyticHandler) GetAnalyticDisplayConfigs(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAnalyticDisplayConfigRequest{}
	err := c.ShouldBindQuery(&req)
	if err != nil {
		commonlogger.Errorf("Failed when get analytic dispaly config request: %v", err)
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AnalyticUseCase.GetAnalyticDisplayConfigs(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *AnalyticHandler) UpsertAnalyticDisplayConfigs(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.AnalyticDisplayConfigRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	resp, err := h.AnalyticUseCase.UpsertAnalyticDisplayConfigs(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusCreated, resp)
}
