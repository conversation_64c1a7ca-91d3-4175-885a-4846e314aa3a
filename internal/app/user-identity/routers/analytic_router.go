package routers

import (
	"assetfindr/internal/app/user-identity/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterAnalyticRoutes(route *gin.Engine, analyticHandler *handler.AnalyticHandler) *gin.Engine {
	analyticRoutes := route.Group("/v1/analytics/display-config", middleware.TokenValidationMiddleware())
	{
		analyticRoutes.GET("", analyticHandler.GetAnalyticDisplayConfigs)
		analyticRoutes.POST("", analyticHandler.UpsertAnalyticDisplayConfigs)
	}

	return route
}
