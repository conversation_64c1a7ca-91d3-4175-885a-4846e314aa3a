package dtos

import (
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonmodel"
	"time"
)

type CreatePartner struct {
	Name            string          `json:"name" binding:"required"`
	ServiceProvided string          `json:"service_provided" binding:"required"`
	StatusCode      string          `json:"status_code" binding:"required"`
	PartnerTypeCode string          `json:"partner_type_code"`
	Address         string          `json:"address" binding:"required"`
	Floor           string          `json:"floor"`
	Unit            string          `json:"unit"`
	MapLat          float64         `json:"map_lat" binding:"required"`
	MapLong         float64         `json:"map_long" binding:"required"`
	PhoneNumber1    string          `json:"phone_number_1" binding:"required"`
	PhoneNumber2    string          `json:"phone_number_2"`
	Email           string          `json:"email" binding:"required"`
	Notes           string          `json:"notes"`
	TaxIdentity     string          `json:"tax_identity"`
	ContactList     []CreateContact `json:"contact_list" binding:"required"`
}

type UpdatePartner struct {
	Name            string          `json:"name"`
	ServiceProvided string          `json:"service_provided"`
	StatusCode      string          `json:"status_code"`
	Address         string          `json:"address"`
	Floor           string          `json:"floor"`
	Unit            string          `json:"unit"`
	MapLat          float64         `json:"map_lat"`
	MapLong         float64         `json:"map_long"`
	PhoneNumber1    string          `json:"phone_number_1"`
	PhoneNumber2    string          `json:"phone_number_2"`
	Email           string          `json:"email"`
	Notes           string          `json:"notes"`
	ContactList     []CreateContact `json:"contact_list"`
}

func (u *UpdatePartner) ToCreate() CreatePartner {
	return CreatePartner{
		Name:            u.Name,
		ServiceProvided: u.ServiceProvided,
		StatusCode:      u.StatusCode,
		Address:         u.Address,
		Floor:           u.Floor,
		Unit:            u.Unit,
		MapLat:          u.MapLat,
		MapLong:         u.MapLong,
		PhoneNumber1:    u.PhoneNumber1,
		PhoneNumber2:    u.PhoneNumber2,
		Email:           u.Email,
		Notes:           u.Notes,
		ContactList:     u.ContactList,
	}
}

type PartnerListReq struct {
	commonmodel.ListRequest
	ShowDeleted     bool   `form:"show_deleted"`
	StatusCode      string `form:"status_code"`
	PartnerTypeCode string `form:"partner_type_code"`
}

type GetPartner struct {
	ID              string       `json:"id"`
	CreatedBy       string       `json:"created_by"`
	CreatedAt       time.Time    `json:"created_at"`
	UpdatedBy       string       `json:"updated_by"`
	Name            string       `json:"name"`
	ServiceProvided string       `json:"service_provided"`
	StatusCode      string       `json:"status_code"`
	PartnerTypeCode string       `json:"partner_type_code"`
	Address         string       `json:"address"`
	Floor           string       `json:"floor"`
	Unit            string       `json:"unit"`
	MapLat          float64      `json:"map_lat"`
	MapLong         float64      `json:"map_long"`
	PhoneNumber1    string       `json:"phone_number_1"`
	PhoneNumber2    string       `json:"phone_number_2"`
	Email           string       `json:"email"`
	Notes           string       `json:"notes"`
	TaxIdentity     string       `json:"tax_identity"`
	ContactList     []GetContact `json:"contact_list"`
}

func (p *GetPartner) Set(partner models.Partner) {
	p.ID = partner.ID
	p.CreatedBy = partner.CreatedBy
	p.CreatedAt = partner.CreatedAt
	p.UpdatedBy = partner.UpdatedBy
	p.Name = partner.Name
	p.ServiceProvided = partner.ServiceProvided
	p.StatusCode = partner.StatusCode
	p.Address = partner.Address
	p.Floor = partner.Floor.String
	p.Unit = partner.Unit.String
	p.MapLat = partner.MapLat
	p.MapLong = partner.MapLong
	p.PhoneNumber1 = partner.PhoneNumber1
	p.PhoneNumber2 = partner.PhoneNumber2.String
	p.Email = partner.Email
	p.Notes = partner.Notes
	p.PartnerTypeCode = partner.PartnerTypeCode
	p.TaxIdentity = partner.TaxIdentity
	contacts := []GetContact{}
	for _, val := range partner.Contacts {
		contactDTO := GetContact{}
		contactDTO.Set(val)
		contacts = append(contacts, contactDTO)
	}
	p.ContactList = contacts
}

type GetPartnerCustomerList struct {
	ID           string `json:"id"`
	PartnerNo    string `json:"partner_no"`
	Name         string `json:"name"`
	IsActive     bool   `json:"is_active"`
	PhoneNumber1 string `json:"phone_number_1"`
	PhoneNumber2 string `json:"phone_number_2"`
	Email        string `json:"email"`
	TaxIdentity  string `json:"tax_identity"`
	Address      string `json:"address"`
}

type GetPartnerCustomerPaymentTermList struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	IsActive bool   `json:"is_active"`
}

type PartnerCustomerListReq struct {
	commonmodel.ListRequest
}

type CreateUpdatePartnerCustomer struct {
	Name            string                        `json:"name" binding:"required"`
	Address         string                        `json:"address"`
	AddressCity     string                        `json:"address_city"`
	AddressCountry  string                        `json:"address_country"`
	AddressProvince string                        `json:"address_province"`
	AddressZipCode  string                        `json:"address_zip_code"`
	PhoneNumber1    string                        `json:"phone_number_1"`
	Email           string                        `json:"email"`
	TaxIdentity     string                        `json:"tax_identity"`
	TermName        string                        `json:"term_name"`
	ContactList     []CreateUpdateCustomerContact `json:"contact_list"`

	Photos []commonmodel.PhotoReq `json:"photos"`
}

type CreateUpdateCustomerContact struct {
	ID          string `json:"id"`
	Name        string `json:"name" binding:"required"`
	PhoneNumber string `json:"phone_number"`
	Email       string `json:"email"`
	Role        string `json:"role"`
	IsDeleted   bool   `json:"is_deleted"`
}

type GetPartnerCustomer struct {
	Name            string               `json:"name" binding:"required"`
	Address         string               `json:"address"`
	AddressCity     string               `json:"address_city"`
	AddressCountry  string               `json:"address_country"`
	AddressProvince string               `json:"address_province"`
	AddressZipCode  string               `json:"address_zip_code"`
	PhoneNumber1    string               `json:"phone_number_1"`
	Email           string               `json:"email"`
	TaxIdentity     string               `json:"tax_identity"`
	TermName        string               `json:"term_name"`
	ContactList     []GetCustomerContact `json:"contact_list"`
}

type GetCustomerContact struct {
	ID          string `json:"id"`
	Name        string `json:"name" binding:"required"`
	PhoneNumber string `json:"phone_number"`
	Email       string `json:"email"`
	Role        string `json:"role"`
}
