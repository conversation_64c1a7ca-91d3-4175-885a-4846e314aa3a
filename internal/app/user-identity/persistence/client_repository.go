package persistence

import (
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
)

func enrichClientQueryWithWhere(query *gorm.DB, where models.ClientWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	}

	if len(where.IDs) > 0 {
		query.Where("id IN ?", where.IDs)
	} // IDs
}

func enrichClientQueryWithPreload(query *gorm.DB, preload models.ClientPreload) {
	if preload.Status {
		query.Preload("Status")
	}
}

func (r *UserRepository) CreateClient(ctx context.Context, dB database.DBI, client *models.Client) error {
	return dB.GetTx().Create(client).Error
}

func (r *UserRepository) GetClient(ctx context.Context, dB database.DBI, condition models.ClientCondition) (*models.Client, error) {
	client := models.Client{}
	query := dB.GetOrm().Model(&client)

	enrichClientQueryWithWhere(query, condition.Where)

	enrichClientQueryWithPreload(query, condition.Preload)

	if len(condition.Columns) > 0 {
		query = query.Select(condition.Columns)
	}

	err := query.First(&client).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("client")
		}

		return nil, err
	}

	return &client, nil
}

func (r *UserRepository) GetClients(ctx context.Context, dB database.DBI, condition models.ClientCondition) ([]models.Client, error) {
	client := []models.Client{}
	query := dB.GetOrm().Model(&models.Client{})

	enrichClientQueryWithWhere(query, condition.Where)

	enrichClientQueryWithPreload(query, condition.Preload)

	if len(condition.Columns) > 0 {
		query = query.Select(condition.Columns)
	}

	err := query.Find(&client).Error
	if err != nil {
		return nil, err
	}

	return client, nil
}

func (r *UserRepository) UpdateClient(ctx context.Context, dB database.DBI, client *models.Client) error {
	return dB.GetTx().
		Model(&models.Client{}).
		Where("id = ?", client.ID).
		Updates(client).Error
}

func (r *UserRepository) UpdateUserClient(ctx context.Context, dB database.DBI, userClient *models.UserClient) error {
	return dB.GetTx().Updates(userClient).Error
}

func (r *UserRepository) GetDigispectPackages(ctx context.Context, dB database.DBI) ([]models.DigispectPackage, error) {
	digispectPackages := []models.DigispectPackage{}
	err := dB.GetTx().
		Model(&models.DigispectPackage{}).
		Order("rank ASC").
		Find(&digispectPackages).
		Error

	return digispectPackages, err
}

type clientRepository struct{}

func NewClientRepository() repository.ClientRepository {
	return &clientRepository{}
}

func (r *clientRepository) CreateLinkedClient(ctx context.Context, dB database.DBI, linkedClient *models.LinkedClient) error {
	return dB.GetTx().Create(linkedClient).Error
}

func (r *clientRepository) CreateUserClients(ctx context.Context, dB database.DBI, userClients []models.UserClient) error {
	return dB.GetTx().Create(userClients).Error
}

func (r *clientRepository) GetLinkedClient(ctx context.Context, dB database.DBI, parentID, childID string) (*models.LinkedClient, error) {
	linkedClient := &models.LinkedClient{}
	err := dB.GetTx().Model(linkedClient).
		Where("parent_id = ?", parentID).
		Where("child_id = ?", childID).
		First(linkedClient).
		Error
	if err != nil {
		return nil, err
	}

	return linkedClient, nil
}

func enrichLinkedClientWithPreload(query *gorm.DB, preload models.LinkedClientPreload) {
	if preload.Child {
		query.Preload("Child")
	}
}

func enrichLinkedClientWithWhere(query *gorm.DB, where models.LinkedClientWhere) {
	if where.ParentID != "" {
		query.Where("parent_id = ?", where.ParentID)
	}
}

func (r *clientRepository) GetLinkedClients(ctx context.Context, dB database.DBI, condition models.LinkedClientCondition) ([]models.LinkedClient, error) {
	linkedClients := []models.LinkedClient{}
	query := dB.GetTx().Model(&linkedClients)

	enrichLinkedClientWithWhere(query, condition.Where)
	enrichLinkedClientWithPreload(query, condition.Preload)

	query.Order("uis_linked_clients.created_at DESC")
	err := query.Find(&linkedClients).Error
	if err != nil {
		return nil, err
	}

	return linkedClients, nil
}
func (r *clientRepository) GetClientList(ctx context.Context, dB database.DBI, param models.GetClientListParam) (int, []models.Client, error) {
	var totalRecords int64
	clients := []models.Client{}
	query := dB.GetTx().Model(&clients)
	enrichClientQueryWithWhere(query, param.Cond.Where)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(client_alias) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(business_sector) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("uis_clients.created_at DESC")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Preload("Status").Find(&clients).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), clients, nil

}

func (r *clientRepository) GetClient(ctx context.Context, dB database.DBI, condition models.ClientCondition) (*models.Client, error) {
	client := models.Client{}
	query := dB.GetOrm().Model(&client)

	enrichClientQueryWithWhere(query, condition.Where)

	enrichClientQueryWithPreload(query, condition.Preload)

	if len(condition.Columns) > 0 {
		query = query.Select(condition.Columns)
	}

	err := query.First(&client).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("client")
		}

		return nil, err
	}

	return &client, nil
}

func (r *clientRepository) GetClients(ctx context.Context, dB database.DBI, condition models.ClientCondition) ([]models.Client, error) {
	clients := []models.Client{}
	query := dB.GetOrm().Model(&models.Client{})

	enrichClientQueryWithWhere(query, condition.Where)

	enrichClientQueryWithPreload(query, condition.Preload)

	if len(condition.Columns) > 0 {
		query = query.Select(condition.Columns)
	}

	err := query.Find(&clients).Error
	if err != nil {
		return nil, err
	}

	return clients, nil
}

func (r *clientRepository) GetPackageList(ctx context.Context, dB database.DBI, param models.GetPackageListParam) (int, []models.AsClientPackage, error) {
	var totalRecords int64
	packages := []models.AsClientPackage{}
	query := dB.GetTx().Model(&packages)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(code) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(label) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(description) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&packages).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), packages, nil
}

func (r *clientRepository) GetConfigList(ctx context.Context, dB database.DBI, param models.GetConfigListParam) (int, []models.ClientConfig, error) {
	var totalRecords int64
	configs := []models.ClientConfig{}
	query := dB.GetTx().Model(&configs)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(code) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(label) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(description) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&configs).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), configs, nil
}
