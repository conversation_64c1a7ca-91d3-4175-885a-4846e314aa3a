package persistence

import (
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AnalyticRepository struct{}

func NewAnalyticRepository() repository.AnalyticRepository {
	return &AnalyticRepository{}
}

func enrichAnalyticQueryWithWhere(query *gorm.DB, where models.AnalyticWhere) {
	if len(where.ExcludeAnalyticCode) > 0 {
		query.Where("code NOT IN ?", where.ExcludeAnalyticCode)
	} // ExcludeAnalyticCode
}

func enrichAnalyticDisplayConfigQueryWithPreload(query *gorm.DB, preload models.AnalyticDisplayConfigPreload) {
	if preload.Analytic {
		query.Preload("Analytic")
	} // Analytic
}

func (r *AnalyticRepository) UpsertAnalyticDisplayConfig(ctx context.Context, dB database.DBI, analyticDisCon *[]models.AnalyticDisplayConfig) error {
	return dB.GetTx().Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "analytic_code"},
			{Name: "user_id"},
			{Name: "client_id"},
		},
		DoUpdates: clause.AssignmentColumns([]string{
			"sequence",
			"is_active",
			"updated_at",
			"updated_by",
		}),
	}).Create(analyticDisCon).Error
}

func (r *AnalyticRepository) GetAnalyticDisplayConfigs(ctx context.Context, dB database.DBI, clientID string, userID string, preload models.AnalyticDisplayConfigPreload) ([]models.AnalyticDisplayConfig, error) {
	analyticDisplayConfigs := []models.AnalyticDisplayConfig{}
	query := dB.GetOrm().Model(&analyticDisplayConfigs).
		Where("client_id = ?", clientID).
		Where("user_id = ?", userID)

	enrichAnalyticDisplayConfigQueryWithPreload(query, preload)

	query.Order("sequence ASC")
	err := query.Find(&analyticDisplayConfigs).Error
	if err != nil {
		return nil, err
	}

	return analyticDisplayConfigs, nil
}

func (r *AnalyticRepository) GetAnalytics(ctx context.Context, dB database.DBI, where models.AnalyticWhere) ([]models.Analytic, error) {
	analytics := []models.Analytic{}
	query := dB.GetOrm().Model(&analytics)

	enrichAnalyticQueryWithWhere(query, where)

	query.Order("sequence ASC")
	err := query.Find(&analytics).Error
	if err != nil {
		return nil, err
	}

	return analytics, nil
}
