package repository

import (
	"assetfindr/internal/app/task/models"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"
	"time"
)

type TicketRepository interface {
	GetTickets(ctx context.Context, dB database.DBI, tickets *[]models.Ticket, pageSize int, pageNo int, searchKeyword string) (int, error)
	GetTicket(ctx context.Context, dB database.DBI, condition models.TicketCondition) (*models.Ticket, error)
	CountTicket(ctx context.Context, dB database.DBI, condition models.TicketCondition) (int, error)
	GetTicketsV2(ctx context.Context, dB database.DBI, condition models.TicketCondition) ([]models.Ticket, error)
	CreateTicket(ctx context.Context, dB database.DBI, ticket *models.Ticket) error
	CreateTickets(ctx context.Context, dB database.DBI, ticket []models.Ticket) error
	GetTicketByID(ctx context.Context, dB database.DBI, id string) (*models.Ticket, error)
	UpdateTicket(ctx context.Context, dB database.DBI, ticket *models.Ticket) error
	GetTicketList(ctx context.Context, dB database.DBI, req models.GetTicketListParam) (int, []models.Ticket, error)
	GetLatestTicketNumber(ctx context.Context, dB database.DBI, clientID string) (string, error)

	CreateNote(ctx context.Context, dB database.DBI, note *models.TicketNote) error
	CreateNotes(ctx context.Context, dB database.DBI, notes []models.TicketNote) error
	GetNotes(ctx context.Context, dB database.DBI, condition models.TicketNoteCondition) ([]models.TicketNote, error)

	IsTicketsAlreadyLinked(ctx context.Context, dB database.DBI, parentID, childID string) (bool, error)
	LinkedTicket(ctx context.Context, dB database.DBI, parentID, childID string) error
	DeleteLinkedTicket(ctx context.Context, dB database.DBI, id string) error
	GetLinkedTicket(ctx context.Context, dB database.DBI, condition models.LinkedTicketCondition) (*models.LinkedTicket, error)
	GetTicketLinkedList(ctx context.Context, dB database.DBI, req models.GetTicketLinkedListParam) (int, []models.Ticket, error)
	UpdateTicketSeverityLevelCode(ctx context.Context, dB database.DBI, id string, severityLevelCode string) error
	GetTicketSchedule(ctx context.Context, dB database.DBI, cond models.TicketCondition) ([]models.Ticket, error)
	UpdateTicketSchedule(ctx context.Context, dB database.DBI, id string, scheduleDatetime *time.Time) error

	UpsertTicketContact(ctx context.Context, dB database.DBI, ticketContact *models.TicketContact) error
	UpsertTicketContacts(ctx context.Context, dB database.DBI, ticketContacts []models.TicketContact) error
	GetTicketContacts(ctx context.Context, dB database.DBI, condition models.TicketContactCondition) ([]models.TicketContact, error)

	UpdateTicketPartnerOwnerName(ctx context.Context, dB database.DBI, partnerID string, newName string) error
	GetTicketStatus(ctx context.Context, dB database.DBI, condition models.TicketStatusCondition) (*models.TicketStatus, error)

	GetTicketCategoryList(ctx context.Context, dB database.DBI, req models.GetTicketCategoryListParam) (int, []models.TicketCategory, error)
	CreateTicketCategory(ctx context.Context, dB database.DBI, category *models.TicketCategory) error
	GetTicketCategory(ctx context.Context, dB database.DBI, condition models.TicketCategoryCondition) (*models.TicketCategory, error)
	InsertTicketCategoryAssetCategory(ctx context.Context, dB database.DBI, ticketCategoryAssetCategory *models.TicketCategoryAssetCategoryMapping) error
	InsertTicketCategoryCustomAssetCategory(ctx context.Context, dB database.DBI, ticketCategoryAssetCategory *models.TicketCategoryCustomAssetCategoryMapping) error
	UpdateTicketCategoryStatus(ctx context.Context, dB database.DBI, code string, statusCode string) error

	ChartCountTickets(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartCountOverdueTickets(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartTicketStatus(ctx context.Context, dB database.DBI, clientID string, where models.TicketWhere) ([]commonmodel.Chart, error)

	GetTask(ctx context.Context, dB database.DBI, condition models.TaskCondition) (*models.Task, error)
	GetTasks(ctx context.Context, dB database.DBI, condition models.TaskCondition) ([]models.Task, error)
	GetTaskList(ctx context.Context, dB database.DBI, req models.GetTaskListParam) (int, []models.Task, error)
	CreateTask(ctx context.Context, dB database.DBI, task *models.Task) error
	UpdateTask(ctx context.Context, dB database.DBI, id string, task *models.Task) error
	DeleteTask(ctx context.Context, dB database.DBI, id string) error
}
