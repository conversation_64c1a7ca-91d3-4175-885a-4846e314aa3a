package usecase

import (
	"assetfindr/internal/app/task/constants"
	"assetfindr/internal/app/task/dtos"
	"assetfindr/internal/app/task/models"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
)

func (uc *TicketUseCase) ChartTotalTickets(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.TicketRepository.ChartCountTickets(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *TicketUseCase) ChartTicketOverdue(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	workOrderGroup, _ := claim.GetPermissionCategory(constants.TICKET_PERMISSION_WORK_ORDER)
	isWOViewAll := uc.isWOViewAll(workOrderGroup)

	var nonTicketAdminCondition *models.NonTicketAdminCondition
	if !isWOViewAll {
		nonViewAllCondition, err := uc.GenerateTicketNonViewAllCondition(ctx, claim)
		if err != nil {
			return nil, err
		}

		nonTicketAdminCondition = nonViewAllCondition
	}

	charts, err := uc.TicketRepository.ChartCountOverdueTickets(ctx, uc.DB.DB(), claim.GetLoggedInClientID(), nonTicketAdminCondition)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *TicketUseCase) ChartTicketStatus(ctx context.Context, req dtos.ChartTicketStatusReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	var where models.TicketWhere
	if len(req.StatusCodes) > 0 {
		where.StatusCodes = req.StatusCodes
	}
	if !req.CreatedBeforeDate.IsZero() {
		where.CreatedBeforeDate = req.CreatedBeforeDate
	}

	charts, err := uc.TicketRepository.ChartTicketStatus(ctx, uc.DB.DB(), claim.GetLoggedInClientID(), where)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}
