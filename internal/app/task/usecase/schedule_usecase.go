package usecase

import (
	assetServiceModels "assetfindr/internal/app/asset/models"
	financeConstants "assetfindr/internal/app/finance/constants"
	financeDtos "assetfindr/internal/app/finance/dtos"
	"assetfindr/internal/app/task/constants"
	"assetfindr/internal/app/task/models"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/sqlhelpers"
	"context"
	"time"
)

func (uc *TicketUseCase) TicketScheduler(ctx context.Context, statusCode string) error {
	tickets, err := uc.TicketRepository.GetTicketSchedule(ctx, uc.DB.DB(), models.TicketCondition{
		Where: models.TicketWhere{
			StatusCodes: []string{statusCode},
		},
	})
	if err != nil {
		return err
	}
	if len(tickets) == 0 {
		commonlogger.Infof("There is no ticket to be close")
		return nil
	}
	numsOfErr := []string{}
	for _, val := range tickets {
		err = uc.CloseTicketSchedule(ctx, val.ID)
		if err != nil {
			numsOfErr = append(numsOfErr, val.ID)
			commonlogger.Warnf("Error in closing ticket on id %s", val.ID)

		}
	}
	commonlogger.Warnf("Number of error on closing ticket %v", len(numsOfErr))
	return nil
}

func (uc *TicketUseCase) CloseTicketSchedule(ctx context.Context, id string) error {
	ticket, err := uc.TicketRepository.GetTicketByID(ctx, uc.DB.DB(), id)
	if err != nil {
		return err
	}

	if ticket.StatusCode == constants.TICKET_STATUS_CODE_CLOSED {
		return nil
	}

	if ticket.StartTime != nil && ticket.StartTime.Valid {
		ticket.RunningTime += int(time.Since(ticket.StartTime.Time).Seconds())
	}

	updateTicket := &models.Ticket{
		StatusCode:  constants.TICKET_STATUS_CODE_CLOSED,
		RunningTime: ticket.RunningTime,
		StartTime:   &sqlhelpers.SqlNullTime{},
	}
	updateTicket.ID = id
	err = uc.TicketRepository.UpdateTicket(ctx, uc.DB.WithCtx(ctx).DB(), updateTicket)
	if err != nil {
		return err
	}

	journalReferences := []financeDtos.JournalReference{
		{
			ReferenceID: ticket.ReferenceID,
			// Currentlny always assume ticket came from asset
			SourceCode: financeConstants.JOURNAL_SOURCE_ASSET_CODE,
		},
		{
			ReferenceID: ticket.ID,
			SourceCode:  financeConstants.JOURNAL_SOURCE_WORK_ORDER_CODE,
		},
	}

	tread, err := uc.assetTyreRepo.GetAssetTyreTread(ctx, uc.DB.DB(), assetServiceModels.AssetTyreTreadCondition{
		Where: assetServiceModels.AssetTyreTreadWhere{
			AssetID:   ticket.ReferenceID,
			IsLastest: true,
		},
	})
	if err == nil {
		journalReferences = append(journalReferences, financeDtos.JournalReference{
			SourceCode:  financeConstants.JOURNAL_SOURCE_RETREAD_TYRE_CODE,
			ReferenceID: tread.ID,
		})
	}

	err = uc.financeUseCase.CreateJournal(ctx, financeDtos.CreateJournalReq{
		AccountTransactionType: financeConstants.ACCOUNT_TRANSACTION_TYPE_TICKET_RESOLUTION_COST,
		Date:                   updateTicket.UpdatedAt,
		References:             journalReferences,
		Notes:                  "Ticket Resolved",
		Amount:                 ticket.Cost.Int64,
		ClientID:               ticket.ClientID,
		UserID:                 ticket.AssignedToUserID.String,
	})
	if err != nil {
		return err
	}

	go uc.notifyAfterUpdateTicketStatus(contexthelpers.WithoutCancel(ctx), updateTicket, "-", constants.TICKET_STATUS_CODE_CLOSED)
	return nil
}
