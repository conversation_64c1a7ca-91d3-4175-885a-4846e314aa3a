package usecase

import (
	assetServiceModels "assetfindr/internal/app/asset/models"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	"assetfindr/internal/app/task/constants"
	"assetfindr/internal/app/task/dtos"
	"assetfindr/internal/app/task/models"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"context"
	"fmt"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

func (uc *TicketUseCase) GetTicketListV2(ctx context.Context, req dtos.TicketListV2Req) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	startDueDate, _ := parseScheduleDate(req.StartDueDate, false)
	endDueDate, _ := parseScheduleDate(req.EndDueDate, true)

	startScheduleDate, _ := parseScheduleDate(req.StartDate, false)
	endScheduleDate, _ := parseScheduleDate(req.EndDate, true)

	whereCondition := models.TicketWhere{
		ClientID:           claim.GetLoggedInClientID(),
		StatusCodes:        req.StatusCodes,
		StartDueDate:       startDueDate,
		EndDueDate:         endDueDate,
		Categories:         req.Categories,
		SeverityLevelCodes: req.SeverityLevelCodes,
		ReferenceIDs:       req.ReferenceIDs,
		DepartmentIDs:      req.DepartmentIDs,
		AssignedToUserIDs:  req.AssignedToUserIDs,
		CreatedByUserIDs:   req.CreatedByUserIDs,

		ReferenceID:              req.ReferenceID,
		StartDate:                startScheduleDate,
		EndDate:                  endScheduleDate,
		ExcludeCategories:        req.ExcludeCategories,
		HasCost:                  req.HasCost,
		IsArchived:               null.BoolFrom(req.IsArchived),
		IsOverdueServiceReminder: req.IsOverdueServiceReminder,
	}

	if req.PartnerOwnerID != "" {
		assets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), assetServiceModels.AssetCondition{
			Where: assetServiceModels.AssetWhere{
				PartnerOwnerID: req.PartnerOwnerID,
				ClientID:       claim.GetLoggedInClientID(),
			},
			Columns: []string{"id"},
		})
		if err != nil {
			return nil, err
		}

		assetIDs := make([]string, 0, len(assets))
		for i := range assets {
			assetIDs = append(assetIDs, assets[i].ID)
		}

		whereCondition.ReferenceIDs = append(whereCondition.ReferenceIDs, assetIDs...)
	}

	workOrderGroup, _ := claim.GetPermissionCategory(constants.TICKET_PERMISSION_WORK_ORDER)
	isWOViewAll := uc.isWOViewAll(workOrderGroup)

	if !isWOViewAll {
		nonViewAllCondition, err := uc.GenerateTicketNonViewAllCondition(ctx, claim)
		if err != nil {
			return nil, err
		}

		whereCondition.NonTicketAdminCondition = nonViewAllCondition
	}

	if req.IsDueSoon {
		whereCondition.DueSoon = true
		whereCondition.ExcludeStatusCodes = []string{
			constants.TICKET_STATUS_CODE_CANCELED, constants.TICKET_STATUS_CODE_CLOSED,
			constants.TICKET_STATUS_CODE_RESOLVED}
	} // IsDueSoon

	if req.IsOverDue {
		whereCondition.Overdue = true
		whereCondition.ExcludeStatusCodes = []string{
			constants.TICKET_STATUS_CODE_CANCELED, constants.TICKET_STATUS_CODE_CLOSED,
			constants.TICKET_STATUS_CODE_RESOLVED}
	} // IsOverDue

	if req.IsUpComingServiceReminder {
		whereCondition.StatusCodes = []string{constants.TICKET_STATUS_CODE_OPEN, constants.TICKET_STATUS_CODE_ASSIGNED}
		whereCondition.IsUpcomingServiceReminder = true
	} // IsUpComingServiceReminder

	if req.CreatedByMe {
		whereCondition.RequesterUserID = claim.UserID
	} // CreatedByMe

	if req.AssignedToMe {
		whereCondition.AssignedUserID = claim.UserID
	} // AssignedToMe

	if req.IsRelatedToMyAssets {
		assets, err := uc.assetAssignmentRepo.GetAssetAssignments(ctx, uc.DB.DB(),
			assetServiceModels.AssetAssignmentCondition{
				Where: assetServiceModels.AssetAssignmentWhere{
					UserID:   claim.UserID,
					Assigned: true,
				},
				Columns: []string{"asset_id"},
			})
		if err != nil {
			return nil, err
		}

		if len(assets) == 0 {
			return &commonmodel.ListResponse{
				TotalRecords: 0,
				PageSize:     req.PageSize,
				PageNo:       req.PageNo,
				Data:         nil,
			}, nil
		}

		assetIDs := []string{}
		for i := range assets {
			assetIDs = append(assetIDs, assets[i].AssetID)
		}

		whereCondition.ReferenceIDs = assetIDs

	} // IsRelatedToMyAssets

	if req.IsOpen {
		whereCondition.StatusCodes = []string{constants.TICKET_STATUS_CODE_OPEN}
	} // IsOpen

	if req.IsRelatedToMyDepartments {
		userClient, err := uc.UserRepository.GetUserClient(ctx, uc.DB.DB(),
			userIdentityModel.UserClientCondition{
				Where: userIdentityModel.UserClientWhere{
					UserID:   claim.UserID,
					ClientID: claim.GetLoggedInClientID(),
				},
			})
		if err != nil {
			return nil, err
		}

		whereCondition.DepartmentID = userClient.DepartmentID.String

	} // IsRelatedToMyDepartments

	if req.IsOverdueServiceReminder {
		whereCondition.Categories = []string{constants.TICKET_CATEGORY_CODE_CEK_BERKALA, constants.TICKET_CATEGORY_CODE_CEK_KOPLING, constants.TICKET_CATEGORY_CODE_CEK_REM, constants.TICKET_CATEGORY_CODE_CEK_TEKANAN_BAN}
		whereCondition.ExcludeStatusCodes = []string{constants.TICKET_STATUS_CODE_CANCELED, constants.TICKET_STATUS_CODE_CLOSED, constants.TICKET_STATUS_CODE_RESOLVED}
		whereCondition.IsOverdueServiceReminder = true
	} // IsOverdueServiceReminder

	count, tickets, err := uc.TicketRepository.GetTicketList(ctx, uc.DB.DB(), models.GetTicketListParam{
		ListRequest: req.ListRequest,
		Cond: models.TicketCondition{
			Where: whereCondition,
			Preload: models.TicketPreload{
				Parents:        true,
				Childs:         true,
				SeverityLevel:  true,
				TicketCategory: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	if len(tickets) == 0 {
		return &commonmodel.ListResponse{
			TotalRecords: count,
			PageSize:     req.PageSize,
			PageNo:       req.PageNo,
			Data:         nil,
		}, nil
	}

	// Get user by Ids
	var userIds []string

	// Get Asset By ids
	var assetIds []string

	var departmentIds []string

	for _, ticket := range tickets {
		if ticket.RequesterUserID != "" {
			userIds = append(userIds, ticket.RequesterUserID)
		}

		if ticket.AssignedToUserID.String != "" {
			userIds = append(userIds, ticket.AssignedToUserID.String)
		}

		if ticket.ReferenceID != "" {
			assetIds = append(assetIds, ticket.ReferenceID)
		}

		if ticket.DepartmentID != "" {
			departmentIds = append(departmentIds, ticket.DepartmentID)
		}
	}

	usersMapById := map[string]userIdentityModel.User{}

	err = uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return nil, err
	}

	assetsMapById := map[string]assetServiceModels.Asset{}

	if len(assetIds) > 0 {
		err = uc.AssetUseCase.GetAssetsMapByID(ctx, &assetsMapById, assetIds)
		if err != nil {
			commonlogger.Errorf("Error in getting users by user ids from identity service", err)
			return nil, err
		}
	}

	departments := []userIdentityModel.Department{}
	departmentsMapById := map[string]userIdentityModel.Department{}
	if len(departmentIds) > 0 {
		err = uc.departmentRepo.GetDepartmentsByIds(ctx, uc.DB.DB(), &departments, departmentIds)
		if err != nil {
			commonlogger.Errorf("Error in getting departments by department ids from identity service", err)
			return nil, err
		}
		for _, val := range departments {
			departmentsMapById[val.ID] = val
		}
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         dtos.BuildTksTicketResponse(tickets, usersMapById, assetsMapById, departmentsMapById),
	}, nil
}

func (uc *TicketUseCase) GetTicketCategoryList(ctx context.Context, req dtos.TicketCategoryList) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	statusCodes := req.StatusCodes
	if len(statusCodes) == 0 {
		statusCodes = append(statusCodes, constants.TICKET_CATEGORY_STATUS_ACTIVE, constants.TICKET_CATEGORY_STATUS_INACTIVE)
	}

	count, ticketCategories, err := uc.TicketRepository.GetTicketCategoryList(ctx, uc.DB.DB(),
		models.GetTicketCategoryListParam{
			ListRequest: req.ListRequest,
			Cond: models.TicketCategoryCondition{
				Where: models.TicketCategoryWhere{
					ClientIDOrGeneral:         claim.GetLoggedInClientID(),
					FilterAssetCategory:       req.AssetCategory,
					FilterCustomAssetCategory: req.CustomAssetCategory,
					StatusCodes:               statusCodes,
				},
				Preload: models.TicketCategoryPreload{
					TicketCategoryAssetCategoryMapping:       true,
					TicketCategoryCustomAssetCategoryMapping: true,
					AssetCategory:                            true,
					CustomAssetCategory:                      true,
				},
			},
		})
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         ticketCategories,
	}, nil
}

func (uc *TicketUseCase) GetTicketCategory(ctx context.Context, id string) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	ticketCategory, err := uc.TicketRepository.GetTicketCategory(ctx, uc.DB.DB(), models.TicketCategoryCondition{
		Where: models.TicketCategoryWhere{
			ClientID:         claim.GetLoggedInClientID(),
			TicketCategoryId: id,
		},
		Preload: models.TicketCategoryPreload{
			TicketCategoryAssetCategoryMapping:       true,
			TicketCategoryCustomAssetCategoryMapping: true,
			AssetCategory:                            true,
			CustomAssetCategory:                      true,
		},
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: 1,
		PageSize:     1,
		PageNo:       1,
		Data:         ticketCategory,
	}, nil
}

func (uc *TicketUseCase) CreateTicketV2(ctx context.Context, req dtos.CreateTicketReqV2) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	ticket := &models.Ticket{
		Subject:             req.Subject,
		Description:         req.Description,
		TicketCategoryCode:  req.TicketCategoryCode,
		TicketReferenceCode: req.TicketReferenceCode,
		ReferenceID:         req.ReferenceID,
		SeverityLevelCode:   req.SeverityLevelCode,
		RequesterUserID:     req.RequesterUserID,
		AssignedToUserID:    req.AssignedToUserID,
		StatusCode:          constants.TICKET_STATUS_CODE_OPEN,
		DepartmentID:        req.DepartmentID,
		LocationID:          req.LocationID,
	}

	if req.AssignedToUserID.String != "" {
		ticket.StatusCode = constants.TICKET_STATUS_CODE_ASSIGNED
	}

	if req.ScheduleDatetime.Valid {
		ticket.ScheduleDatetime = &req.ScheduleDatetime.Time
	}

	if req.ScheduleDatetimeEnd.Valid {
		ticket.ScheduleDatetimeEnd = &req.ScheduleDatetimeEnd.Time
	}

	if req.DueDatetime.Valid {
		ticket.DueDatetime = &req.DueDatetime.Time
	}

	// GET DEPARTMENT ID
	if req.AssignedToUserID.Valid && req.AssignedToUserID.String != "" {
		userClient, err := uc.UserRepository.GetUserClient(ctx, uc.DB.DB(), userIdentityModel.UserClientCondition{
			Where: userIdentityModel.UserClientWhere{
				UserID:   req.AssignedToUserID.String,
				ClientID: claim.GetLoggedInClientID(),
			},
		})
		if err != nil {
			return nil, err
		}

		ticket.DepartmentID = userClient.DepartmentID.String
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	dataInformation, err := uc.AssetRepository.GetAssetDataInformation(ctx, uc.DB.DB(), req.ReferenceID)
	if err != nil {
		return nil, err
	}

	ticket.AssetDataInformation = pgtype.JSONB{Bytes: dataInformation, Status: pgtype.Present}

	err = uc.TicketRepository.CreateTicket(ctx, tx.DB(), ticket)
	if err != nil {
		return nil, err
	}

	_, err = uc.attachmentUseCase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_TICKET,
		SourceReferenceID: ticket.ID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	go uc.notifyAfterCreateTicket(contexthelpers.WithoutCancel(ctx), ticket)

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: ticket.ID,
		Data:        nil,
	}, nil

}

func (uc *TicketUseCase) UpdateTicketV2(ctx context.Context, id string, req dtos.UpdateTicketReqV2) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.TicketRepository.GetTicket(ctx, uc.DB.DB(), models.TicketCondition{
		Where: models.TicketWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	ticket := &models.Ticket{
		Subject:            req.Subject,
		Description:        req.Description,
		TicketCategoryCode: req.TicketCategoryCode,
		RequesterUserID:    req.RequesterUserID,
	}

	if ticket.Description == "" {
		ticket.Description = "-"
	}

	if req.ScheduleDatetime.Valid {
		ticket.ScheduleDatetime = &req.ScheduleDatetime.Time
	}

	if req.ScheduleDatetimeEnd.Valid {
		ticket.ScheduleDatetimeEnd = &req.ScheduleDatetimeEnd.Time
	}

	if req.DueDatetime.Valid {
		ticket.DueDatetime = &req.DueDatetime.Time
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	ticket.ID = id
	err = uc.TicketRepository.UpdateTicket(ctx, tx.DB(), ticket)
	if err != nil {
		return nil, err
	}

	_, err = uc.attachmentUseCase.UpdateAttachmentPhotos(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_TICKET,
		SourceReferenceID: ticket.ID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}

	ticketNote := models.TicketNote{
		TicketID: id,
		UserID:   claim.UserID,
		Title:    fmt.Sprintf("%s has edited the work order.", claim.GetName()),
		Notes:    "",
		ClientID: claim.GetLoggedInClientID(),
	}

	err = uc.TicketRepository.CreateNote(ctx, tx.DB(), &ticketNote)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	go uc.notifyAfterUpdateTicket(contexthelpers.WithoutCancel(ctx), ticket)

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: ticket.ID,
		Data:        nil,
	}, nil
}

func (uc *TicketUseCase) UpdateTicketV2DueDate(ctx context.Context, id string, req dtos.UpdateTicketDueDateReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.TicketRepository.GetTicket(ctx, uc.DB.DB(), models.TicketCondition{
		Where: models.TicketWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	ticket := &models.Ticket{}
	if req.DueDatetime.Valid {
		ticket.DueDatetime = &req.DueDatetime.Time
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	ticket.ID = id
	err = uc.TicketRepository.UpdateTicket(ctx, tx.DB(), ticket)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	go uc.notifyAfterCreateTicket(contexthelpers.WithoutCancel(ctx), ticket)

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: ticket.ID,
		Data:        nil,
	}, nil
}
