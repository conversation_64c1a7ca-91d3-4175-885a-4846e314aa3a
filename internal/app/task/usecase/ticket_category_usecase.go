package usecase

import (
	"assetfindr/internal/app/task/constants"
	"assetfindr/internal/app/task/dtos"
	"assetfindr/internal/app/task/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
)

func (uc *TicketUseCase) CreateTicketCategory(ctx context.Context, req dtos.TicketCategoryReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	if constants.MapTicketCategoryStatusToLabel()[req.StatusCode] == "" {
		req.StatusCode = constants.TICKET_CATEGORY_STATUS_ACTIVE
	}

	ticketCategory := &models.TicketCategory{
		ModelCodePK: commonmodel.ModelCodePK{ClientID: claim.UserID},
		Label:       req.Label,
		Description: req.Description,
		StatusCode:  req.StatusCode,
	}
	err = uc.TicketRepository.CreateTicketCategory(ctx, uc.DB.WithCtx(ctx).DB(), ticketCategory)
	if err != nil {
		return nil, err
	}

	for i := range req.AssetCategories {
		err := uc.TicketRepository.InsertTicketCategoryAssetCategory(ctx, uc.DB.DB(), &models.TicketCategoryAssetCategoryMapping{
			CategoryCode:      ticketCategory.Code,
			AssetCategoryCode: req.AssetCategories[i],
		})
		if err != nil {
			return nil, err
		}
	}

	for i := range req.CustomAssetCategories {
		err := uc.TicketRepository.InsertTicketCategoryCustomAssetCategory(ctx, uc.DB.DB(), &models.TicketCategoryCustomAssetCategoryMapping{
			CategoryCode:          ticketCategory.Code,
			CustomAssetCategoryId: req.CustomAssetCategories[i],
		})
		if err != nil {
			return nil, err
		}
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Succes",
		ReferenceID: ticketCategory.Code,
		Data:        nil,
	}, nil
}

func (uc *TicketUseCase) UpdateStatusTicketCategory(ctx context.Context, req dtos.UpdateStatusTicketCategoryReq, id string) (*commonmodel.UpdateResponse, error) {

	status := constants.MapTicketCategoryStatusToLabel()[req.StatusCode] // checking req.StatusCode
	if status == "" {
		return nil, errorhandler.ErrBadRequest("invalid status code")
	}

	err := uc.TicketRepository.UpdateTicketCategoryStatus(ctx, uc.DB.DB(), id, req.StatusCode)
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Succes",
		ReferenceID: id,
		Data:        nil,
	}, nil
}
