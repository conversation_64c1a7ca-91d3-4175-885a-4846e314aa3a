package persistence

import (
	"assetfindr/internal/app/task/models"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"

	"gopkg.in/guregu/null.v4"
)

func (r *TicketRepository) ChartCountTickets(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Ticket{})

	enrichTicketQueryWithWhere(query, models.TicketWhere{
		ClientID: clientID,
	})

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}

	return []commonmodel.Chart{
		{
			Y:    float64(total),
			Name: "Total Tickets",
		},
	}, nil
}

func (r *TicketRepository) ChartCountOverdueTickets(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Ticket{})

	enrichTicketQueryWithWhere(query, models.TicketWhere{
		ClientID: clientID,
		Overdue:  true,
	})

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}

	return []commonmodel.Chart{
		{
			Y:    float64(total),
			Name: "Overdue Work Orders",
			Code: null.StringFrom("OVERDUE_WORK_ORDERS"),
		},
	}, nil
}

func (r *TicketRepository) ChartTicketStatus(ctx context.Context, dB database.DBI, clientID string, where models.TicketWhere) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Ticket{})

	where.ClientID = clientID
	enrichTicketQueryWithWhere(query, where)

	query.Joins(`JOIN "tks_TICKET_STATUSES" 
		   ON "tks_TICKET_STATUSES".code = tks_tickets.status_code`)

	query.Group(`tks_tickets.status_code, "tks_TICKET_STATUSES".label`)

	query.Select(
		"count(tks_tickets.id) AS y",
		"tks_tickets.status_code AS code",
		`"tks_TICKET_STATUSES".label AS name`,
	)

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}
