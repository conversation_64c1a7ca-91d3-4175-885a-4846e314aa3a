package bq

import (
	"assetfindr/internal/constants"
	"assetfindr/pkg/common/commonlogger"
	"context"
	"strconv"

	"cloud.google.com/go/bigquery"
	"github.com/spf13/viper"
	"google.golang.org/api/option"
)

var (
	BQ  *bigquery.Client
	err error
)

func BQConnection(masterBQProjectID string) error {

	var bq = BQ
	logMode := viper.GetBool(constants.CONFIG_DB_MASTER_LOG_MODE)
	debug := viper.GetBool(constants.CONFIG_DB_MASTER_DEBUG)

	commonlogger.Infof("bigquery log mod is " + strconv.FormatBool(logMode))

	opt := option.WithCredentialsFile("gcloud-service-key.json")
	ctx := context.Background()
	bq, err = bigquery.NewClient(ctx, masterBQProjectID, opt)
	commonlogger.Infof("Bigquery debug mod is " + strconv.FormatBool(debug))
	if err != nil {
		commonlogger.Fatalf("Bigquery connection error", err)
		return err
	} else {
		commonlogger.Infof("Connected to bigquery!")
	}

	BQ = bq
	return nil
}

func GetBQ() *bigquery.Client {
	return BQ
}
