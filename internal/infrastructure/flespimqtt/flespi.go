package flespimqtt

import (
	"assetfindr/pkg/common/commonlogger"
	"os"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

var connectLostHandler mqtt.ConnectionLostHandler = func(client mqtt.Client, err error) {
	commonlogger.Warnf("Connect lost: %v", err)
}

func ConnectToFlespiMqtt(connectHandler mqtt.OnConnectHandler) mqtt.Client {
	broker := "tcp://mqtt.flespi.io:1883"
	token := os.Getenv("FLESPI_TOKEN")
	opts := mqtt.NewClientOptions().AddBroker(broker)
	opts.AutoReconnect = true
	opts.SetUsername("FlespiToken " + token)
	opts.OnConnect = connectHandler
	opts.OnConnectionLost = connectLostHandler

	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}

	return client
}

func ConnectToHivemqMqtt(connectHandler mqtt.OnConnectHandler) mqtt.Client {
	broker := os.Getenv("HIVEMQ_BROKER")
	username := os.Getenv("HIVEMQ_USERNAME")
	password := os.Getenv("HIVEMQ_PASSWORD")

	opts := mqtt.NewClientOptions().AddBroker(broker)
	opts.AutoReconnect = true
	opts.SetUsername(username)
	opts.SetPassword(password)
	opts.OnConnect = connectHandler
	opts.OnConnectionLost = connectLostHandler

	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}

	return client
}
