package errorhandler

import (
	"fmt"
	"net/http"
)

func ParseCredentialErrorFromHttpResponseCode(url string, statusCode int) error {
	if statusCode == http.StatusOK {
		return nil
	}

	if statusCode >= http.StatusInternalServerError {
		return ErrInternalServerError(fmt.Sprintf("get error status >= 500 from %s, status: %d", url, statusCode))
	}

	if statusCode >= http.StatusBadRequest {
		return ErrBadRequest(fmt.Sprintf("get error status >= 400 from %s, status: %d, your credential may not valid", url, statusCode))
	}

	return nil
}
